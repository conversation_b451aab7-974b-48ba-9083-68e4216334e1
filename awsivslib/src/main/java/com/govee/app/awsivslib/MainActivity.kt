package com.govee.app.awsivslib

import android.net.Uri
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log
import com.amazonaws.ivs.player.*

class MainActivity : AppCompatActivity() {

    private val playerView by lazy { findViewById<PlayerView>(R.id.playerView) }
    private val player by lazy { playerView.player }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        init()
    }


    private fun init() {
        player.addListener(listener)
        player.load(Uri.parse(URL))
    }

    private val listener = object : Player.Listener() {
        override fun onCue(p0: Cue) {
            Log.i(TAG, "onCue: ")
        }

        override fun onDurationChanged(p0: Long) {
            Log.i(TAG, "onDurationChanged: p0 = $p0")
        }

        override fun onStateChanged(p0: Player.State) {
            Log.i(TAG, "onStateChanged: state = $p0")
            when (p0) {
                Player.State.BUFFERING -> {
                    Log.i(TAG, "onStateChanged: player is buffering")
                }
                Player.State.PLAYING -> {
                    Log.i(TAG, "onStateChanged:  playback started")
                }
                Player.State.READY -> {
                    player.play()
                }
                Player.State.IDLE -> {}
                Player.State.ENDED -> {}
            }
        }

        override fun onError(p0: PlayerException) {
            Log.i(TAG, "onError: $p0")
        }

        override fun onRebuffering() {
            Log.i(TAG, "onRebuffering: ")
        }

        override fun onSeekCompleted(p0: Long) {
            Log.i(TAG, "onSeekCompleted: $p0")
        }

        override fun onVideoSizeChanged(p0: Int, p1: Int) {
            Log.i(TAG, "onVideoSizeChanged: p0 = $p0, p1 = $p1")
        }

        override fun onQualityChanged(p0: Quality) {
            Log.i(TAG, "onQualityChanged: quality = $p0")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        player.removeListener(listener)
        player.release()
    }

    companion object {
        private const val TAG = "MainActivity"
        private const val URL = "https://b37bd1536301.us-east-1.playback.live-video.net/api/video/v1/us-east-1.920700710667.channel.eapnsU21ExSe.m3u8"
    }
}
