# Widget 广播功能测试指南

## 测试环境准备

1. 编译并安装应用到设备
2. 打开 Android Studio 的 Logcat
3. 设置过滤器：`tag:CubeDemoWidget OR tag:WidgetInternalReceiver OR tag:WidgetTargetActivity`

## 测试步骤

### 1. 添加 Widget 到桌面
1. 长按桌面空白处
2. 选择"小组件"或"Widget"
3. 找到 "Cube Demo" 小组件
4. 拖拽到桌面

### 2. 测试点击功能
1. 点击桌面上的 Widget
2. 观察以下现象：
   - **Toast 提示**：显示"Widget 内部广播已接收"
   - **Logcat 日志**：查看详细的执行流程
   - **目标页面**：自动启动并显示统计信息

### 3. 验证广播流程
在 Logcat 中应该看到以下日志序列：

```
D/CubeDemoWidget: Widget clicked, ID: [widget_id]
D/CubeDemoWidget: Handling widget click for ID: [widget_id]
D/CubeDemoWidget: Sending internal broadcast
D/WidgetInternalReceiver: Received internal broadcast from widget ID: [widget_id], timestamp: [timestamp]
I/WidgetInternalReceiver: Widget click logged - ID: [widget_id], Time: [date]
D/WidgetInternalReceiver: Performing business logic for widget: [widget_id]
D/WidgetInternalReceiver: Widget [widget_id] click count updated to: [count]
D/CubeDemoWidget: Starting target activity
D/WidgetTargetActivity: Target activity started from widget ID: [widget_id]
```

### 4. 验证统计功能
1. 多次点击 Widget
2. 每次打开的目标页面应显示递增的点击次数
3. 不同的 Widget 实例有独立的计数

## 手动测试广播

### 使用 ADB 命令测试内部广播
```bash
# 发送内部广播
adb shell am broadcast \
  -a com.govee.app.cubedemo.test.widget.INTERNAL_BROADCAST \
  -n com.govee.app.cubedemo/.test.widget.WidgetInternalReceiver \
  --ei widget_id 999 \
  --el timestamp $(date +%s)000
```

### 使用 ADB 命令测试 Widget 点击广播
```bash
# 发送 Widget 点击广播
adb shell am broadcast \
  -a com.govee.app.cubedemo.test.widget.WIDGET_CLICKED \
  -n com.govee.app.cubedemo/.test.widget.CubeDemoWidgetProvider \
  --ei widget_id 999
```

## 预期结果

### 正常流程
1. ✅ Widget 点击触发广播
2. ✅ 内部广播被正确接收和处理
3. ✅ Toast 提示显示
4. ✅ 统计数据正确更新
5. ✅ 目标页面正确启动
6. ✅ 页面显示正确的统计信息

### 异常处理
- 如果广播接收失败，检查 AndroidManifest.xml 注册
- 如果页面启动失败，检查 Activity 导出设置
- 如果统计不准确，检查 SharedPreferences 权限

## 性能考虑

- 内部广播处理应该快速完成（< 100ms）
- 避免在广播接收器中执行耗时操作
- 统计数据使用 SharedPreferences 本地存储
- 页面启动有 100ms 延迟，确保广播处理完成
