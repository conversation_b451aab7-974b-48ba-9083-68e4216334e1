package com.govee.app.cubedemo.test;

import androidx.annotation.StringRes;

/**
 * author  : sinrow
 * time    : 2024/10/28
 * version : 1.0.0
 * desc    :
 */
public class SubMusicMode {
    public static final int music_type_old_single = 0;
    public static final int music_type_new_multi = 1;
    public int musicType;
    private boolean editable;/*是否可编辑*/
    public int musicCode;
    /*defRes-selectedRes*/
    public int[] iconRes;
    @StringRes
    public int labelRes;

    /*额外参数*/
    public int fenliPointNum = 5;//分离点数量
    public int spiltPoint = -1;/*分离默认选中第几个 从1开始*/
    public boolean cuicanShowSubMode = true;//璀璨下是展示子模式
    public int[] colorSizeRange;//颜色数量范围 length = 2
    public boolean isCustomStr = false;
    public String customStr = "";

}
