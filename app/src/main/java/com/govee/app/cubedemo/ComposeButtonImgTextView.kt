package com.govee.app.cubedemo

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/11/30
 *     version : 1.0.0
 *     desc    : 该 view 处理
 */
class ComposeButtonImgTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : View(context, attrs) {
    private val paint by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }

    private var textContent: String = ""
    private var textSize: Float = 0f
    private var textColorNormal: Int = Color.BLACK
    private var textColorSelected: Int = Color.WHITE
    private var bgNormal: Int = Color.WHITE
    private var bgSelected: Int = Color.WHITE
    private val textColor: Int
        get() {
            return if (isSelected) textColorSelected else textColorNormal
        }

    init {
        val obtainStyledAttributes =
            context.obtainStyledAttributes(attrs, R.styleable.ComposeButtonImgTextView)
        obtainStyledAttributes.let {
            it.getString(R.styleable.ComposeButtonImgTextView_text)?.run { textContent = this }
            textSize = it.getDimension(R.styleable.ComposeButtonImgTextView_textSize, textSize)
            textColorNormal =
                it.getInt(R.styleable.ComposeButtonImgTextView_textColorNormal, textColorNormal)
            textColorSelected =
                it.getInt(R.styleable.ComposeButtonImgTextView_textColorSelected, textColorSelected)

        }
        obtainStyledAttributes.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.let {
            drawBackground()
            drawText(it)
        }
    }

    private fun drawBackground() {
        val bgRes = if (isSelected) bgSelected else bgNormal
        setBackgroundColor(bgRes)
    }

    private fun drawText(canvas: Canvas) {
        paint.color = textColor
        val fontMetrics = paint.fontMetrics
        val top = fontMetrics.top//为基线到字体上边框的距离,即上图中的top
        val bottom = fontMetrics.bottom//为基线到字体下边框的距离,即上图中的bottom
        paint.textSize = textSize
        val baseLineY = (height / 2).toFloat() - top / 2 - bottom / 2//基线中间点的y轴计算公式
        canvas.drawText(textContent, (width / 2).toFloat(), baseLineY, paint)
    }

    private fun drawImgView() {

    }

}