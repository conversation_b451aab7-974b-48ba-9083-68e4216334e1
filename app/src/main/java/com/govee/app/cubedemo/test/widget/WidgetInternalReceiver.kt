package com.govee.app.cubedemo.test.widget

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import com.govee.app.cubedemo.test.widget.CubeDemoWidgetProvider.Companion.EXTRA_WIDGET_ID

/**
 * Widget 内部广播接收器
 * 用于处理 Widget 点击时发起的内部广播
 */
class WidgetInternalReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "WidgetInternalReceiver"
        const val ACTION_WIDGET_INTERNAL_BROADCAST = "com.govee.app.cubedemo.test.widget.INTERNAL_BROADCAST"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_WIDGET_INTERNAL_BROADCAST -> {
                handleInternalBroadcast(context, intent)
            }
        }
    }

    /**
     * 处理内部广播
     */
    private fun handleInternalBroadcast(context: Context, intent: Intent) {
        val widgetId = intent.getIntExtra(CubeDemoWidgetProvider.EXTRA_WIDGET_ID, -1)
        val timestamp = intent.getLongExtra("timestamp", 0)
        
        Log.d(TAG, "Received internal broadcast from widget ID: $widgetId, timestamp: $timestamp")
        
        // 在这里可以执行任何需要的内部逻辑
        // 例如：记录统计、更新数据、发送分析事件等
        
        // 示例：显示一个短暂的 Toast（可选）
        Toast.makeText(context, "Widget 内部广播已接收", Toast.LENGTH_SHORT).show()
        
        // 示例：记录日志
        logWidgetClick(widgetId, timestamp)
        
        // 示例：可以在这里触发其他业务逻辑
        performBusinessLogic(context, widgetId)
    }

    /**
     * 记录 Widget 点击日志
     */
    private fun logWidgetClick(widgetId: Int, timestamp: Long) {
        Log.i(TAG, "Widget click logged - ID: $widgetId, Time: ${java.util.Date(timestamp)}")
        
        // 这里可以添加更复杂的日志记录逻辑
        // 例如：保存到数据库、发送到分析服务等
    }

    /**
     * 执行业务逻辑
     */
    private fun performBusinessLogic(context: Context, widgetId: Int) {
        Log.d(TAG, "Performing business logic for widget: $widgetId")
        
        // 在这里可以添加任何需要的业务逻辑
        // 例如：
        // - 更新应用状态
        // - 发送网络请求
        // - 更新本地数据
        // - 触发其他组件
        
        // 示例：可以通过 SharedPreferences 记录点击次数
        val prefs = context.getSharedPreferences("widget_prefs", Context.MODE_PRIVATE)
        val clickCount = prefs.getInt("click_count_$widgetId", 0)
        prefs.edit().putInt("click_count_$widgetId", clickCount + 1).apply()
        
        Log.d(TAG, "Widget $widgetId click count updated to: ${clickCount + 1}")

        // 2. 延迟启动目标页面（给广播处理一些时间）
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            val activityIntent = Intent(context, WidgetTargetActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra(EXTRA_WIDGET_ID, widgetId)
            }

            Log.d(TAG, "Starting target activity")
            context.startActivity(activityIntent)
        }, 100) // 延迟 100ms
    }
}
