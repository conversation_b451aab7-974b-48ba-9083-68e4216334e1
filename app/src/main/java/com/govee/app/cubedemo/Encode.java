package com.govee.app.cubedemo;

import android.util.Base64;

import androidx.annotation.Nullable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018.9.11
 * 加解密
 */
public class Encode {
    public static String encryptByBase64(String clearText) {
        String value = "";
        try {
            value = Base64.encodeToString(clearText.getBytes(), Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static String decryptByBase64(String encrypted) {
        String value = "";
        if (encrypted == null) {
            return value;
        }
        try {
            byte[] decode = Base64.decode(encrypted, Base64.DEFAULT);
            value = new String(decode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static String encryptByBase64(byte[] bytes) {
        String value = "";
        try {
            value = Base64.encodeToString(bytes, Base64.NO_WRAP);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static String encryptByBase64UrlSafe(byte[] bytes) {
        String value = "";
        try {
            value = Base64.encodeToString(bytes, Base64.URL_SAFE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    @Nullable
    public static byte[] decryByBase64(String encrypted) {
        try {
            return Base64.decode(encrypted, Base64.NO_WRAP);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
