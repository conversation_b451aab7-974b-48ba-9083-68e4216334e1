package com.govee.app.cubedemo

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/11/30
 *     version : 1.0.0
 *     desc    :
 */

class CubeAreaTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AppCompatTextView(context, attrs) {

    private var areaColor: Int = Color.WHITE
    private val gradientDrawable = GradientDrawable()


    init {
        context.obtainStyledAttributes(attrs, R.styleable.CubeAreaTextView).let {
            areaColor = it.getInt(R.styleable.CubeAreaTextView_area_color, areaColor)
            it.recycle()
        }
    }

    override fun setSelected(selected: <PERSON><PERSON><PERSON>) {
        super.setSelected(selected)
        // 把背景变成
        drawBg()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
    }

    private fun drawBg() {
        if (isSelected) {
            setBackgroundColor(areaColor)
        } else {
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.setColor(areaColor)
            gradientDrawable.cornerRadius = 10f
            gradientDrawable.setStroke(4, Color.BLACK)
            background = gradientDrawable
        }
    }

    fun getAreaColor(): Int {
        return areaColor
    }
}