package com.govee.app.cubedemo.test

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View

/**
 *     author  : sinrow
 *     time    : 2024/10/16
 *     version : 1.0.0
 *     desc    :
 */
class H6020ColorRangeView : View {
    private val color1: Int = -0xcbcc //红色

    private val color2: Int = -0xfe3400 //绿色

    private val color3: Int = -0xffff01 //蓝色

    private val color4: Int = -0x1400 //黄色

    private val color5: Int = -0x4bff01 //紫色

    private val colors: IntArray = intArrayOf(color1, color2, color3, color4, color5)
    private var paint = Paint()
    private var bitmap: Bitmap? = null
    private var isOpenBase = true
    private var isOpenBody = true
    private var bodyColor = -0x1400


    constructor(context: Context?) : super(context) {
        initParams()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        initParams()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initParams()
    }

    private fun initParams() {

    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawArc(canvas)
    }

    private fun drawArc(canvas: Canvas) {
        /*灯体*/
        if (isOpenBody) {
            val rectF = RectF(0f, 0f, inReal(242f), inReal(101f))
            paint.reset()
            paint.color = bodyColor
            canvas.drawRect(rectF, paint)
        }

        /*底座*/
        if (isOpenBase) {
            drawArcRing(canvas, 312, 85, colors[0])// 紫色
            drawArcRing(canvas, 37, 53, colors[4])// 黄色
            drawArcRing(canvas, 90, 53, colors[3])// 蓝色
            drawArcRing(canvas, 143, 85, colors[2])// 绿色
            drawArcRing(canvas, 228, 84, colors[1])//红色
        }


        if (bitmap != null) {
            val rectF = RectF(0f, 0f, inReal(242f), inReal(180f))
            canvas.drawBitmap(bitmap!!, null, rectF, null)
        }

    }

    private fun drawArcRing(canvas: Canvas, startAngle: Int, angle: Int, color: Int) {
        paint.reset()
        //以椭圆的外接圆画圆弧
        paint.isAntiAlias = true
        paint.color = color
        canvas.drawArc(getOutsideCircleRectF(), startAngle.toFloat(), angle.toFloat(), true, paint)
    }

    /**
     * 椭圆的外界圆的矩形
     */
    private fun getOutsideCircleRectF(): RectF {
        // 181 55 242
        // 242 180
        // 左上角:(242-181)/2 , (180-55)
        // y:180 - 125/2 = 62.5 = 117.5 - 100.5 = 17 +117.5 中心点
        // x:30.5+181/2 = 90.5+30.5 = 121
        return RectF(inReal(0f), inReal(100.5f), inReal(242f), inReal(180f))
    }

    /**
     * 算出实际画在手机上的坐标或大小
     */
    private fun inReal(f: Float): Float {
        return f * widthPixels / 375
    }

    fun setBgImage(bitmap: Bitmap) {
        this.bitmap = bitmap
        invalidate()
    }

    var widthPixels: Int = 0


}