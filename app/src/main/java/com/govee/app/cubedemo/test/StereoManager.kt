package com.govee.app.cubedemo.test

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.appcompat.app.AppCompatActivity

/**
 *     author  : sinrow
 *     time    : 2025/2/19
 *     version : 1.0.0
 *     desc    :
 */
object StereoManager {

    private val activityMap = HashMap<String, AppCompatActivity>()
    private var curActivityTag: String = ""
    private val tagList = listOf("1111", "22222", "33333")
    private var count = 0


    fun addActivity(device: String, activity: AppCompatActivity) {
        activityMap[device] = activity
    }

    fun removeAc(device: String) {
        activityMap.remove(device)
    }

    fun firstActivityExtTag(device: String): String {
        if (activityMap.containsKey(device)) {
            return activityMap.keys.firstOrNull { it != curActivityTag } ?: ""
        }
        return device
    }

    fun startActivityForDevice(context: Context, device: String) {
        var tag = tagList.getOrNull(count)
        if (tag == null) {
            count = 0
        }
        tag = tagList.getOrNull(count)
        count++
        Log.d("-------", "onResume: 跳转页面 = $tag")
        val intent = Intent(context, TestAc::class.java).apply {
            // 通过 data URI 标识不同实例
            data = Uri.parse("dynamic://tag/$tag")
            action = "ACTION_$tag"
            putExtra("TAG_ID", tag)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        }
        context.startActivity(intent)

//        activityMap.get(device)?.let {
//            it.moveTaskToBack(true)
//        } ?: run {
//
//        }
//        curActivityTag = device
    }

}