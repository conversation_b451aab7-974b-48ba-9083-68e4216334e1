package com.govee.app.cubedemo.test

import android.text.TextUtils
import androidx.annotation.Keep
import com.google.gson.Gson
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

/**
 *     author  : sinrow
 *     time    : 2024/10/21
 *     version : 1.0.0
 *     desc    :
 */
data class CommBean(
    val config: String
) {

    private var temp: Any? = null

    fun <T> getConfig(t: Class<T>): T? {
        if (temp == null) {
            val fromJson = fromJson(config, t)
            if (fromJson != null) {
                temp = fromJson
                return temp as T
            } else {
                return null
            }
        }
        return temp as T
    }
}

fun <T> fromJson(json: String, t: Class<T>?): T? {
    if (TextUtils.isEmpty(json)) return null
    return try {
        Gson().fromJson(json, t)
    } catch (e: Exception) {
        null
    }
}


@Keep
class SpeedInfoType4Config {
    var speed: SpeedInfoType4? = null
    var direction: DirectionInfoType4? = null
}

@Keep
class DirectionInfoType4 {
    var defIndex = 0
    var directions: IntArray? = null
    var layers: IntArray? = null
}

@Keep
class SpeedInfoType4 {
    var defSpeed: Int = 0
    var speedRange: IntArray? = null

}

// 泛型解析函数，将 JSON 字符串解析为 Map
fun parseJsonToMap(jsonString: String): Map<String, Any> {
    val jsonElement = Json.decodeFromString<JsonObject>(jsonString)
    return jsonElement.toMap()
}

// 扩展函数将 JsonObject 转换为 Map
fun JsonObject.toMap(): Map<String, Any> {
    return this.mapValues { (_, value) ->
        when {
            value is JsonPrimitive -> value.content // 处理基本类型
            value is JsonObject -> value.toMap() // 递归处理嵌套对象
            else -> value // 处理其他类型，如数组
        }
    }
}