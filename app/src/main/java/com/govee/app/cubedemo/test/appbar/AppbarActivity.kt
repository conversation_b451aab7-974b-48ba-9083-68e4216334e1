package com.govee.app.cubedemo.test.appbar

import android.animation.ValueAnimator
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.google.android.material.appbar.AppBarLayout
import com.govee.app.cubedemo.R

/**
 *     author  : sinrow
 *     time    : 2025/5/9
 *     version : 1.0.0
 *     desc    : AppBarLayout 折叠动画示例
 */
class AppbarActivity : AppCompatActivity() {

    companion object {
        private const val ITEM_WIDTH_DP = 185
        private const val ITEM_HEIGHT_EXPANDED_DP = 75
        private const val ITEM_HEIGHT_COLLAPSED_DP = 40
        private const val ANIMATION_DURATION = 200L
        private const val ITEM_MARGIN_DP = 8
    }

    private lateinit var recycleCircle: RecyclerView
    private lateinit var appBarLayout: AppBarLayout
    private var currentProgress: Float = 0f
    private var isAnimating = false
    private var heightAnimator: ValueAnimator? = null
    private var lastHeight: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_appbar)

        appBarLayout = findViewById(R.id.appBarLayout)
        recycleCircle = findViewById(R.id.recycleCircle)

        // 初始化 RecyclerView（模拟横向列表）
        recycleCircle.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        recycleCircle.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val margin = TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP,
                    ITEM_MARGIN_DP.toFloat(),
                    resources.displayMetrics
                ).toInt()
                outRect.set(margin, margin, margin, margin)
            }
        })
        recycleCircle.adapter = SimpleAdapter()

        // 监听 AppBarLayout 折叠进度
        appBarLayout.addOnOffsetChangedListener { _, verticalOffset ->
            if (isAnimating) return@addOnOffsetChangedListener

            val totalScrollRange = appBarLayout.totalScrollRange
            val newProgress = (-verticalOffset).toFloat() / totalScrollRange  // 0 - 1

            if (newProgress != currentProgress) {
                animateItemsHeight(newProgress)
            }
        }
    }

    private fun animateItemsHeight(targetProgress: Float) {
        heightAnimator?.cancel()

        heightAnimator = ValueAnimator.ofFloat(currentProgress, targetProgress).apply {
            duration = ANIMATION_DURATION
            interpolator = AccelerateDecelerateInterpolator()

            addUpdateListener { animation ->
                currentProgress = animation.animatedValue as Float
                updateVisibleItemsHeight()
            }

            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: android.animation.Animator) {
                    isAnimating = true
                }

                override fun onAnimationEnd(animation: android.animation.Animator) {
                    isAnimating = false
                }
            })

            start()
        }
    }

    private fun updateVisibleItemsHeight() {
        val newHeight = getCurrentItemHeight()
        if (newHeight != lastHeight) {
            lastHeight = newHeight
            for (i in 0 until recycleCircle.childCount) {
                val child = recycleCircle.getChildAt(i)
                val holder = recycleCircle.getChildViewHolder(child)
                if (holder is SimpleAdapter.ViewHolder) {
                    holder.updateHeight(newHeight, currentProgress)
                }
            }
        }
    }

    private fun getCurrentItemHeight(): Int {
        val expandedHeightPx = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            ITEM_HEIGHT_EXPANDED_DP.toFloat(),
            resources.displayMetrics
        ).toInt()

        val collapsedHeightPx = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            ITEM_HEIGHT_COLLAPSED_DP.toFloat(),
            resources.displayMetrics
        ).toInt()

        return (expandedHeightPx * (1 - currentProgress) + collapsedHeightPx * currentProgress).toInt()
    }

    override fun onDestroy() {
        super.onDestroy()
        heightAnimator?.cancel()
        heightAnimator = null
    }

    // 简单 RecyclerView Adapter 示例
    inner class SimpleAdapter : RecyclerView.Adapter<SimpleAdapter.ViewHolder>() {

        private val items = List(20) { "Item $it" }
        private val colors = listOf(
            android.graphics.Color.parseColor("#FF6200EE"), // 紫色
            android.graphics.Color.parseColor("#FF03DAC5"), // 青色
            android.graphics.Color.parseColor("#FF018786"), // 深青色
            android.graphics.Color.parseColor("#FFBB86FC"), // 浅紫色
            android.graphics.Color.parseColor("#FF3700B3")  // 深紫色
        )

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val container = FrameLayout(parent.context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    TypedValue.applyDimension(
                        TypedValue.COMPLEX_UNIT_DIP,
                        ITEM_WIDTH_DP.toFloat(),
                        resources.displayMetrics
                    ).toInt(),
                    getCurrentItemHeight()
                )
            }

            val circleView = TextView(parent.context).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ).apply {
                    gravity = Gravity.CENTER
                }
                textAlignment = View.TEXT_ALIGNMENT_CENTER
                textSize = 20f
                setTextColor(android.graphics.Color.WHITE)
                gravity = Gravity.CENTER
            }

            container.addView(circleView)
            return ViewHolder(container, circleView)
        }

        override fun getItemCount(): Int = items.size

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.bind(items[position], colors[position % colors.size])
        }

        inner class ViewHolder(
            container: View,
            private val circleView: TextView
        ) : RecyclerView.ViewHolder(container) {

            fun bind(text: String, color: Int) {
                circleView.text = text
                updateBackground(color)
            }

            fun updateHeight(height: Int, currentProgress: Float) {
                if (height > 0 && itemView.layoutParams.height != height) {
                    Log.i("ViewHolder", "updateHeight:height = $height, currentProgress = $currentProgress")
                    itemView.layoutParams.height = height
                    itemView.requestLayout()
                }
            }

            private fun updateBackground(color: Int) {
                val drawable = GradientDrawable().apply {
                    shape = GradientDrawable.OVAL
                    setColor(color)
                    setStroke(
                        TypedValue.applyDimension(
                            TypedValue.COMPLEX_UNIT_DIP,
                            2f,
                            resources.displayMetrics
                        ).toInt(),
                        android.graphics.Color.WHITE
                    )
                }
                circleView.background = drawable
            }
        }
    }
}