package com.govee.app.cubedemo.test

import android.graphics.BitmapFactory
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import com.google.gson.Gson
import com.govee.app.cubedemo.R
import com.govee.app.cubedemo.databinding.ActivityTestBinding
import com.govee.cubeview.clickNoRepeat

/**
 *     author  : sinrow
 *     time    : 2024/10/16
 *     version : 1.0.0
 *     desc    :
 */
class TestAc : AppCompatActivity() {
    private lateinit var binding: ActivityTestBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        super.onCreate(savedInstanceState)

        binding = ActivityTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        StereoManager.addActivity(intent.getStringExtra("TAG_ID") ?: "", this)

        binding.run {
            viewRangeView.widthPixels = application.resources.displayMetrics?.widthPixels ?: 0
            val bitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.h6020_pics_color_on)
            viewRangeView.setBgImage(bitmap)
            viewRangeView.clickNoRepeat {
                val str =
                    "{\"a\":{\"a\":1,\"b\":true,\"c\":130,\"d\":[2131694209,2131694210,2131694209],\"e\":2131887482,\"f\":5,\"g\":-1,\"h\":true,\"i\":[1,8],\"j\":true,\"k\":\"自定义eee\"},\"b\":[80,81],\"c\":99,\"d\":[-65536,-33024,-256,-16711936,-16776961,-16711681,-7667457],\"e\":true}"

                val str1 =
                    "{\"a\":{},\"b\":[123,1,12,12535],\"c\":20,\"d\":[12,1223,1224],\"e\":false}"

//        val commBean = CommBean(str)

//        val config = commBean.getConfig(SpeedInfoType4Config::class.java)

                val fromJson = fromJson(str, MusicEffect::class.java)

                val gson = Gson()
                val musicEffect =
                    MusicEffect(subMusicMode = SubMusicMode(), subEffectList = mutableListOf<Int>().apply {
                        add(123)
                        add(1)
                        add(12)
                        add(12535)
                    }, sensitivity = 20, colorList = mutableListOf<Int>().apply {
                        add(12)
                        add(1223)
                        add(1224)
                    }, isNewMusic = false)
                Log.d("-------", "onCreate: musicEffect = $musicEffect")
                val toJson = gson.toJson(musicEffect)
                Log.d("-------", "onCreate: toJson = $toJson")
                val fromJson1 = gson.fromJson(toJson, MusicEffect::class.java)
                val fromJson2 = gson.fromJson(str, MusicEffect::class.java)

                Log.d("-------", "onCreate: newEffect = $fromJson1")

                Log.d("-------", "onCreate: newEffect = $fromJson2")
            }
            tvTest.clickNoRepeat {
                // 调转到 Main 页面

                StereoManager.startActivityForDevice(this@TestAc, StereoManager.firstActivityExtTag("2222"))
            }
            tvTest2.text = <EMAIL>
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d("-------", "onResume: 当前页面 = $this")
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0) // 立即生效，无过渡动画
    }
}