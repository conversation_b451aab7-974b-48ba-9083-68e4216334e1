package com.govee.app.cubedemo.test.webview

import java.security.MessageDigest

/**
 *     author  : sinrow
 *     time    : 2025/3/12
 *     version : 1.0.0
 *     desc    :
 */
object MD5Utils {
    /**
     * 计算字符串的 MD5 值
     */
    fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }

}