package com.govee.app.cubedemo.test


/**
 *Create by hey on 2023/5/25
 *$音乐模式子效果
 */
data class MusicEffect(
    //图标
    var subMusicMode: SubMusicMode,
    //各种子效果 如方向 动感柔和
    var subEffectList: MutableList<Int> = mutableListOf(),
    //灵敏度
    var sensitivity: Int = 99,
    //颜色数组
    val colorList: MutableList<Int> = mutableListOf(),
    val isNewMusic: Boolean = true
) {
    @Transient
    var tempByteArray: ByteArray? = null

    //根据效果转发送到设备的bytes
    fun toBytes(): ByteArray {
        val toBytes =
            ByteArray(1 + colorList.size * 3 + subEffectList.size)
        /*颜色个数*/
        toBytes[0] = colorList.size.toByte()
        /*颜色值*/
        var index = 1
//        for (rgb in colorList) {
//            val rgbColor4Bytes = ColorUtils.getRgbBytes(rgb)
//            System.arraycopy(rgbColor4Bytes, 0, toBytes, index, rgbColor4Bytes.size)
//            index += 3
//        }
        //子效果列表
        subEffectList.forEach {
            toBytes[index] = it.toByte()
            index++
        }
        return toBytes
    }

    //获取颜色bytes 方便subEffectList需要单独组指令的模式复用
    fun getColorBytes(): MutableList<Byte> {
        val toBytes =
            ByteArray(1 + colorList.size * 3)
        /*颜色个数*/
        toBytes[0] = colorList.size.toByte()
        /*颜色值*/
        var index = 1
//        for (rgb in colorList) {
//            val rgbColor4Bytes = ColorUtils.getRgbBytes(rgb)
//            System.arraycopy(rgbColor4Bytes, 0, toBytes, index, rgbColor4Bytes.size)
//            index += 3
//        }
        return toBytes.toMutableList()
    }

    fun toBytesColorAfter(): ByteArray {
        val toBytes =
            ByteArray(1 + colorList.size * 3 + subEffectList.size)
        var index = 0

        //子效果列表
        subEffectList.forEach {
            toBytes[index++] = it.toByte()
        }
        /*颜色个数*/
        toBytes[index++] = colorList.size.toByte()
        /*颜色值*/
//        for (rgb in colorList) {
//            val rgbColor4Bytes = ColorUtils.getRgbBytes(rgb)
//            System.arraycopy(rgbColor4Bytes, 0, toBytes, index, rgbColor4Bytes.size)
//            index += 3
//        }
        return toBytes
    }

}