package com.govee.app.cubedemo.test.webview

import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import java.io.File


/**
 *     author  : sinrow
 *     time    : 2025/3/12
 *     version : 1.0.0
 *     desc    :
 */
class WebResourceInterceptor(private val resourceManager: ResourceManager) : WebViewClient() {

    override fun shouldInterceptRequest(
        view: WebView,
        request: WebResourceRequest
    ): WebResourceResponse? {
        val url = request.url.toString()

        return when {
            url.endsWith(".js") || url.endsWith(".css") -> {
                handleLocalResource(url)
            }

            else -> super.shouldInterceptRequest(view, request)
        }
    }

    private fun handleLocalResource(url: String): WebResourceResponse? {
        // 先尝试获取本地资源
        return resourceManager.getLocalResource(url)?.let { file ->
            createWebResourceResponse(url, file)
        } ?: run {
            // 本地没有资源，尝试下载并缓存
//            resourceManager.downloadAndCacheResource(url)?.let { file ->
//                createWebResourceResponse(url, file)
//            }
            null
        }
    }

    private fun createWebResourceResponse(url: String, file: File): WebResourceResponse {
        val mimeType = when {
            url.endsWith(".js") -> "application/javascript"
            url.endsWith(".css") -> "text/css"
            else -> "application/octet-stream"
        }

        val responseHeaders = hashMapOf(
            "Access-Control-Allow-Origin" to "*",
            "Access-Control-Allow-Methods" to "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers" to "Content-Type",
            "Access-Control-Allow-Credentials" to "true"
        )

        return WebResourceResponse(
            mimeType,
            "UTF-8",
            file.inputStream()
        ).apply {
            setResponseHeaders(responseHeaders)
        }
    }
}