package com.govee.app.cubedemo.test.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.RemoteViews
import com.govee.app.cubedemo.R

/**
 * Cube Demo App Widget Provider
 * 实现 Android App Widget 功能，点击后先发起内部广播，然后跳转到指定页面
 */
class CubeDemoWidgetProvider : AppWidgetProvider() {

    companion object {
        private const val TAG = "CubeDemoWidget"
        const val ACTION_WIDGET_CLICKED = "com.govee.app.cubedemo.test.widget.WIDGET_CLICKED"
        const val EXTRA_WIDGET_ID = "widget_id"

        /**
         * 更新单个 App Widget
         */
        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // 创建 RemoteViews 对象
            val views = RemoteViews(context.packageName, R.layout.widget_cube_demo)

            // 创建自定义广播意图
            val broadcastIntent = Intent(ACTION_WIDGET_CLICKED).apply {
                setPackage(context.packageName) // 限制为应用内部广播
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }

            // 创建 PendingIntent 用于发送广播
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                appWidgetId,
                broadcastIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 设置整个 widget 的点击事件
            views.setOnClickPendingIntent(R.id.widget_icon, pendingIntent)
            views.setOnClickPendingIntent(R.id.widget_title, pendingIntent)
            views.setOnClickPendingIntent(R.id.widget_subtitle, pendingIntent)

            // 更新 widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)

        when (intent.action) {
            ACTION_WIDGET_CLICKED -> {
                val widgetId = intent.getIntExtra(EXTRA_WIDGET_ID, -1)
                Log.d(TAG, "Widget clicked, ID: $widgetId")

                // 发起内部广播
                handleWidgetClick(context, widgetId)
            }
        }
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // 为每个 widget 实例更新
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // 当第一个 widget 被添加时调用
        super.onEnabled(context)
        Log.d(TAG, "Widget enabled")
    }

    override fun onDisabled(context: Context) {
        // 当最后一个 widget 被移除时调用
        super.onDisabled(context)
        Log.d(TAG, "Widget disabled")
    }

    /**
     * 处理 Widget 点击事件
     */
    private fun handleWidgetClick(context: Context, widgetId: Int) {
        Log.d(TAG, "Handling widget click for ID: $widgetId")

        // 1. 发起内部广播
        val internalBroadcast = Intent(WidgetInternalReceiver.ACTION_WIDGET_INTERNAL_BROADCAST).apply {
            setPackage(context.packageName)
            putExtra(EXTRA_WIDGET_ID, widgetId)
            putExtra("timestamp", System.currentTimeMillis())
        }

        Log.d(TAG, "Sending internal broadcast")
        context.sendBroadcast(internalBroadcast)
    }
}
