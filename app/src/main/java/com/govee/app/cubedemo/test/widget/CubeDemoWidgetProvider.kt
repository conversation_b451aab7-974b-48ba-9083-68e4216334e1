package com.govee.app.cubedemo.test.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.govee.app.cubedemo.R

/**
 * Cube Demo App Widget Provider
 * 实现 Android App Widget 功能，点击后跳转到指定页面
 */
class CubeDemoWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // 为每个 widget 实例更新
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // 当第一个 widget 被添加时调用
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        // 当最后一个 widget 被移除时调用
        super.onDisabled(context)
    }

    companion object {
        /**
         * 更新单个 App Widget
         */
        internal fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // 创建 RemoteViews 对象
            val views = RemoteViews(context.packageName, R.layout.widget_cube_demo)

            // 创建点击意图，跳转到目标 Activity
            val intent = Intent(context, WidgetTargetActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }

            // 创建 PendingIntent
            val pendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 设置整个 widget 的点击事件
            views.setOnClickPendingIntent(R.id.widget_icon, pendingIntent)
            views.setOnClickPendingIntent(R.id.widget_title, pendingIntent)
            views.setOnClickPendingIntent(R.id.widget_subtitle, pendingIntent)

            // 更新 widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
