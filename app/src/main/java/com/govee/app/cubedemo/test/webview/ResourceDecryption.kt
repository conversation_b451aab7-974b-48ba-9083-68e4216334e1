package com.govee.app.cubedemo.test.webview

import android.util.Base64
import android.util.Log
import org.json.JSONArray
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec


/**
 *     author  : sinrow
 *     time    : 2025/3/12
 *     version : 1.0.0
 *     desc    :
 */


class ResourceDecryption {
    /**
     * 解密资源链接数组
     * @param encryptedData 加密的数据字符串
     * @return 解密后的资源链接数组
     */
    fun decrypt(encryptedData: String?): List<String> {
        try {
            if (encryptedData.isNullOrEmpty()) {
                return emptyList()
            }

            // 1. Base64解码
            val encryptedBytes = Base64.decode(encryptedData, Base64.NO_WRAP)

            // 2. 初始化解密器
            val keyBytes = SECRET_KEY.toByteArray(Charsets.UTF_8)
            val ivBytes = IV.toByteArray(Charsets.UTF_8)

            // 确保密钥长度为16字节
            val secretKeyBytes =
                if (keyBytes.size >= 16) keyBytes.copyOf(16) else keyBytes.copyOf(16)
            val ivParameterBytes =
                if (ivBytes.size >= 16) ivBytes.copyOf(16) else ivBytes.copyOf(16)

            val secretKey = SecretKeySpec(secretKeyBytes, "AES")
            val ivParameterSpec = IvParameterSpec(ivParameterBytes)

            // 3. 使用CBC模式解密
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec)

            // 4. 解密并解析JSON
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            val jsonString = String(decryptedBytes, Charsets.UTF_8)

            Log.d(TAG, "解密后的JSON: $jsonString")

            val jsonArray = JSONArray(jsonString)
            return List(jsonArray.length()) { i -> jsonArray.getString(i) }

        } catch (e: Exception) {
            Log.e(TAG, "解密失败: ${e.message}", e)
            return emptyList()
        }
    }

    companion object {
        private const val TAG = "ResourceDecryption"
        private const val SECRET_KEY = "abundle-resource"
        private const val IV = "2025031320250313"
    }
}