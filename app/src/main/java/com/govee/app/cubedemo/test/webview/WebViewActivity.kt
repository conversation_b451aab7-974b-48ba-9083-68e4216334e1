package com.govee.app.cubedemo.test.webview

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.govee.app.cubedemo.databinding.ActivityWebBinding
import com.govee.cubeview.clickNoRepeat

/**
 *     author  : sinrow
 *     time    : 2025/3/12
 *     version : 1.0.0
 *     desc    :
 */

class WebViewActivity : AppCompatActivity() {
    private lateinit var binding: ActivityWebBinding
    private val resourceManager: ResourceManager by lazy {
        ResourceManager.getInstance(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWebBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initWebView()
        binding.tvRest.clickNoRepeat {
            initResources()
        }
    }

    private fun initWebView() {
        binding.webView.apply {
            webViewClient = WebResourceInterceptor(resourceManager)
            settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true
            }
        }
    }

    private fun initResources() {

//        val config = resourceManager.loadResourceConfig1()
//        config?.firstOrNull()?.let {
//            val mResourceDecryption = ResourceDecryption()
//            mResourceDecryption.decrypt(it.encryptedResource).let {
//
//            }
//        }
//        binding.webView.post {
//            binding.webView.loadUrl("https://www.baidu.com")
//        }
        binding.webView.post {
            //binding.webView.loadUrl("https://www.baidu.com")
            binding.webView.loadUrl("https://media-a.goveelife.com/deals-img/e250b9cc8e3d93058bcf29f3e51d0f1e-scene.mp4")
        }

//        resourceManager.initializeResources {
//            // 加载网页
//
//        }
    }
}