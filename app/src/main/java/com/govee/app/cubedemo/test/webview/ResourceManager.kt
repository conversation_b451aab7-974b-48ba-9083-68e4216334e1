package com.govee.app.cubedemo.test.webview

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.Collections
import java.util.zip.ZipInputStream


class ResourceManager private constructor(context: Context) {
    private val appContext = context.applicationContext
    private val job = SupervisorJob()
    private val scope = CoroutineScope(Dispatchers.IO + job)
    private val gson = Gson()
    private val baseDir: File by lazy { appContext.getDir("web_resources", Context.MODE_PRIVATE) }
    private val okHttpClient = OkHttpClient()
    private val prefs: SharedPreferences by lazy {
        appContext.getSharedPreferences("resource_manager", Context.MODE_PRIVATE)
    }

    // 修改成员变量，添加配置缓存时间戳
    private var cachedConfig: List<ManifestConfig>? = null
    private var cachedFileNames: Map<String, Set<String>>? = null
    private var lastConfigLoadTime: Long = 0
    private val CONFIG_CACHE_DURATION = 5 * 60 * 1000L // 配置缓存时间：5分钟

    companion object {
        private const val TAG = "ResourceManager"
        private const val CONFIG_FILE = "manifest.json"
        private const val BUNDLE_UPDATE_URL =
            "https://app-h5-manifest.govee.com/dev/bundleUpdate.json"
        private const val MANIFEST_URL = "https://app-h5-manifest.govee.com/dev/manifest.json"
        private const val LAST_UPDATE_TIME = "last_update_time"
        private const val MAX_CACHE_SIZE = 500 * 1024 * 1024L // 500MB
        private const val TWO_WEEKS = 14 * 24 * 60 * 60 * 1000L
        private const val ONE_WEEK = 7 * 24 * 60 * 60 * 1000L
        private const val AES_KEY = "web-bundle-resource-crypto" // 需要替换为实际的密钥

        @Volatile
        private var instance: ResourceManager? = null

        fun getInstance(context: Context): ResourceManager {
            return instance ?: synchronized(this) {
                instance ?: ResourceManager(context).also { instance = it }
            }
        }

        fun release() {
            instance?.let {
                it.destroy()
                instance = null
            }
        }
    }

    fun destroy() {
        job.cancel()
        okHttpClient.dispatcher.executorService.shutdown()
        Log.d(TAG, "ResourceManager destroyed")
    }

    /**
     * 初始化资源配置并下载
     */
    fun initializeResources(onComplete: () -> Unit) {
        scope.launch {
            try {
                // 检查更新并初始化资源
                checkAndInitResources()

                // 在主线程执行回调
                withContext(Dispatchers.Main) {
                    onComplete()
                }
            } catch (e: Exception) {
                Log.e(TAG, "初始化资源失败", e)
                withContext(Dispatchers.Main) {
                    onComplete()
                }
            }
        }
    }

    /**
     * 检查更新并初始化资源
     */
    private suspend fun checkAndInitResources() {
        Log.i(TAG, "初始化资源")
        try {
            val configFile = File(baseDir, CONFIG_FILE)
            val bundleInfo = fetchBundleUpdateInfo()
            val lastUpdateTime = prefs.getString(LAST_UPDATE_TIME, "0") ?: "0"

            // 检查配置文件是否存在
            val configExists = configFile.exists()

            // 检查是否有配置更新
            val hasConfigUpdate = bundleInfo != null && bundleInfo.timeStamp > lastUpdateTime
            
            if (!configExists) {
                Log.i(TAG, "配置文件不存在，需要重新下载")
            } else if (hasConfigUpdate) {
                Log.i(TAG, "检测到配置更新：远程时间戳=${bundleInfo?.timeStamp}，本地时间戳=$lastUpdateTime")
            }

            // 需要更新manifest的条件:
            // 1. 配置文件不存在 或者
            // 2. 有新的更新且时间戳更新了
            val needManifestUpdate = !configExists || hasConfigUpdate

            // 记录是否因为有更新而下载了新配置
            var configUpdatedDueToNewVersion = false

            Log.i(TAG, "初始化资源 bundleInfo = $bundleInfo")

            // 获取配置信息
            val configs = if (needManifestUpdate) {
                // 下载新的配置
                val manifest = downloadManifest()
                Log.i(TAG, "是否成功下载 manifest: ${manifest != null}")

                if (manifest != null) {
                    // 如果无法获取更新信息但需要强制下载，或者MD5校验通过
                    if (bundleInfo == null || verifyManifestMd5(manifest, bundleInfo.md5)) {
                        val parsedConfigs = parseManifest(manifest)

                        // 保存配置文件
                        try {
                            configFile.writeText(gson.toJson(parsedConfigs))
                            Log.d(TAG, "保存新的配置文件成功")

                            // 只有在有新的更新时才更新时间戳
                            if (bundleInfo != null) {
                                prefs.edit().putString(LAST_UPDATE_TIME, bundleInfo.timeStamp).apply()
                                // 标记因版本更新而下载了新配置
                                configUpdatedDueToNewVersion = hasConfigUpdate
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "保存配置文件失败: ${e.message}")
                        }

                        parsedConfigs
                    } else {
                        Log.e(TAG, "manifest MD5校验失败")
                        null
                    }
                } else {
                    Log.e(TAG, "下载 manifest 失败")
                    null
                }
            } else {
                // 使用已有配置
                try {
                    val json = configFile.readText()
                    gson.fromJson<List<ManifestConfig>>(json, object :
                        TypeToken<List<ManifestConfig>>() {}.type)
                } catch (e: Exception) {
                    Log.e(TAG, "读取配置文件失败: ${e.message}", e)
                    null
                }
            }

            if (configs != null) {
                // 确定需要更新的项目:
                // 1. 配置文件不存在时，所有项目都需要更新
                // 2. 配置有更新时，所有项目都需要更新
                // 3. 否则，只更新检测到资源不完整的项目
                val projectsNeedingUpdate = when {
                    !configExists -> {
                        Log.i(TAG, "配置文件刚刚下载，强制更新所有项目资源")
                        configs.map { it.project }
                    }

                    configUpdatedDueToNewVersion -> {
                        Log.i(TAG, "配置文件已更新（版本变更），强制更新所有项目资源")
                        configs.map { it.project }
                    }

                    else -> {
                        Log.i(TAG, "检查哪些项目需要更新资源")
                        checkProjectsNeedingUpdate(configs)
                    }
                }
                
                if (projectsNeedingUpdate.isNotEmpty()) {
                    Log.i(TAG, "以下项目需要更新资源: ${projectsNeedingUpdate.joinToString()}")
                    downloadMissingResources(configs, projectsNeedingUpdate)
                } else {
                    Log.i(TAG, "所有项目资源完整，无需更新")
                }
            } else {
                Log.e(TAG, "无法获取配置信息")
            }

            cleanupResources()
        } catch (e: Exception) {
            Log.e(TAG, "初始化资源失败", e)
        }
    }

    private suspend fun fetchBundleUpdateInfo(): BundleUpdateInfo? {
        Log.i(TAG, "fetchBundleUpdateInfo")
        return withContext(Dispatchers.IO) {
            try {
                val response = okHttpClient.newCall(
                    Request.Builder().url(BUNDLE_UPDATE_URL).build()
                ).execute()

                if (response.isSuccessful) {
                    response.body?.string()?.let {
                        gson.fromJson(it, BundleUpdateInfo::class.java)
                    }
                } else null
            } catch (e: Exception) {
                Log.e(TAG, "获取更新信息失败", e)
                null
            }
        }
    }

    private suspend fun downloadManifest(): String? {
        Log.i(TAG, "downloadManifest 中")
        return withContext(Dispatchers.IO) {
            try {
                val response = okHttpClient.newCall(
                    Request.Builder().url(MANIFEST_URL).build()
                ).execute()

                if (response.isSuccessful) {
                    response.body?.string()
                } else null
            } catch (e: Exception) {
                Log.e(TAG, "下载manifest失败", e)
                null
            }
        }
    }

    private fun verifyManifestMd5(manifest: String, expectedMd5: String): Boolean {
        Log.i(TAG, "verifyManifestMd5")
        return MD5Utils.md5(manifest) == expectedMd5
    }

    private fun parseManifest(manifestJson: String): List<ManifestConfig> {
        return gson.fromJson(manifestJson, object : TypeToken<List<ManifestConfig>>() {}.type)
    }

    private fun decryptResources(encryptedData: String): List<String>? {
        return ResourceDecryption().decrypt(encryptedData)
    }

    private suspend fun handleResources(configs: List<ManifestConfig>) {
        try {
            // 保存配置文件
            val configFile = File(baseDir, CONFIG_FILE)
            configFile.writeText(gson.toJson(configs))
            Log.d(TAG, "保存配置文件成功")

            configs.forEach { config ->
                val projectDir = getProjectDir(config.project)
                val validFiles = mutableSetOf<String>() // 使用 Set 来存储有效文件名

                // 处理zip资源包
                config.resourceZip?.let { zipUrl ->
                    Log.d(TAG, "开始下载资源包: $zipUrl")
                    val zipFile = downloadZipResource(zipUrl)
                    if (zipFile != null) {
                        // 在解压之前获取 zip 文件中的所有文件名
                        ZipInputStream(FileInputStream(zipFile)).use { zipInputStream ->
                            var entry = zipInputStream.nextEntry
                            while (entry != null) {
                                if (!entry.isDirectory) {
                                    validFiles.add(entry.name)
                                }
                                zipInputStream.closeEntry()
                                entry = zipInputStream.nextEntry
                            }
                        }

                        // 解压文件
                        extractZipResource(zipFile, projectDir)
                    }
                }

                // 清理多余资源
                if (validFiles.isNotEmpty()) {
                    Log.d(TAG, "开始清理多余资源，有效文件数量: ${validFiles.size}")
                    projectDir.walkTopDown()
                        .filter { it.isFile }
                        .forEach { file ->
                            val relativePath = file.relativeTo(projectDir).path
                            if (!validFiles.contains(relativePath)) {
                                file.delete()
                                Log.d(TAG, "清理无效文件: ${file.name}")
                            }
                        }
                }
            }

            // 处理完新资源后清除缓存
            clearConfigCache()
        } catch (e: Exception) {
            Log.e(TAG, "处理资源失败", e)
        }
    }

    private suspend fun downloadResource(url: String, projectDir: File) {
        val fileName = getFileNameForUrl(url)
        val file = File(projectDir, fileName)

        if (!file.exists()) {
            withContext(Dispatchers.IO) {
                try {
                    val response = okHttpClient.newCall(
                        Request.Builder().url(url).build()
                    ).execute()

                    if (response.isSuccessful) {
                        response.body?.byteStream()?.use { input ->
                            file.outputStream().use { output ->
                                input.copyTo(output)
                            }
                        }
                        Log.d(TAG, "下载资源成功: $url")
                    } else {
                        Log.e(TAG, "下载资源失败: $url")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "下载资源失败: $url", e)
                }
            }
        } else {
            //Log.e(TAG, "资源已存在: $url")
        }
    }

    private fun getProjectDir(project: String): File {
        return File(baseDir, project).apply {
            if (!exists()) mkdirs()
        }
    }

    private fun getFileNameForUrl(url: String): String {
        return MD5Utils.md5(url)
    }

    fun getLocalResource(url: String): File? {
        val configs = loadResourceConfig() ?: return null
        val fileName = getFileNameForUrl(url)

        configs.forEach { config ->
            if (isFileExistsInProject(fileName, config.project)) {
                val file = File(getProjectDir(config.project), fileName)
                if (file.exists() && !isFileExpired(file)) {
                    file.setLastModified(System.currentTimeMillis())
                    return file
                }
            }
        }
        return null
    }

    fun downloadAndCacheResource(url: String): File? {
        val fileName = getFileNameForUrl(url)
        val projectDir = getProjectDir("default") // 使用默认项目目录
        val file = File(projectDir, fileName)

        if (!file.exists()) {
            runBlocking {
                try {
                    val response = okHttpClient.newCall(
                        Request.Builder().url(url).build()
                    ).execute()

                    if (response.isSuccessful) {
                        response.body?.byteStream()?.use { input ->
                            file.outputStream().use { output ->
                                input.copyTo(output)
                            }
                        }
                        Log.d(TAG, "下载并缓存资源成功: $url")
                        return@runBlocking file
                    } else {
                        Log.e(TAG, "下载资源失败: $url")
                        return@runBlocking null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "下载资源失败: $url", e)
                    return@runBlocking null
                }
            }
        }
        return if (file.exists()) file else null
    }

    /**
     * 加载资源配置
     * 优化缓存逻辑，减少不必要的文件系统遍历
     */
    fun loadResourceConfig(): List<ManifestConfig>? {
        // 检查缓存是否有效，避免频繁读取文件系统
        if (isCacheValid()) {
            // 缓存有效，直接返回缓存的配置，不打印重复日志
            return cachedConfig
        }

        //Log.d(TAG, "配置缓存失效或不存在，重新加载配置文件")
        
        try {
            val configFile = File(baseDir, CONFIG_FILE)
            if (!configFile.exists()) {
                Log.d(TAG, "配置文件不存在")
                return null
            }

            // 读取配置文件获取项目信息
            val configs = try {
                val json = configFile.readText()
                val type = object : TypeToken<ArrayList<ManifestConfig>>() {}.type
                gson.fromJson<ArrayList<ManifestConfig>>(json, type)
            } catch (e: Exception) {
                Log.e(TAG, "解析配置文件失败: ${e.message}", e)
                return null
            }

            // 更新文件名缓存
            cachedFileNames = configs.associate { config ->
                val projectDir = getProjectDir(config.project)
                val fileNames = projectDir.walkTopDown()
                    .filter { it.isFile }
                    .map { it.name }
                    .toSet()
                config.project to fileNames
            }
            Log.d(TAG, "已缓存各项目文件列表，共 ${cachedFileNames?.size} 个项目")

            // 更新配置缓存和时间戳
            cachedConfig = configs
            lastConfigLoadTime = System.currentTimeMillis()

            return configs
        } catch (e: Exception) {
            Log.e(TAG, "加载配置失败: ${e.message}", e)
            return null
        }
    }

    fun loadResourceConfig1(): List<ManifestConfig>? {
        // 如果缓存存在，直接返回
//        cachedConfig?.let { return it }

        return try {
            val configFile = File(baseDir, "long_text_2025-03-13-10-56-34.txt")
            if (configFile.exists()) {
                val json = configFile.readText()
                Log.d(TAG, "读取配置文件内容: $json")

                val type = object : TypeToken<ArrayList<ManifestConfig>>() {}.type
                gson.fromJson<ArrayList<ManifestConfig>>(json, type).also {
                    // 更新缓存
                    cachedConfig = it
                }
            } else {
                Log.d(TAG, "配置文件不存在")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载配置失败: ${e.message}", e)
            null
        }
    }

    private fun cleanupProjectResources(projectDir: File, validUrls: List<String>) {
        projectDir.listFiles()?.forEach { file ->
            val isValidFile = validUrls.any { url ->
                getFileNameForUrl(url) == file.name
            }
            if (!isValidFile) {
                file.delete()
                Log.d(TAG, "清理无效文件: ${file.name}")
            }
        }
    }

    private suspend fun cleanupResources() {
        withContext(Dispatchers.IO) {
            val totalSize = calculateCacheSize()
            when {
                totalSize > MAX_CACHE_SIZE -> cleanupByAge(ONE_WEEK)
                else -> cleanupByAge(TWO_WEEKS)
            }
        }
    }

    private fun calculateCacheSize(): Long {
        var size = 0L
        baseDir.walkTopDown().forEach { file ->
            if (file.isFile) {
                size += file.length()
            }
        }
        return size
    }

    private fun cleanupByAge(maxAge: Long) {
        val currentTime = System.currentTimeMillis()
        baseDir.walkTopDown().forEach { file ->
            if (file.isFile && (currentTime - file.lastModified() > maxAge)) {
                file.delete()
                Log.d(TAG, "清理过期文件: ${file.name}")
            }
        }
    }

    private fun isFileExpired(file: File): Boolean {
        return System.currentTimeMillis() - file.lastModified() > TWO_WEEKS
    }

    private suspend fun handleResources(config: ResourceConfig): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val zipFile = downloadZipResource(config.resourceZip)
                if (zipFile != null) {
                    //extractZipResource(zipFile)
                    true
                } else {
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理资源文件失败: ${e.message}")
                false
            }
        }
    }

    /**
     * 下载zip资源包
     * @param zipUrl zip包的URL
     * @return 下载的临时文件，如果下载失败返回null
     */
    private suspend fun downloadZipResource(zipUrl: String): File? {
        return withContext(Dispatchers.IO) {
            try {
                // 生成唯一的临时文件名，使用URL的一部分作为文件名
                val fileName = "temp_resource_${MD5Utils.md5(zipUrl)}.zip"
                val tempFile = File(appContext.cacheDir, fileName)

                // 如果文件已存在，先删除
                if (tempFile.exists()) {
                    tempFile.delete()
                }
                
                val request = Request.Builder()
                    .url(zipUrl)
                    .build()

                val response = okHttpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    response.body?.byteStream()?.use { input ->
                        FileOutputStream(tempFile).use { output ->
                            input.copyTo(output)
                        }
                    }
                    Log.d(TAG, "成功下载资源包到临时文件: ${tempFile.name}")
                    tempFile
                } else {
                    Log.e(TAG, "下载资源包失败: ${response.code}")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "下载资源包异常: ${e.message}")
                null
            }
        }
    }

    /**
     * 解压zip资源包到指定目录
     * @param zipFile 待解压的zip文件
     * @param targetDir 解压目标目录
     */
    private fun extractZipResource(zipFile: File, targetDir: File) {
        Log.i(TAG, "开始解压 zip 包资源到目录: ${targetDir.path}")
        if (!targetDir.exists()) {
            targetDir.mkdirs()
        }

        try {
            ZipInputStream(FileInputStream(zipFile)).use { zipInputStream ->
                var entry = zipInputStream.nextEntry
                val extractedFiles = mutableListOf<String>()

                while (entry != null) {
                    val outputFile = File(targetDir, entry.name)

                    // 确保解压目标路径在允许的目录范围内
                    if (!outputFile.canonicalPath.startsWith(targetDir.canonicalPath)) {
                        Log.e(TAG, "检测到不安全的zip路径: ${entry.name}")
                        zipInputStream.closeEntry()
                        entry = zipInputStream.nextEntry
                        continue
                    }

                    if (entry.isDirectory) {
                        outputFile.mkdirs()
                    } else {
                        // 记录成功解压的文件
                        extractedFiles.add(entry.name)

                        // 记录文件是否已存在
                        val fileExists = outputFile.exists()

                        // 如果文件已存在，确保删除它以便覆盖
                        if (fileExists) {
                            val deleted = outputFile.delete()
                            if (!deleted) {
                                Log.w(TAG, "无法删除现有文件准备覆盖: ${outputFile.absolutePath}")
                            }
                        }

                        // 确保父目录存在
                        outputFile.parentFile?.mkdirs()

                        // 写入新文件
                        FileOutputStream(outputFile).use { output ->
                            zipInputStream.copyTo(output)
                        }
                    }

                    zipInputStream.closeEntry()
                    entry = zipInputStream.nextEntry
                }

                Log.i(TAG, "ZIP解压完成，共解压 ${extractedFiles.size} 个文件到目录: ${targetDir.path}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解压资源包失败: ${e.message}", e)
        }
    }

    // 添加一个方法用于判断文件是否存在于项目中
    private fun isFileExistsInProject(fileName: String, project: String): Boolean {
        return cachedFileNames?.get(project)?.contains(fileName) ?: false
    }

    /**
     * 修改缓存验证方法，更精确地控制缓存失效条件
     */
    private fun isCacheValid(): Boolean {
        return cachedConfig != null &&
            cachedFileNames != null &&
            (System.currentTimeMillis() - lastConfigLoadTime) < CONFIG_CACHE_DURATION
    }

    // 添加清除缓存的方法，在资源更新时调用
    fun clearConfigCache() {
        cachedConfig = null
        cachedFileNames = null
        lastConfigLoadTime = 0
    }

    /**
     * 检查本地资源是否为空
     * @return 如果所有项目目录都为空或者不存在有效资源，则返回true
     */
    private fun isLocalResourceEmpty(): Boolean {
        // 先尝试加载配置
        val configs = try {
            val configFile = File(baseDir, CONFIG_FILE)
            if (configFile.exists()) {
                val json = configFile.readText()
                val type = object : TypeToken<ArrayList<ManifestConfig>>() {}.type
                gson.fromJson<ArrayList<ManifestConfig>>(json, type)
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "读取配置文件失败: ${e.message}", e)
            null
        }

        // 如果没有配置文件，认为资源为空
        if (configs == null || configs.isEmpty()) {
            return true
        }

        // 检查每个项目目录是否有文件
        var hasAnyFile = false
        for (config in configs) {
            val projectDir = getProjectDir(config.project)
            if (projectDir.exists() && projectDir.list()?.isNotEmpty() == true) {
                // 至少有一个项目目录下有文件
                hasAnyFile = true
                break
            }
        }

        return !hasAnyFile
    }

    /**
     * 检查哪些项目需要更新资源
     * @param configs 配置信息列表
     * @return 需要更新的项目名称列表
     */
    private fun checkProjectsNeedingUpdate(configs: List<ManifestConfig>): List<String> {
        val projectsNeedingUpdate = mutableListOf<String>()

        configs.forEach { config ->
            val projectDir = getProjectDir(config.project)

            // 检查项目资源是否完整
            val resourcesExist = if (projectDir.exists() && projectDir.isDirectory) {
                val files = projectDir.listFiles()
                files != null && files.isNotEmpty() && files.any { it.isFile }
            } else false

            if (!resourcesExist) {
                projectsNeedingUpdate.add(config.project)
            }
        }

        return projectsNeedingUpdate
    }

    /**
     * 下载缺失的资源
     * @param configs 所有配置信息
     * @param projectsToUpdate 需要更新的项目名称列表
     */
    private suspend fun downloadMissingResources(configs: List<ManifestConfig>, projectsToUpdate: List<String>) {
        // 创建一个映射表，用于存储URL到相关项目的映射，避免重复下载相同的zip包
        val zipUrlToProjects = mutableMapOf<String, MutableList<String>>()

        // 首先收集所有需要下载的zip URL和对应的项目
        configs.forEach { config ->
            if (projectsToUpdate.contains(config.project) && config.resourceZip != null) {
                val projectList = zipUrlToProjects.getOrPut(config.resourceZip) { mutableListOf() }
                projectList.add(config.project)
            }
        }

        // 使用线程安全的结构来存储下载结果
        val downloadResults = Collections.synchronizedMap(mutableMapOf<String, File?>())

        // 创建多个协程同时下载
        coroutineScope {
            zipUrlToProjects.keys.forEach { zipUrl ->
                launch(Dispatchers.IO) {
                    Log.d(TAG, "开始下载资源包: $zipUrl")
                    val zipFile = downloadZipResource(zipUrl)
                    downloadResults[zipUrl] = zipFile

                    // 添加详细日志
                    if (zipFile != null) {
                        Log.d(TAG, "资源包下载完成: $zipUrl -> ${zipFile.absolutePath}")
                    } else {
                        Log.e(TAG, "资源包下载失败: $zipUrl")
                    }
                }
            }
        }
        // 此时所有下载任务已完成

        // 处理下载结果
        downloadResults.forEach { (zipUrl, zipFile) ->
            if (zipFile != null) {
                val projects = zipUrlToProjects[zipUrl] ?: return@forEach

                // 预先获取所有zip文件里的内容清单，避免重复读取
                val validFiles = mutableSetOf<String>()
                try {
                    ZipInputStream(FileInputStream(zipFile)).use { zipInputStream ->
                        var entry = zipInputStream.nextEntry
                        while (entry != null) {
                            if (!entry.isDirectory) {
                                validFiles.add(entry.name)
                            }
                            zipInputStream.closeEntry()
                            entry = zipInputStream.nextEntry
                        }
                    }

                    Log.d(TAG, "已读取zip包内容清单，共 ${validFiles.size} 个文件")
                } catch (e: Exception) {
                    Log.e(TAG, "读取zip文件内容失败: ${e.message}", e)
                    return@forEach
                }

                // 对每个使用此zip包的项目执行解压和清理
                for (projectName in projects) {
                    val projectDir = getProjectDir(projectName)

                    try {
                        // 解压文件到项目目录
                        extractZipResource(zipFile, projectDir)
                        Log.d(TAG, "项目 $projectName 资源解压完成")

                        // 清理多余资源
                        if (validFiles.isNotEmpty()) {
                            projectDir.walkTopDown()
                                .filter { it.isFile }
                                .forEach { file ->
                                    val relativePath = file.relativeTo(projectDir).path
                                    if (!validFiles.contains(relativePath)) {
                                        file.delete()
                                        Log.d(TAG, "清理项目 $projectName 中的无效文件: ${file.name}")
                                    }
                                }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "处理项目 $projectName 的资源失败: ${e.message}", e)
                    }
                }

                // 所有项目处理完毕后再删除临时zip文件
                try {
                    val deleted = zipFile.delete()
                    if (deleted) {
                        Log.d(TAG, "删除临时zip文件成功: ${zipFile.name}")
                    } else {
                        Log.w(TAG, "删除临时zip文件失败: ${zipFile.name} 可能已被删除或无权限")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "删除临时zip文件异常: ${e.message}", e)
                }
            } else {
                Log.e(TAG, "没有找到下载的资源包: $zipUrl")
            }
        }

        // 资源更新后清除缓存
        clearConfigCache()
    }
}
