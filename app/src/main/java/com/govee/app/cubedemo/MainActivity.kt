package com.govee.app.cubedemo

import android.graphics.Color
import android.graphics.PointF
import android.os.Build
import android.os.Bundle
import android.widget.SeekBar
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.isVisible
import com.govee.app.cubedemo.databinding.ActivityMainBinding
import com.govee.app.cubedemo.test.StereoManager
import com.govee.cubeview.CanvasLayout
import com.govee.cubeview.Utils
import com.govee.cubeview.ZoomLayout
import com.govee.cubeview.clickNoRepeat
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.data.CubeConnectStatusData
import com.govee.cubeview.reconfigure.shape.data.CubeMoveFeastData
import com.govee.cubeview.reconfigure.shape.ui.OnShapeListener
import com.govee.cubeview.setViewsVisible
import com.govee.cubeview.shape.AbsCubeShapeView
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor
import com.govee.cubeview.visible
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.random.Random

class MainActivity : AppCompatActivity() {
    private val TAG = "MainActivity"

    private val shapeProxyV1 by lazy { binding.canvasLayout }

    private val newModeTypes = mutableListOf<NewModeType>().apply {//8370
        add(NewModeType(CanvasLayoutModeType.AddLamp, "编辑模式"))
//        add(NewModeType(CanvasLayoutModeType.Preview, "预览模式"))
//        add(NewModeType(CanvasLayoutModeType.EditPre, "编辑后模式"))
//        add(NewModeType(CanvasLayoutModeType.MultiPower, "辅助电源模式"))
//        add(NewModeType(CanvasLayoutModeType.Focus, "聚焦模式"))
//        add(NewModeType(CanvasLayoutModeType.Install, "安装模式"))
//        add(NewModeType(CanvasLayoutModeType.ColorMode, "颜色模式"))
        add(NewModeType(CanvasLayoutModeType.Graffiti, "涂鸦模式"))
//        add(NewModeType(CanvasLayoutModeType.DefaultType, "默认模式"))
//        add(NewModeType(CanvasLayoutModeType.MoveFeast, "盛宴模式"))
    }

    private val oldModeTypes = mutableListOf<OldModeType>().apply {
        add(OldModeType(CanvasLayout.Type.AddLamp, "编辑模式"))
        add(OldModeType(CanvasLayout.Type.Preview, "预览模式"))
//        add(OldModeType(CanvasLayout.Type.Check, "编辑前模式"))
//        add(OldModeType(CanvasLayout.Type.MultiPower, "辅助电源模式"))
//        add(OldModeType(CanvasLayout.Type.Focus, "聚焦模式"))
//        add(OldModeType(CanvasLayout.Type.Install, "安装模式"))
//        add(OldModeType(CanvasLayout.Type.Check, "校准模式"))
//        add(OldModeType(CanvasLayout.Type.ColorMode, "颜色模式"))
//        add(OldModeType(CanvasLayout.Type.DefaultType, "默认模式"))
    }
    private val testStr =
//        "AgZABkAGAAAGGQb8BXgAAQU"
//        "CgZABkAGLAEGaAb8BSwBBo8GuAXwAAZABrgFAAAGGQb8BSwBBvIFQQYAAAajBUEG8AAGtwb8BQAABgYH/AU8AAbeBkAG8AABAQEAARACMAQIEAEQAggCAQACEAEgIAA"
//        "FQZBBkQGtAAG8wVEBrQABssFAAYsAQYaBgAGLAEGaQYABrQABpAGvAV4AAZBBrwFLAEG8wW8BSwBBssFdwU8AAbzBTMFtAAGQQYzBXgABmkG7wQAAAaQBjMFPAAGywXvBPAABhoG7wQ8AAZCBqoEPAAGkAaqBPAABvMFqgQsAQYaBncFeAAGaQZ3BbQABrgGdwW0AAEDAQAEEAIgASAEIBABECAgBCAYEAQIIiACASACAAIhAQQgCAgAAgAQBBACEAA="
//          "EwZABkAGWgAGQAbyBdIABvwFGQYOAQb8BWgGlgAG/AXKBVoABkAGowUeAAZABlQFlgAG/AV7BUoBBvwFLAXSAAb8Bd0EDgEGuAW2BEoBBvwFjgQeAAa4BWcElgAG/AU/BB4ABvwF8AMOAQZABhgEHgAGuAUYBB4ABkAGZwRKAQZABrYE0gABBQEAEAIECCASAQAQBAEECDAQAAIgAiggEAEKBAIBBAIFAgAIAAIACAA"
//          "FQZABkAGWgAGQAbyBdIABvwFGQYOAQb8BWgGlgAG/AXKBVoABrgFowXSAAb8BXsFDgEGQAajBR4ABkAGVAWWAAb8BSwF0gAG/AXdBA4BBrgFBQXSAAa4BbYESgEG/AWOBB4ABrgFZwSWAAb8BT8EHgAG/AXwAw4BBkAGGAQeAAa4BRgEHgAGQAZnBEoBBkAGtgTSAAEFAQAQAgQIIBIBABAFAgEEAAEECCACIAIsAQAgEAEKBAIBBAIFAgAIAAIACAA"
//          "FQZABkAGHgAG/QUZBpYABvwFaQbSAAZBBpAGWgAGuAVBBh4ABnQFaQZKAQYwBUEGWgAGLwWQBpYABnMFGgbSAAa4BfMFSgEGdAXLBVoABi8F8gWWAAYvBaQFDgEG6wTLBUoBBusEGgZaAAanBPIFlgAGpgSjBVoABmIEywUeAAYdBPIFDgEGpgRBBpYABmIEGgZKAQEE"
    //"FQZABkAGAAAG8gVABngABhkGhQa0AAZoBoUGPAAGygWFBgAABqMFyQYsAQZUBckGPAAGewUOB3gABqMFQQYsAQZ7BYUGtAAGVAVBBjwABiwFhQZ4AAYFBUEG8AAG3QSFBiwBBgUFyQY8AAa2BMkGeAAGjgSFBjwABmcEyQYAAAY/BA4H8AAG3QQOB3gABo4EDgcsAQEFAQAQAgQIIBIBABAFEAQIEAEAAQYBAAggAgECBBADAgACCRAgCAEgAAEgIAA"
//        "FQZABkAGAAAG8gVABngABhkGhQa0AAZoBoUGPAAGygWFBgAABqMFyQYsAQZUBckGPAAGewUOB3gABqMFQQYsAQZ7BYUGtAAGVAVBBjwABiwFhQZ4AAYFBUEG8AAG3QSFBiwBBgUFyQY8AAa2BMkGeAAGjgSFBjwABmcEyQYAAAY/BA4H8AAG3QQOB3gABo4EDgcsAQEEAQQQAgQIIBIBABAFEAQIEAEAAQYBAAggAgECBBADAgACCRAgCAEgAAEgIAA="
//        "CgZABkAGAAAG8gVABgAABqMFQAYAAAZUBUAGAAAGewX8BQAABqMFuAUAAAbKBXMFAAAG8gW4BQAABsoF/AUAAAYZBvwFAAABAAEAAQIQAhACEAgBCAEIASAEAQgQAgA"
//        "CgZABkAGAAAGaAb8BQAABo8GuAXwAAbeBrgFeAAGtwZzBXgABgYH/AV4AAYtB0AGeAAG3gZABgAABo8GQAYAAAa3BvwFeAA="
//        "FAZABkAGAAAGGQb8BQAABsoF/AUAAAajBUAGAAAGewX8BQAABiwF/AUAAAZUBbgFAAAGLAVzBQAABt0EcwUAAAajBbgFAAAGygVzBQAABvIFLwUAAAYZBnMFAAAG8gW4BQAABkAGuAUAAAaPBrgFAAAGaAb8BQAABrcG/AUAAAa3BnMFAAAGBgdzBQAA"
//        "ARBABkAGAAAAAAAAAQA="
//        "BhJABkAGAAAQcQVABg4BEP8DQAYOARE1A0AGDgESIwNfBtIAEUIDcQZ4AAEBAQEBAgECAQIBAgECAQI="
//        "BhJABkAGAAAQ4wVABg4BED8FQAYOARHmBEAGDgES3gROBtIAEewEVgZ4AAEAAQABAgECAQIBAgECAQI="
//        "BhFABkAGAAASMwY5BiwBEAQGiQZ4ABE7BisGHgAQDgbeBfAAEI0GFAZKAQEAAQABBgEKAQABAgEAAQA="
    //"ChBABkAGtAAQmgVABrQAEUIFQQYOARAVBY0GeAAQFQX0BfAAEMIEZAXwABGWBBcFSgEQPQQXBbQAEeQDFwUOARC4A8oE8AABAAEAAQIBAgEGAQABAgECAQIBAgEEAQA="
//        "BhFABkAGAAASMwY5BiwBEAQGiQZ4ABE7BisGHgAQDgbeBfAAEI0GFAZKAQEAAQABBgEKAQABAgEAAQA="
//        "BhFABkAGAAASMwY5BiwBEAQGiQbSABE7BisGHgAQDgbeBUoBEI0GFAY8AAEAAQABBgEKAQABAgEAAQA="
//        "BhFABkAGAAASCgYhBiwBEMQFmgbSABEpBuoFHgAQ7AWABUoBEKsGAwY8AAEAAQABBgEKAQABAgEAAQA="
//        "ChBABkAGAAAQQAZ4BQAAEUAG/QQAABDWBcAELAEQqwbABDwAEFgHXAQ8ABHDBx4EPAAQwwejAwAAEcMHKAMAABAuCOsCPAABAAEAAQIBAgEGAQABAgECAQIBAgEEAQA="
//    "CRBABkAGAAASQAa0BQAAELQFtAUOARJABmQFAAAQtAVkBQ4BEEAG2AQAABBABhAEAAAQzAZkBVoAEMwGZAVaAAEAAQABAgEOAQABDgEAAQIBAAEAAQA="
//        "ERI+AwYJAAAQPgN6CAAAED4DsgcAABA+A+oGAAASPgNeBgAAED4D0gUAABA+AwoFAAAQPgNCBAAAED4DegMAABDKAwYJWgAQkgQGCVoAEFoFBglaABAiBgYJWgAQ6gYGCVoAELIHBglaABB6CAYJWgAQQgkGCVoAAQABAAEMAQIBAgECAQQBAgECAQIBAAECAQIBAgECAQIBAgECAQA="
        //"eCBwBjQGWgAgWAY0BgAAIEAGNAYAACAoBjQGAAAgEAY0BgAAIPgFNAZaACDgBTQGDgEgyAU0Bg4BILAFNAYAACCYBTQGDgEggAU0BgAAIGgFNAYAACBQBTQGDgEgOAU0Bg4BICAFNAYOASAIBTQGDgEg8AQ0Bg4BINkENAa0ACDABDQGDgEgqAQ0Bg4BIJAENAa0ACB4BDQGDgEgYAQ0BrQAIEgENQYOASAwBDUGAAAgGAQ0BloAIAAENQa0ACDpAzUGtAAg0AM1BgAAILkDNAYAACCgAzUGAAAgoANMBgAAILkDTQZaACDQA00GWgAg6ANNBgAAIAAETQYAACAYBE0GDgEgMARMBloAIEgETAa0ACBhBE0GWgAgeARMBg4BIJAETQa0ACCoBE0GWgAgwARNBgAAINgETQYAACDwBEwGAAAgCAVNBloAICAFTAZaACA4BUwGDgEgUAVMBloAIGgFTAYOASCABUwGDgEgmAVMBloAILAFTAYOASDIBUwGtAAg4AVMBrQAIPgFTAYAACAQBkwGtAAgKAZMBrQAIEAGTAZaACBYBkwGtAAgcAZMBloAIIgGTAYOASCgBkwGDgEguAZMBg4BINAGTAZaACDoBkwGAAAgAAdMBloAIBgHTAZaACAwB0wGtAAgSAdMBgAAIGAHTAYAACB4B0wGtAAgkAdMBrQAIKgHTAZaACDAB0wGDgEg2AdMBg4BIIkDNAYOASBwAzQGWgAgiAY0BgAAIKAGNAZaACC4BjQGtAAg0AY0BgAAIOgGNAYOASAABzQGWgAgGAc0BgAAIDAHNAYOASBIBzQGAAAgYAc0BgAAIHgHNAYOASCQBzQGAAAgqAc0BgAAIMAHNAYOASDYBzQGtAAg8Ac0BgAAIAgINAYAACAgCDQGAAAgOAg0Bg4BIFAINAYOASBoCDQGDgEggAg0Bg4BIJgINAa0ACCwCDQGAAAgyAg0BrQAIOAINAYAACD4CDQGDgEgEAk0Bg4BIBAJTAZaACD4CEwGDgEg4AhMBloAIMgITAYAACCwCEwGDgEgmAhMBg4BIIAITAYAACBoCEwGWgAgUAhMBg4BIDgITAZaACAgCEwGWgAgCAhMBloAIPAHTAa0AAEBAAQCBQgCCAIIAggCBAEBBAEECAIBBAgCCAIBBAEEAQQBBAEEAggBBAEEAggBBAIIAQQIAgQBAggCCAgCCAIIAwQIAQQBBAIIAggEAQEECAIBBAQBCAIBBAIIAggCCAEEAQQEAQEEBAEEAQEEBAEIAggCAggIAggCAQQIAgEEBAEEAQQBAQQCCAEEAQQIAgIIAggIAggCAQQEAQQAAQQEAAIIAQQIAgIIBAEBBAIIBAECCAIIBAECCAIIBAEIAgIIAggCCAQBBAEEAQQBCAICCAgCAggEAQQCAgEBBAQBCAIBBAEECAIEAQEEBAEEAQBAgA="
        "KCBMBsQGtAAgTAasBg4BIGQGrAYAACA0BqwGtAAgNAaUBg4BIDQGfAYAACAcBnwGAAAgBAZ8BrQAIBwGZAa0ACAcBkwGWgAgHAY0BrQAIDQGNAZaACBMBjQGtAAgTAYcBloAIEwGBAZaACA0BgQGAAAgHAYEBrQAIAQGBAa0ACAEBhwGAAAgNAbsBQ4BIEwG7AVaACBMBtQFDgEgZAbUBQAAIDQG1AUAACAcBtQFDgEgNAa8BbQAIGQGBAYOASB8BgQGDgEgZAYcBgAAIGQGNAa0ACA0BkwGDgEgTAZMBgAAIGQGTAYOASA0BmQGWgAgTAZ8BgAAIEwGlAZaACBkBnwGDgEgfAZ8BloAIHwGZAa0ACAcBqwGWgABAgAEBAECBQIAAgkCCAEKCAYCAAQBCAYEAgEECAEIAggHCAYCCAIEBAACAAgCAgUCAAgGAQAEAAQDBAAEAQEABAMCCAQAAgACCQIABAEBAgQABAA="
//        "DRCkBhgIAAASpAaMBwAAEBgGjAcOARBQBYwHDgESpAY8BwAAEBgGPAcOARCkBrAGAAAQpAboBQAAEKQGIAUAABGkBqUEAAAQDwdoBDwAEDAHPAdaABAwB4wHWgABAAEAAQIBDgECAQABDgEAAQIBAgECAQQBAAEAAQA="
//        "CgYYBacGAAAGQAVjBgAABmcFHgYAAAaPBdoFAAAG3gXaBQAABi0G2gUAAAZ8BtoFAAAGywbaBQAABhoH2gUAAAZpB9oFAAABAAAGAQgBCAEIARACEAIQAhACEAIQAgA="
//        "Bg9ABkAGeAAP4AV4BjwAD+AF6AYAAA9ABiAHLAEPQAaQBwAAD+AFyAc8AAAAAQEBAgIA"

    data class NewModeType(val type: CanvasLayoutModeType, val name: String)
    data class OldModeType(val type: CanvasLayout.Type, val name: String)

    private val shapeTypes by lazy {
        mutableListOf<ShapeType>().apply {
            add(ShapeType(Shape.TYPE_SQUARE, 0))
//            add(ShapeType(Shape.TYPE_GAME_RECTANGLE, 0))
//            add(ShapeType(Shape.TYPE_GAME_SQUARE, 0))
//            add(ShapeType(Shape.TYPE_SPACE_HEXAGON, 0))
//            add(ShapeType(Shape.TYPE_Y, 180))
//            add(ShapeType(Shape.TYPE_HEXAGON, 60))
//            add(ShapeType(Shape.TYPE_TRIANGLE, 0))
//            add(ShapeType(Shape.TYPE_SOLID_HEXAGON, 90))// 默认角度 90
        }
    }

    private fun getDefColors(): MutableList<Int> {
        return mutableListOf<Int>().apply {
            add(CubeUtils.toColor(255, 0, 0))
            add(CubeUtils.toColor(255, 127, 0))
            add(CubeUtils.toColor(255, 255, 0))
            add(CubeUtils.toColor(0, 255, 0))
            add(CubeUtils.toColor(0, 0, 255))
            add(CubeUtils.toColor(0, 255, 255))
            add(CubeUtils.toColor(139, 0, 255))
        }
    }

    data class ShapeType(val type: Int, val angle: Int)

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.apply {
            val ceil = ceil(47 / 8f).toInt()
            val booleanArray = BooleanArray(17)
            booleanArray[16] = true
            showLog(TAG, "onCreate: CEIL = $ceil , booleanArray = $booleanArray")

            zoomLayout.init(this@MainActivity)

            zoomLayoutOld.init(this@MainActivity)

            val focusDragScaleDex =
                resources.displayMetrics.widthPixels / (com.govee.cubeview.view.FocusDragView.SIZE_FOCUS * 375 / 50f)

            zoomLayout.moveOrScaleListener = object : ZoomLayout.MoveOrScaleListener {
                override fun onMoveOrScale(scale: Float) {
//                    showLog(TAG, "onMoveOrScale: scale = $scale ")
                    val sc = focusDragScaleDex / scale
                    canvasLayout.updateDragViewScale(sc)
                }
            }

            zoomLayoutOld.moveOrScaleListener = object : ZoomLayout.MoveOrScaleListener {
                override fun onMoveOrScale(scale: Float) {

                    val sc = focusDragScaleDex / scale
                    canvasLayoutOld.updateDragViewScale(sc)
                }
            }

            btnFanzhuan.setOnClickListener {
//                canvasLayoutOld.flipShapeViewByX()
//                shapeProxyV1.flipShapeViewByX()
                val shapeCenterPointF = canvasLayout.getShapeCenterPointF()
                canvasLayout.updateFocusPoint(shapeCenterPointF)
                StereoManager.startActivityForDevice(this@MainActivity, "1111")
            }

            btnXuanzhuan.clickNoRepeat {
//                canvasLayoutOld.setShapeRotation(30f)
                shapeProxyV1.setShapeRotation(45f)
//                val shapes = Shape.getShapes(testStr)
//                canvasLayout.setShapeData(shapes)
            }

            btnChongzhi.clickNoRepeat {
//                canvasLayoutOld.reSetCanvas(true)
//                canvasLayout.resetShape(true)
                val old = oldModeTypes[modeTypeIndex]
                val shapes = Shape.getShapes(testStr)
                val shapes1 = Shape.getShapes(testStr)
                canvasLayout.setCubeType(PanelLampType.Y)
                canvasLayout.setShapeData(shapes)

                val connectStateList = mutableListOf<CubeConnectStatusData>()
                shapes.forEach {
                    val cubeConnectStatusData =
                        CubeConnectStatusData(it.type, it.inputNum.toByte(), it.outputNum.toByte())
                    connectStateList.add(cubeConnectStatusData)
                }
                setH6063CubeShape(connectStateList)

                val clone = shapes1.clone() as ArrayList<ShapePosition>
                clone.onEach {
//                    it.angle = (it.angle - 90).checkAngle()
//                    it.type = Shape.TYPE_SPACE_HEXAGON
                }
                canvasLayoutOld.setCubeShapeViewType(Shape.TYPE_SPACE_HEXAGON)
                canvasLayoutOld.setShapeData(clone, old.type)
//
                //showLog("onCreate:对比数据1 testStr = $testStr ")
                //showLog("onCreate:对比数据2 shapes = $shapes ")
//                showLog("onCreate:对比数据3 shapes = $shapes1 ")
//                val shapePosition = shapes.first()
//                if (shapePosition.type == Shape.TYPE_SPACE_HEXAGON) {
//                    val firstAngle = shapePosition.angle
//                    val connectList = mutableListOf<ByteArray>()
//                    shapes.onEach {
//                        connectList.add(byteArrayOf(it.inputNum.toByte(), it.outputNum.toByte()))
//                    }
//                    val shapes1 = Shape.getShapesByStream(canvasLayout.getShapeViews().first(), firstAngle, connectList)
//                    showLog("onCreate:对比数据2 shapes = $shapes1 ")
//                    canvasLayout.setShapeData(shapes1)
//                } else {
//                    canvasLayout.setModeType(CanvasLayoutModeType.EditPre)
//                    canvasLayout.setShapeData(shapes)
//                }
            }

            btnChangeMode.clickNoRepeat {
                val shapeRotations1 = canvasLayout.getShapeRotations()
                canvasLayout.setModeType(CanvasLayoutModeType.CheckPre)
                canvasLayout.setShapeData(shapeRotations1)
                canvasLayout.installAllShape()
            }

            btnChange.clickNoRepeat {
                updateShapeType()
//                setH6063CubeShape()
            }

            btnImport.clickNoRepeat {
                val shapeRotations = canvasLayoutOld.getShapeRotations()
                val shapeRotations1 = canvasLayout.getShapeRotations()
                showLog(TAG, "onCreate: old shapeRotations= $shapeRotations")
                showLog(TAG, "onCreate:  shapeRotations --------------------------------------- ")
                showLog(TAG, "onCreate: new shapeRotations1 = $shapeRotations1")
//                canvasLayoutOld.setCubeShapeViewType(Shape.TYPE_SPACE_HEXAGON)
//                canvasLayoutOld.setShapeData(shapeRotations1, oldModeTypes[modeTypeIndex].type)
                resetImportData()
            }

            btnMode.clickNoRepeat {

                modeTypeIndex++

                if (modeTypeIndex >= newModeTypes.size) {
                    modeTypeIndex = 0
                }

                val new = newModeTypes[modeTypeIndex]
                val old = oldModeTypes[modeTypeIndex]

                val shapeRotations1 = canvasLayoutOld.getShapeRotations()

                val shapeRotations = shapeProxyV1.getShapeRotations()

                showLog(TAG, "onCreate: old shapeRotations = $shapeRotations1")
                showLog(TAG, "onCreate: new shapeRotations = $shapeRotations")
                val handleShapesStr = CubeUtils.handleShapesStr(shapeRotations)
                showLog(TAG, "onCreate() result handleShapesStr = $handleShapesStr ")

//                canvasLayoutOld.updateDataWithType(old.type, false)
//                canvasLayout.updateDataWithType(new.type)

//                if (new.type == CanvasLayoutModeType.MultiPower) {
//                    shapeRotations.onEach {
//                        it.mainPower = null
//                        it.ext = null
//                    }
//                }

                canvasLayout.setModeType(new.type).setShapeData(shapeRotations)

                canvasLayoutOld.setShapeData(shapeRotations1, old.type)

                canvasLayoutOld.resetPreViewRandomBackground()

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    canvasLayoutOld.setShapePreViewBackground(Color.WHITE, Color.WHITE)
                }

                updateUI()
            }

            btnInstallNext.clickNoRepeat {
                val oldPair = canvasLayoutOld.installNextShape(false)
                val newPair = shapeProxyV1.installNextShape(false)

                installMode(oldPair, newPair)
            }
            btnAddLamp.clickNoRepeat {
                val shapeViews = canvasLayout.getShapeRotations()

                shapeViews.forEachIndexed { index, baseShapeView ->
//                    if (index < 4) {
                    baseShapeView.editable = false
//                    }
                }
                canvasLayout.setShapeData(shapeViews)
            }

            btnInstallLast.clickNoRepeat { installLastShape() }

            btnInstallAll.clickNoRepeat {
                canvasLayoutOld.installAllShape()
                shapeProxyV1.installAllShape()
            }

            btnCheckNext.clickNoRepeat {
                val shapeRotations = canvasLayout.getShapeRotations()
                if (checkNextPos >= shapeRotations.size) {
                    checkNextPos = 0
                }

                val checkNextShape = canvasLayoutOld.checkNextShape(checkNextPos)
                val checkNextShape1 = shapeProxyV1.checkNextShape(checkNextPos)

                val yShapeIc = canvasLayoutOld.getYShapeIc(checkNextPos)
                val yShapeIc1 = shapeProxyV1.getYShapeIc(checkNextPos)
                showLog(TAG, "onCreate:old yShapeIc = $yShapeIc ")
                showLog(TAG, "onCreate:new yShapeIc = $yShapeIc1 ")

                moveCenterZoomLayout(checkNextShape?.pos, checkNextShape1)

                checkNextPos++
            }

            btnCheckAll.clickNoRepeat {
                canvasLayoutOld.checkAllShape()
                shapeProxyV1.checkAllShape()
            }

            resetImportData()
        }
        val booleanArrayOf = booleanArrayOf(true, true, false, false, false, false, false, false)
        val makeSelectByOneBit = Utils.makeSelectByOneBit(booleanArrayOf)
        showLog("makeSelectByOneBit = $makeSelectByOneBit")
    }

    var modeTypeIndex = 0
    var shapeTypeIndex = 0
    var checkNextPos = 0


    private fun installLastShape() {
        binding.apply {
            val oldPair = canvasLayoutOld.installLastShape()
            val newPair = shapeProxyV1.installLastShape()

            installMode(oldPair, newPair, false)
        }
    }

    private fun installMode(
        oldPair: Pair<AbsCubeShapeView, Int>?,
        newPair: Pair<BaseShapeView, Int>?,
        isNextMode: Boolean = true
    ) {
        oldPair?.first?.apply {
            // 需要习惯改变思维
            if (isAddMultiPower) {
                //
                showLog(TAG, "onCreate: show multi power dialog  ")
            } else {
                /*移动画布到下一个点*/

                moveCenterZoomLayout(pos, null)
            }
            showLog(
                TAG,
                "installMode: oldPair nextDirectionTag = $nextDirectionTag , offsetTag = $offsetTag $canvasRotation"
            )
        }
            ?: showLog(TAG, "onCreate: old -- ${if (isNextMode) "已是最后一个了" else "已是第一个了"} ")

        newPair?.first?.apply {
            // 当前正在安装的图块，整体图形不可视，则移动到当前图块的中心点
            moveCenterZoomLayout(null, centerPos)
            showLog(
                TAG,
                "installMode: newPair nextDirectionTag = $nextDirectionTag , offsetTag = $offsetTag"
            )
        }
            ?: showLog(TAG, "onCreate: new -- ${if (isNextMode) "已是最后一个了" else "已是第一个了"} ")
    }

    private fun moveCenterZoomLayout(oldCenterPos: PointF?, newCenterPos: PointF?) {

        with(binding) {

            if (newCenterPos != null && !shapeProxyV1.isShapeAllVisible()) {
                val pos = PointF()
                pos.x = canvasLayoutOld.width / 2f - newCenterPos.x
                pos.y = canvasLayoutOld.height / 2f - newCenterPos.y
                zoomLayout.setTransitionXY(pos)
            }

            if (oldCenterPos != null && !canvasLayoutOld.isShapeAllVisible()) {//!nextShape.getGlobalVisibleRect(new Rect()))
                val pos = PointF()
                pos.x = canvasLayoutOld.width / 2f - oldCenterPos.x
                pos.y = canvasLayoutOld.height / 2f - oldCenterPos.y
                zoomLayoutOld.setTransitionXY(pos)
            }
        }

    }

    private fun resetImportData(
        type: Int = shapeTypes.first().type, angle: Int = shapeTypes.first().angle
    ) {

        binding.apply {
            val arrayList = arrayListOf<ShapePosition>()
            arrayList.add(ShapePosition(type, 1600f, 1600f, angle))

            // 需要手动设置当前画布 CubeType 类型
            val cubeType: PanelLampType = when (type) {
                Shape.TYPE_SPACE_HEXAGON -> {
                    PanelLampType.SPACE_HEXAGON
                }

                Shape.TYPE_HEXAGON -> {
                    PanelLampType.HEXAGON
                }

                Shape.TYPE_SOLID_HEXAGON -> {
                    PanelLampType.SOLID_HEXAGON
                }

                Shape.TYPE_Y -> {
                    PanelLampType.Y
                }

                Shape.TYPE_TRIANGLE -> {
                    PanelLampType.TRIANGLE
                }

                Shape.TYPE_GAME_RECTANGLE,
                Shape.TYPE_GAME_TRIANGLE,
                Shape.TYPE_GAME_SQUARE -> {
                    PanelLampType.GAME_WALL.apply {
                        shapeType = type
                    }
                }

                Shape.TYPE_SQUARE -> {
                    PanelLampType.SQUARE
                }

                else -> {
                    PanelLampType.HEXAGON
                }
            }
            canvasLayout.setCubeType(cubeType).setModeType(CanvasLayoutModeType.Edit)
                .setShapeData(arrayList)

            canvasLayoutOld.setCubeShapeViewType(type)
            val arrayListOf = arrayListOf<ShapePosition>()
            arrayListOf.add(ShapePosition(type, 1600f, 1600f, angle))
            canvasLayoutOld.setShapeData(arrayListOf, CanvasLayout.Type.AddLamp)

            canvasLayoutOld.callBack = object : CanvasLayout.OnScaleAndTransitionChange {
                override fun scaleAndTransition(scale: Float, point: PointF, animation: Boolean) {
                    // 需要确认这个移动中心点的回调
                    showLog(
                        TAG,
                        "zoomLayoutOld scaleAndTransition: scale = $scale , point = $point , animation = $animation , point = $point"
                    )
                    zoomLayoutOld.post {
                        val zoomWidth = resources.displayMetrics.widthPixels * 325 / 375
                        val sc = zoomWidth / (Shape.LINE_LENGTH_4_SQURA * 2 * 325 / 50f);
                        if (animation) {
                            zoomLayoutOld.setScaleAndTranslationByAnimation(sc, point, 600)
                        } else {
                            zoomLayoutOld.setScaleAndTransitionXY(sc, point)
                        }
                    }
                }
            }

            shapeProxyV1.registerListener(object : OnShapeListener() {
                override fun scaleAndTransition(scale: Float, point: PointF, animation: Boolean) {
                    showLog(
                        TAG,
                        "zoomLayout scaleAndTransition: scale = $scale , point = $point , animation = $animation"
                    )
                    zoomLayout.post {
                        val zoomWidth = resources.displayMetrics.widthPixels * 325 / 375
                        val sc = zoomWidth / (Shape.LINE_LENGTH_4_SQURA * 2 * 325 / 50f);
                        if (animation) {
                            zoomLayout.setScaleAndTranslationByAnimation(sc, point, 600)
                        } else {
                            zoomLayout.setScaleAndTransitionXY(sc, point)
                        }
                    }
                }
            })
            updateUI()
        }
    }

    private fun setH6063CubeShape(connectStateList: MutableList<CubeConnectStatusData> = mutableListOf()) {
        //0x03 0x10 0x01 0x02 0x12 0x01 0x08 0x11 0x01 0x00
        //0x03 0x10 0x01 0x02 0x12 0x01 0x02 0x11 0x01 0x00
        //0x03 0x10 0x01 0x02 0x12 0x01 0x08 0x11 0x01 0x00
        //0x03 0x10 0x01 0x02 0x11 0x01 0x02 0x12 0x01 0x00 长方形 - 三角形（左边）- 正方形 -
        //val connectStateList = mutableListOf<CubeConnectStatusData>()
        //[6, 18, 1, 2, 16, 1, 2, 16, 1, 2, 17, 1, 2, 18, 1, 2, 17, 1, 2, 18, 1, 2]
        //[10,16,1,2,16,1,2,17,1,6,16,1,0,16,1,2,16,1,2,17,1,2,16,1,2,17,1,4,16,1,0]
        val values2 =
            intArrayOf(10, 16, 1, 2, 16, 1, 2, 17, 1, 6, 16, 1, 0, 16, 1, 2, 16, 1, 2, 17, 1, 2, 16, 1, 2, 17, 1, 4, 16, 1, 0)
        //val values1 = intArrayOf(9, 16, 1, 2, 18, 1, 14, 16, 1, 0, 18, 1, 14, 16, 1, 0, 16, 1, 2, 16, 1, 0, 16, 1, 0, 16, 1, 0)
//        val values = intArrayOf(-119,32,1,4,32,2,8,32,8,2,32,2,8,32,2,8,32,1,4,32,2,8,32,8,2,32,2,8,32,8,2,32,1,4,32,2,8,32,8,2,32,8,2,32,8,2,32,8,2,32,8,2,32,4,1,32,2,8,32,2,8,32,2,8,32,8,2,32,4,1,32,8,2,32,2,8,32,8,2,32,8,2,32,1,8,32,2,1,32,1,4,32,2,8,32,1,4,32,8,2,32,2,8,32,2,8,32,1,4,32,1,4,32,4,1,32,1,4,32,2,8,32,1,4,32,8,2,32,8,2,32,2,8,32,1,4,32,1,4,32,4,1,32,8,2,32,8,2,32,8,2,32,4,1,32,4,1,32,8,2,32,4,1,32,2,8,32,8,1,32,4,8,32,8,2,32,8,2,32,8,2,32,1,4,32,1,4,32,1,4,32,1,4,32,1,4,32,1,4,32,1,4,32,2,8,32,2,8,32,1,4,32,1,4,32,2,8,32,2,8,32,1,4,32,1,4,32,4,1,32,1,4,32,8,2,32,8,2,32,4,1,32,4,1,32,2,8,32,8,4,32,8,4,32,1,4,32,1,4,32,4,1,32,2,8,32,1,4,32,8,2,32,1,4,32,4,1,32,8,2,32,4,1,32,8,2,32,4,1,32,8,2,32,4,1,32,2,8,32,8,2,32,8,2,32,8,2,32,8,2,32,8,2,32,4,1,32,8,2,32,2,8,32,2,8,32,1,4,32,1,4,32,1,2,32,2,4,32,4,1,32,2,8,32,4,1,32,4,1,32,1,4,32,2,8,32,4,1,32,4,1,32,4,1,32,2,8,32,8,2,32,1,4,32,1,4,32,1,4,32,4,1,32,4,1,32,1,4,32,1,4,32,1,4,32,8,2,32,1,4,32,2,8,32,8,2,32,1,0)// 1 3 2 4 4 2 4 2
//        val values = intArrayOf(15,32,1,14,32,4,2,32,2,0,32,2,8,32,4,9,32,4,9,32,1,10,32,4,0,32,8,0,32,4,0,32,2,0,32,2,4,32,2,8,32,1,4,32,8,0)
        val values =
            intArrayOf(
                90,
                32,
                1,
                4,
                32,
                8,
                2,
                32,
                8,
                2,
                32,
                2,
                8,
                32,
                4,
                1,
                32,
                8,
                4,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                1,
                4,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                8,
                2,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                1,
                4,
                32,
                4,
                1,
                32,
                4,
                1,
                32,
                4,
                1,
                32,
                4,
                1,
                32,
                8,
                4,
                32,
                2,
                8,
                32,
                4,
                1,
                32,
                4,
                1,
                32,
                8,
                2,
                32,
                8,
                4,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                1,
                4,
                32,
                8,
                2,
                32,
                8,
                2,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                1,
                4,
                32,
                1,
                4,
                32,
                8,
                2,
                32,
                8,
                4,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                1,
                8,
                32,
                8,
                2,
                32,
                2,
                8,
                32,
                4,
                1,
                32,
                8,
                2,
                32,
                1,
                4,
                32,
                8,
                2,
                32,
                1,
                4,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                4,
                2,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                2,
                1,
                32,
                1,
                4,
                32,
                8,
                2,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                1,
                4,
                32,
                2,
                8,
                32,
                1,
                4,
                32,
                1,
                4,
                32,
                1,
                4,
                32,
                2,
                8,
                32,
                4,
                2,
                32,
                2,
                8,
                32,
                1,
                8,
                32,
                4,
                1,
                32,
                1,
                4,
                32,
                4,
                1,
                32,
                1,
                4,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                8,
                4,
                32,
                2,
                1,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                8,
                2,
                32,
                4,
                1,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                2,
                8,
                32,
                8,
                2,
                32,
                1,
                0
            )
//        val values2 = intArrayOf(
//            16,
//            32,
//            1,
//            12,
//            32,
//            1,
//            14,
//            32,
//            2,
//            0,
//            32,
//            4,
//            11,
//            32,
//            1,
//            10,
//            32,
//            8,
//            6,
//            32,
//            2,
//            0,
//            32,
//            2,
//            0,
//            32,
//            2,
//            12,
//            32,
//            2,
//            0,
//            32,
//            1,
//            0,
//            32,
//            2,
//            0,
//            32,
//            4,
//            1,
//            32,
//            2,
//            0,
//            32,
//            1,
//            0,
//            32,
//            1,
//            0
//        )
//        val values = intArrayOf(6,32,1,10,32,2,8,32,8,0,32,2,8,32,8,2,32,2,0)
        //((it.outputEdge - inputEdge) * 90 + (360 - (it.outputEdge - 1) * 90) + firstAngle).checkAngle()
        var pos = 0
        val values1 = intArrayOf(
            0x05,

            0x20,
            0x01,
            0x0E,

            0x20,
            0x01,
            0x00,

            0x20,
            0x01,
            0x00,


            0x20,
            0x01,
            0x04,


            0x20,
            0x01,
            0x00,
        )
        if (connectStateList.isEmpty()) {
            val size: Int = values[pos++] and 0xFF

            for (i in 0 until size) {
                val type: Int = values[pos++]
                val input: Byte = values[pos++].toByte()
                val output: Byte = values[pos++].toByte()
                connectStateList.add(CubeConnectStatusData(type, input, output))
            }
        }
        val check = binding.etContent.text.toString()
        if (check.isNotEmpty()) {
            check.split(",").let {
                if (it.isNotEmpty()) {
                    Shape.useSoftLineNum.clear()
                    it.forEach {
                        if (it.isNotEmpty()) {
                            if (it.toInt() < connectStateList.size) {
                                Shape.useSoftLineNum.add(it.toInt())
                            }
                        }
                    }
                }
            }
        }


        val shapesByStream4H6063 =
            Shape.getShapesByStreamSoftLine4H6069(this, PanelLampType.SQUARE, 180, connectStateList, true, useSoftLineNum = Shape.useSoftLineNum)
//        val shapesByStream4H6063 =
//            Shape.getShapesByStream4H6063(this, PanelLampType.GAME_WALL, 45, connectStateList)

        showLog(TAG, "onCreate: 准备调用 setRegionIds, regionIds=${shapesByStream4H6063.third}")
        binding.canvasLayout.setRegionIds(shapesByStream4H6063.third)
        showLog(TAG, "onCreate: ✅ setRegionIds 调用完成")
        
        //showLog("onCreate:对比数据--4  shapes = $shapesByStream4H6063 ")
        val checkH6063Points = Shape.checkH6063Points(shapesByStream4H6063.first)
        val last = PointF(0f, 0f)
        checkH6063Points.forEach {
            val difX = ceil(abs(it.x - last.x))
            val difY = ceil(abs(it.y - last.y))
            //showLog("-----it.x = ${it.x} , it.y = ${it.y}  difX = $difX , difY = $difY")
            last.x = it.x
            last.y = it.y
        }
        //showLog("onCreate:对比数据--5 shapes = $checkH6063Points")

        binding.run {
            canvasLayout.setCubeType(PanelLampType.SQUARE).setModeType(CanvasLayoutModeType.SoftLine)
                .setShapeData(checkH6063Points)
        }
    }

    private fun initFocusMode() {
        with(binding) {
            canvasLayoutOld.focusDragCallback = object : CanvasLayout.OnFocusDragChange {
                override fun focusDragPointChange(point: PointF) {
                    showLog(TAG, "old focusDragPointChange: point = $point ")
                }

                override fun selectedFocusPoint(params: ByteArray) {
                    showLog(TAG, "selectedFocusPoint() params = $params")
                }
            }
            shapeProxyV1.registerListener(object : OnShapeListener() {
                override fun focusDragPointChange(point: PointF) {
                    showLog(TAG, "new focusDragPointChange: point = $point ")
                }

                override fun selectedFocusPoint(params: ByteArray) {
                    showLog(TAG, "selectedFocusPoint: params = $params ")
                }
            })
            val x = 1600f
            btnOut.clickNoRepeat {
                canvasLayoutOld.setSelectPoint(PointF(x, x), false)
                shapeProxyV1.updateFocusPoint(PointF(x, x), false)
            }
            btnIn.clickNoRepeat {
                canvasLayoutOld.setSelectPoint(PointF(x, x), true)
                shapeProxyV1.updateFocusPoint(PointF(x, x), true)
            }
            btnCenter.clickNoRepeat {
//                val yShapeDefaultFocusPoint = canvasLayoutOld.getYShapeDefaultFocusPoint()
                val yShapeDefaultFocusPoint1 = shapeProxyV1.getYShapeDefaultFocusPoint()
//                canvasLayoutOld.updateFocusPoint4YShape(yShapeDefaultFocusPoint)
                canvasLayout.updateFocusPoint4YShape(yShapeDefaultFocusPoint1)
            }
        }
    }


    private fun initColorMode() {
        var oldSelectStates = ArrayList<Boolean>()
        val oldBrightnessList = ArrayList<Int>()
        val oldColorList = ArrayList<Int>()

        var newSelectStates = ArrayList<Boolean>()
        val newBrightnessList = ArrayList<Int>()
        val newColorList = ArrayList<Int>()
        var oldSelectedAll = false
        var newSelectedAll = false
        with(binding) {
            canvasLayoutOld.colorModelCallback = object : CanvasLayout.OnSelectShapeChange {
                override fun selectedShape(selectedShapes: ArrayList<Boolean>) {
                    oldSelectStates = selectedShapes
                    oldSelectedAll = oldSelectStates.contains(true)
                }
            }
            shapeProxyV1.registerListener(object : OnShapeListener() {
                override fun selectedShape(selectedShapes: ArrayList<Boolean>) {
                    // 外部需要根据此参数刷新数据
                    showLog(TAG, "selectedShape: selectedShapes = $selectedShapes ")
                    newSelectStates = selectedShapes
                    newSelectedAll = newSelectStates.contains(true)
                }
            })
            canvasLayoutOld.shapeViews.run {

                onEach {
                    oldSelectStates.add(false)
                    oldBrightnessList.add(0)
                    oldColorList.add(Color.WHITE)
                }
            }
            shapeProxyV1.getShapeRotations().run {

                onEach {
                    newSelectStates.add(false)
                    newBrightnessList.add(0)
                    newColorList.add(Color.WHITE)
                }
            }

            seekbar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?, progress: Int, fromUser: Boolean
                ) {
                    showLog(TAG, "onProgressChanged: progress = $progress ")
                    oldSelectStates.onEachIndexed { index, b ->
                        if (b) {
                            oldBrightnessList[index] = progress
                        }
                    }
                    newSelectStates.onEachIndexed { index, b ->
                        if (b) {
                            newBrightnessList[index] = progress
                        }
                    }

                    shapeProxyV1.setColorParams(newColorList, newBrightnessList, newSelectStates)
                    canvasLayoutOld.setColorParams(oldColorList, oldBrightnessList, oldSelectStates)
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                }

            })

            btnSelectedAll.clickNoRepeat {
                oldSelectedAll = !oldSelectedAll
                newSelectedAll = !newSelectedAll

                shapeProxyV1.setSelectAll(newSelectedAll)
                canvasLayoutOld.setSelectAll(oldSelectedAll)

                for (i in 0 until oldSelectStates.size) {
                    oldSelectStates[i] = oldSelectedAll
                }
                for (i in 0 until newSelectStates.size) {
                    newSelectStates[i] = newSelectedAll
                }

                btnSelectedAll.text = if (newSelectedAll) "全不选" else "全选"
            }
            btnSelectedAll.text = if (newSelectedAll) "全不选" else "全选"

            btnChangeColor.clickNoRepeat {
                // 切换颜色
                showLog(TAG, "onCreate: 切换颜色 =  ")
                val color =
                    Color.argb(255, Random.nextInt(256), Random.nextInt(256), Random.nextInt(256))

                newSelectStates.forEachIndexed { index, b ->
                    if (b) {
                        newColorList[index] = color
                    }
                }

                oldSelectStates.forEachIndexed { index, b ->
                    if (b) {
                        oldColorList[index] = color
                    }
                }

                shapeProxyV1.setColorParams(newColorList, newBrightnessList, newSelectStates)
                canvasLayoutOld.setColorParams(oldColorList, oldBrightnessList, oldSelectStates)
            }
        }
    }

    private fun updateShapeType() {
        shapeTypeIndex++
        if (shapeTypeIndex >= shapeTypes.size) {
            shapeTypeIndex = 0
        }
        val shapeType = shapeTypes[shapeTypeIndex]
        resetImportData(shapeType.type, shapeType.angle)
    }

    private fun updateUI() {
        val new = newModeTypes[modeTypeIndex]
        with(binding) {
            setViewsVisible(
                false,
                editModeLayout,
                installModeLayout,
                checkModeLayout,
                colorModeLayout,
                focusModeLayout,
                moveFeastLayout,
                constraintGraffiti
            )
            when (new.type) {
                CanvasLayoutModeType.DefaultType -> {}
                CanvasLayoutModeType.Install -> {
                    installModeLayout.visible()
                }

                CanvasLayoutModeType.Edit, CanvasLayoutModeType.AddLamp -> {
                    editModeLayout.visible()
                }

                CanvasLayoutModeType.Check -> {
                    checkModeLayout.visible()
                }

                CanvasLayoutModeType.ColorMode -> {
                    colorModeLayout.visible()
                }

                CanvasLayoutModeType.Focus -> {
                    focusModeLayout.visible()
                }

                CanvasLayoutModeType.MultiPower -> {}
                CanvasLayoutModeType.Preview -> {}
                CanvasLayoutModeType.MoveFeast -> {
                    moveFeastLayout.visible()
                }

                CanvasLayoutModeType.Graffiti -> {
                    constraintGraffiti.visible()
                }

                else -> {}
            }
            btnMode.text = new.name
            if (colorModeLayout.isVisible) {
                initColorMode()
            }
            if (focusModeLayout.isVisible) {
                initFocusMode()
            }
            if (moveFeastLayout.isVisible) {
                initMoveFeastMode()
            }
            if (constraintGraffiti.isVisible) {
                initGraffitiMode()
            }
        }
    }

    private var index = 0
    private fun initGraffitiMode() {
        binding.run {
            canvasLayout.registerListener(object : OnShapeListener() {
                override fun notifyGraffitiChangeListener() {
                    canvasLayout.getGraffitiColors()
                }
            })
            canvasLayout.setDefaultColor(canvasLayout.toColor(com.govee.ui.R.color.FFFFFFFF_30))
            index = 0
            val color = getDefColors()[index]
            canvasLayout.setGraffitiColor(color)
            btnClear.clickNoRepeat {
                canvasLayout.resetGraffiti()
            }
            btnRestore.clickNoRepeat {
                canvasLayout.onRestore()
            }
            btnRevoke.clickNoRepeat {
                canvasLayout.onRevoke()
            }
            btnEraser.clickNoRepeat {
                canvasLayout.eraser()
            }
            btnGraffitiChangeColor.clickNoRepeat {
                val defColors = getDefColors()
                if (index >= defColors.size) {
                    index = 0
                }
                val color = defColors[index]
                canvasLayout.setGraffitiColor(color)
                index++
            }
        }
    }

    var cubeMoveFeastData = CubeMoveFeastData()

    private fun initMoveFeastMode() {
        binding.run {
            btnLight.clickNoRepeat {
                cubeMoveFeastData.type = 0
                cubeMoveFeastData.color = com.govee.ui.R.color.FFFFFFFF
            }
            btnMiedeng.clickNoRepeat {
                cubeMoveFeastData.type = 255
                cubeMoveFeastData.color = com.govee.ui.R.color.FFCCCCCC
            }
            btnRandomColor.clickNoRepeat {
                cubeMoveFeastData.type = 119
                cubeMoveFeastData.color = com.govee.ui.R.color.FF26BD31
                cubeMoveFeastData.centerText = 119.toString()
            }

            canvasLayout.registerListener(object : OnShapeListener() {
                override fun getMoveFeastSelectedAreaMsg(): CubeMoveFeastData {
                    return cubeMoveFeastData
                }

                override fun notifyFeastChange(feastDataHashMap: HashMap<Int, MutableList<CubeMoveFeastData>>) {
                    super.notifyFeastChange(feastDataHashMap)
                }
            })
        }
    }

}

