# Cube Demo App Widget 使用说明

## 功能概述
这是一个 Android App Widget 实现，允许用户在桌面添加小组件，点击后快速启动应用并跳转到指定页面。

## 文件结构
```
app/src/main/java/com/govee/app/cubedemo/test/widget/
├── CubeDemoWidgetProvider.kt    # Widget Provider 类
├── WidgetTargetActivity.kt      # 目标 Activity
└── README.md                    # 使用说明

app/src/main/res/
├── layout/
│   ├── widget_cube_demo.xml     # Widget 布局文件
│   └── activity_widget_target.xml # 目标页面布局
├── drawable/
│   └── widget_background.xml    # Widget 背景样式
└── xml/
    └── cube_demo_widget_info.xml # Widget 配置文件
```

## 主要组件

### 1. CubeDemoWidgetProvider
- 继承自 AppWidgetProvider
- 处理 Widget 的生命周期事件
- 设置点击事件，跳转到目标页面

### 2. WidgetTargetActivity
- Widget 点击后的目标页面
- 显示成功启动的消息
- 提供返回主页的功能

### 3. Widget 布局
- 包含应用图标、标题和副标题
- 使用渐变背景和圆角设计
- 支持点击整个区域触发跳转

## 使用方法

1. 编译并安装应用
2. 长按桌面空白处，选择"小组件"
3. 找到 "Cube Demo" 小组件并添加到桌面
4. 点击小组件即可快速启动应用并跳转到目标页面

## 自定义说明

### 修改目标页面
在 `WidgetTargetActivity.kt` 中修改跳转逻辑：
```kotlin
val intent = Intent(this@WidgetTargetActivity, YourTargetActivity::class.java)
```

### 修改 Widget 样式
- 编辑 `widget_cube_demo.xml` 修改布局
- 编辑 `widget_background.xml` 修改背景样式
- 在 `strings.xml` 中修改文字内容

### 修改 Widget 尺寸
在 `cube_demo_widget_info.xml` 中调整：
```xml
android:minWidth="110dp"
android:minHeight="110dp"
```
