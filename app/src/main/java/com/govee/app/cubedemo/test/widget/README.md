# Cube Demo App Widget 使用说明

## 功能概述
这是一个 Android App Widget 实现，允许用户在桌面添加小组件，点击后先发起内部广播，然后快速启动应用并跳转到指定页面。

## 功能流程
1. 用户点击 Widget
2. Widget Provider 发起自定义广播
3. 内部广播接收器处理广播（记录日志、统计等）
4. 延迟启动目标 Activity
5. 目标页面显示相关信息

## 文件结构
```
app/src/main/java/com/govee/app/cubedemo/test/widget/
├── CubeDemoWidgetProvider.kt    # Widget Provider 类
├── WidgetInternalReceiver.kt    # 内部广播接收器
├── WidgetTargetActivity.kt      # 目标 Activity
└── README.md                    # 使用说明

app/src/main/res/
├── layout/
│   ├── widget_cube_demo.xml     # Widget 布局文件
│   └── activity_widget_target.xml # 目标页面布局
├── drawable/
│   └── widget_background.xml    # Widget 背景样式
└── xml/
    └── cube_demo_widget_info.xml # Widget 配置文件
```

## 主要组件

### 1. CubeDemoWidgetProvider
- 继承自 AppWidgetProvider
- 处理 Widget 的生命周期事件
- 处理点击事件，发起内部广播
- 延迟启动目标页面

### 2. WidgetInternalReceiver
- 内部广播接收器
- 处理 Widget 点击时的内部广播
- 记录点击日志和统计信息
- 执行业务逻辑（如数据更新、分析等）

### 3. WidgetTargetActivity
- Widget 点击后的目标页面
- 显示成功启动的消息和统计信息
- 显示 Widget ID 和点击次数
- 提供返回主页的功能

### 4. Widget 布局
- 包含应用图标、标题和副标题
- 使用渐变背景和圆角设计
- 支持点击整个区域触发跳转

## 使用方法

1. 编译并安装应用
2. 长按桌面空白处，选择"小组件"
3. 找到 "Cube Demo" 小组件并添加到桌面
4. 点击小组件会：
   - 发起内部广播（可在 Logcat 中查看日志）
   - 显示 Toast 提示
   - 启动目标页面并显示统计信息

## 广播机制

### 内部广播流程
1. **Widget 点击** → 发送 `WIDGET_CLICKED` 广播
2. **Widget Provider** → 接收并处理点击事件
3. **发起内部广播** → 发送 `INTERNAL_BROADCAST` 广播
4. **内部接收器** → 处理业务逻辑（日志、统计等）
5. **启动页面** → 延迟启动目标 Activity

### 广播 Action
- `com.govee.app.cubedemo.test.widget.WIDGET_CLICKED` - Widget 点击广播
- `com.govee.app.cubedemo.test.widget.INTERNAL_BROADCAST` - 内部处理广播

## 自定义说明

### 修改内部广播处理逻辑
在 `WidgetInternalReceiver.kt` 的 `performBusinessLogic()` 方法中添加自定义逻辑：
```kotlin
private fun performBusinessLogic(context: Context, widgetId: Int) {
    // 添加你的业务逻辑
    // 例如：发送网络请求、更新数据库、触发其他组件等
}
```

### 修改目标页面
在 `WidgetTargetActivity.kt` 中修改跳转逻辑：
```kotlin
val intent = Intent(this@WidgetTargetActivity, YourTargetActivity::class.java)
```

### 修改 Widget 样式
- 编辑 `widget_cube_demo.xml` 修改布局
- 编辑 `widget_background.xml` 修改背景样式
- 在 `strings.xml` 中修改文字内容

### 修改 Widget 尺寸
在 `cube_demo_widget_info.xml` 中调整：
```xml
android:minWidth="110dp"
android:minHeight="110dp"
```

## 调试说明

### 查看日志
使用 Logcat 过滤以下标签查看详细日志：
- `CubeDemoWidget` - Widget Provider 日志
- `WidgetInternalReceiver` - 内部广播接收器日志
- `WidgetTargetActivity` - 目标页面日志

### 测试广播
可以通过 ADB 命令手动发送广播进行测试：
```bash
adb shell am broadcast -a com.govee.app.cubedemo.test.widget.INTERNAL_BROADCAST --ei widget_id 1 --el timestamp **********
```
