package com.govee.app.cubedemo.test.widget

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.govee.app.cubedemo.R
import com.govee.app.cubedemo.test.webview.WebViewActivity

/**
 * Widget 点击后跳转的目标 Activity
 * 这是一个示例页面，展示小组件功能的实现
 */
class WidgetTargetActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "WidgetTargetActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_widget_target)

        setupViews()
        handleWidgetData()
    }
    
    private fun setupViews() {
        // 设置标题
        findViewById<TextView>(R.id.tv_title).text = getString(R.string.widget_target_title)

        // 设置返回按钮
        findViewById<Button>(R.id.btn_back).apply {
            text = getString(R.string.back_to_main)
            setOnClickListener {
                // 返回到主页面
                val intent = Intent(this@WidgetTargetActivity, WebViewActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                startActivity(intent)
                finish()
            }
        }
    }

    /**
     * 处理从 Widget 传递过来的数据
     */
    private fun handleWidgetData() {
        val widgetId = intent.getIntExtra(CubeDemoWidgetProvider.EXTRA_WIDGET_ID, -1)

        Log.d(TAG, "Target activity started from widget ID: $widgetId")

        // 获取点击次数
        val prefs = getSharedPreferences("widget_prefs", Context.MODE_PRIVATE)
        val clickCount = prefs.getInt("click_count_$widgetId", 0)

        // 更新消息显示
        val messageText = if (widgetId != -1) {
            "${getString(R.string.widget_target_message)}\n\n" +
            "Widget ID: $widgetId\n" +
            "点击次数: $clickCount"
        } else {
            getString(R.string.widget_target_message)
        }

        findViewById<TextView>(R.id.tv_message).text = messageText
    }
}
