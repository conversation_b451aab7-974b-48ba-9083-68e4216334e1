package com.govee.app.cubedemo.test.widget

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.govee.app.cubedemo.R
import com.govee.app.cubedemo.test.webview.WebViewActivity

/**
 * Widget 点击后跳转的目标 Activity
 * 这是一个示例页面，展示小组件功能的实现
 */
class WidgetTargetActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_widget_target)
        
        setupViews()
    }
    
    private fun setupViews() {
        // 设置标题
        findViewById<TextView>(R.id.tv_title).text = getString(R.string.widget_target_title)
        
        // 设置消息
        findViewById<TextView>(R.id.tv_message).text = getString(R.string.widget_target_message)
        
        // 设置返回按钮
        findViewById<Button>(R.id.btn_back).apply {
            text = getString(R.string.back_to_main)
            setOnClickListener {
                // 返回到主页面
                val intent = Intent(this@WidgetTargetActivity, WebViewActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                startActivity(intent)
                finish()
            }
        }
    }
}
