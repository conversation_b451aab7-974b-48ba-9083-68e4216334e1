package com.govee.app.cubedemo

import android.graphics.Color
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import java.util.Locale
import kotlin.math.ceil

/**
 *     author  : sinrow
 *     time    : 2023/12/14
 *     version : 1.0.0
 *     desc    :
 */
object CubeUtils {
    var writeBytes: ByteArray? = null
    var writeBytes4Server: ByteArray? = null

    var shapes: List<ShapePosition>? = null

    var extBytes: ByteArray? = null
    var mainPowerBytes: ByteArray? = null

    fun handleShapesStr(shapes: List<ShapePosition>?): String? {
        if (shapes.isNullOrEmpty()) return null
        this.shapes = shapes
        val size = shapes.size
        val byteLen = 3 + 4 * size
        writeBytes = ByteArray(byteLen)
        var startPos = 0
        /*用于发给设备数据*/
        val byteAngle: ByteArray = getSignedBytesFor2(shapes[0].angle, false)
        writeBytes?.set(startPos++, byteAngle[0])
        writeBytes?.set(startPos++, byteAngle[1])
        writeBytes?.set(startPos++, size.toByte())
        for (i in shapes.indices) {
            val shapePosition = shapes[i]
            val byteXPoint: ByteArray = getSignedBytesFor2(ceil(shapePosition.x.toDouble()).toInt(), false)
            writeBytes?.set(startPos++, byteXPoint[0])
            writeBytes?.set(startPos++, byteXPoint[1])
            val byteYPoint: ByteArray = getSignedBytesFor2(ceil(shapePosition.y.toDouble()).toInt(), false)
            writeBytes?.set(startPos++, byteYPoint[0])
            writeBytes?.set(startPos++, byteYPoint[1])
        }

        /*用于上传给到服务器*/
        val makeCmd = makeCmd(shapes)
        return Encode.encryptByBase64(makeCmd)
    }

    private fun makeCmd(shapes: List<ShapePosition>): ByteArray? {
        val size = shapes.size
        val byteLen = 1 + 7 * size
        var startPos = 0
        mainPowerBytes = byteArrayOf(0x00, 0x00)
        extBytes = byteArrayOf(0x00, 0x00)
        writeBytes4Server = ByteArray(byteLen)
        writeBytes4Server?.set(startPos++, size.toByte())
        val spliceMsg = ByteArray(size * 2)
        var spliceMsgStartPos = 0
        for (i in shapes.indices) {
            val shapePosition = shapes[i]
            writeBytes4Server?.set(startPos++, shapePosition.type.toByte())
            val byteXPoint: ByteArray = getSignedBytesFor2(ceil(shapePosition.x.toDouble()).toInt(), false)
            writeBytes4Server?.set(startPos++, byteXPoint[0])
            writeBytes4Server?.set(startPos++, byteXPoint[1])
            val byteYPoint: ByteArray = getSignedBytesFor2(ceil(shapePosition.y.toDouble()).toInt(), false)
            writeBytes4Server?.set(startPos++, byteYPoint[0])
            writeBytes4Server?.set(startPos++, byteYPoint[1])
            val byteAngle: ByteArray = getSignedBytesFor2(shapePosition.angle, false)
            writeBytes4Server?.set(startPos++, byteAngle[0])
            writeBytes4Server?.set(startPos++, byteAngle[1])
            if (shapePosition.isMainPower) {
                mainPowerBytes = shapePosition.mainPower.getBytes()
                showLog("", "makeCmd() mainPowerBytes = ${bytesToHexString(mainPowerBytes)}")
            }
            if (shapePosition.isExt) {
                extBytes = shapePosition.ext.getBytes()
                showLog("", "makeCmd() isExt extBytes =  ${bytesToHexString(extBytes)}")
            } else {
                extBytes = mainPowerBytes
            }
            spliceMsg[spliceMsgStartPos++] = shapePosition.inputNum.toByte()
            spliceMsg[spliceMsgStartPos++] = shapePosition.outputNum.toByte()
        }
        if (writeBytes4Server != null && mainPowerBytes != null) {
            writeBytes4Server = concat(writeBytes4Server!!, mainPowerBytes!!)
        }
        if (extBytes != null) {
            writeBytes4Server = concat(writeBytes4Server!!, extBytes!!)
        }
        writeBytes4Server = concat(writeBytes4Server!!, spliceMsg)
        return writeBytes4Server
    }


    private fun getSignedBytesFor2(value: Int, hFirst: Boolean): ByteArray {
        val src = ByteArray(2)
        if (hFirst) {
            src[0] = (value shr 8 and 0xFF).toByte()
            src[1] = (value and 0xFF).toByte()
        } else {
            src[0] = (value and 0xFF).toByte()
            src[1] = (value shr 8 and 0xFF).toByte()
        }
        return src
    }

    private fun concat(bytes1: ByteArray, bytes2: ByteArray): ByteArray {
        val bytes3 = ByteArray(bytes1.size + bytes2.size)
        System.arraycopy(bytes1, 0, bytes3, 0, bytes1.size)
        System.arraycopy(bytes2, 0, bytes3, bytes1.size, bytes2.size)
        return bytes3
    }

    private fun bytesToHexString(src: ByteArray?): String {
        val stringBuilder = StringBuilder()
        if (src == null || src.isEmpty()) {
            return "null"
        }
        for (aSrc in src) {
            val v = aSrc.toInt() and 0xFF
            val hv = Integer.toHexString(v)
            if (hv.length < 2) {
                stringBuilder.append(0)
            }
            stringBuilder.append(hv)
        }
        return stringBuilder.toString().uppercase(Locale.getDefault())
    }

    @JvmStatic
    fun toColor(r: Int, g: Int, b: Int): Int {
        val rNew: Int = 0.coerceAtLeast(r.coerceAtMost(255))
        val gNew: Int = 0.coerceAtLeast(g.coerceAtMost(255))
        val bNew: Int = 0.coerceAtLeast(b.coerceAtMost(255))
        return Color.argb(255, rNew, gNew, bNew)
    }
}