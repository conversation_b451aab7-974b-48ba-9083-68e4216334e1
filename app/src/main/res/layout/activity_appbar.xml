<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:orientation="vertical"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll"
            >

            <!-- 其他吸顶内容 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="200dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/includedSortLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:tag="sticky"
            app:layout_scrollFlags="exitUntilCollapsed"
            app:layout_constraintTop_toBottomOf="@+id/recycleHead"
            >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleCircle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layout_constraintEnd_toStartOf="@+id/ivMoreCircle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivMoreCircle"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginEnd="12dp"
                android:src="@mipmap/add_list_type_device_61a0"
                app:layout_constraintBottom_toBottomOf="@+id/recycleCircle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/recycleCircle"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        >

        <!-- 内容列表或其他内容 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <!-- 示例内容填充 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:text="内容区"
                android:textSize="24sp"
                />

            <!-- 模拟长内容 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1000dp"
                />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>