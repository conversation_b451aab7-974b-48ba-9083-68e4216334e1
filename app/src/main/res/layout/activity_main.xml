<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <com.govee.cubeview.ZoomLayout
        android:id="@+id/zoomLayout"
        android:layout_width="wrap_content"
        android:layout_height="360dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

        <com.govee.cubeview.CanvasLayoutV3
            android:id="@+id/canvasLayout"
            android:layout_width="3200px"
            android:layout_height="3200px"
            android:layout_gravity="center"
            app:cubeShapeViewType="compose"
            />

    </com.govee.cubeview.ZoomLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:background="@color/FF00ACE7"
        android:text="CanvasLayoutV2"
        android:textSize="13sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/zoomLayout"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/zoomLayoutOld"
        />

    <com.govee.cubeview.ZoomLayout
        android:id="@+id/zoomLayoutOld"
        android:layout_width="wrap_content"
        android:layout_height="360dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/zoomLayout"
        >

        <com.govee.cubeview.CanvasLayout
            android:id="@+id/canvasLayoutOld"
            android:layout_width="3200px"
            android:layout_height="3200px"
            android:layout_gravity="center"
            app:cube_shape_view_type="hexagon"
            />

    </com.govee.cubeview.ZoomLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="CanvasLayout"
        android:textSize="13sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/zoomLayoutOld"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/edit_mode_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        >

        <Button
            android:id="@+id/btn_change"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换图形"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <Button
            android:id="@+id/btn_change_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换模式"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_change"
            />

        <Button
            android:id="@+id/btn_import"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="导入数据"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_change_mode"
            />

        <Button
            android:id="@+id/btn_fanzhuan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="翻转"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <Button
            android:id="@+id/btn_xuanzhuan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="旋转"
            app:layout_constraintLeft_toLeftOf="@+id/btn_fanzhuan"
            app:layout_constraintTop_toBottomOf="@+id/btn_fanzhuan"
            />

        <Button
            android:id="@+id/btn_chongzhi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重置"
            app:layout_constraintLeft_toLeftOf="@+id/btn_xuanzhuan"
            app:layout_constraintTop_toBottomOf="@+id/btn_xuanzhuan"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/installModeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <Button
            android:id="@+id/btnInstallNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="下一个"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <Button
            android:id="@+id/btnInstallLast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上一个"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnInstallNext"
            />

        <Button
            android:id="@+id/btnInstallAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全部"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnInstallLast"
            />

        <Button
            android:id="@+id/btnAddLamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="加装"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/checkModeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <Button
            android:id="@+id/btnCheckNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="下一个"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <Button
            android:id="@+id/btnCheckLast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上一个"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnCheckNext"
            />

        <Button
            android:id="@+id/btnCheckAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全部"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnCheckLast"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/colorModeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <SeekBar
            android:id="@+id/seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:max="100"
            android:progress="0"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <Button
            android:id="@+id/btnChangeColor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="随机颜色"
            app:layout_constraintBottom_toTopOf="@+id/seekbar"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnSelectedAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="全选"
            app:layout_constraintBottom_toTopOf="@+id/btnChangeColor"
            app:layout_constraintLeft_toLeftOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/focusModeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <Button
            android:id="@+id/btnOut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="聚拢"
            app:layout_constraintBottom_toTopOf="@+id/btnIn"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnIn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="扩散"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnCenter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="默认中心点"
            app:layout_constraintBottom_toTopOf="@+id/btnIn"
            app:layout_constraintRight_toRightOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintGraffiti"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <Button
            android:id="@+id/btnGraffitiChangeColor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="改变颜色"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnRevoke"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="撤销"
            app:layout_constraintBottom_toTopOf="@+id/btnGraffitiChangeColor"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnRestore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="还原"
            app:layout_constraintBottom_toTopOf="@+id/btnRevoke"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="清空"
            app:layout_constraintBottom_toTopOf="@+id/btnRestore"
            app:layout_constraintLeft_toLeftOf="parent"
            />

        <Button
            android:id="@+id/btnEraser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="橡皮擦"
            app:layout_constraintBottom_toTopOf="@+id/btnClear"
            app:layout_constraintLeft_toLeftOf="parent"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btnMode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="颜色模式"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2"
        />

    <EditText
        android:id="@+id/etContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnMode"
        app:layout_constraintVertical_bias="0.2"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/moveFeastLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <Button
            android:id="@+id/btn_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="主颜色"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <Button
            android:id="@+id/btn_miedeng"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="熄灯"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_light"
            />

        <Button
            android:id="@+id/btn_random_color"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="随机颜色"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_miedeng"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>