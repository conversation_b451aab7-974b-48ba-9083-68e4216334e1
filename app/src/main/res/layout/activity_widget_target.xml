<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="32dp"
        android:contentDescription="@string/widget_icon_description" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/widget_target_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/widget_target_message"
        android:textSize="16sp"
        android:textColor="#666666"
        android:layout_marginBottom="48dp"
        android:gravity="center"
        android:lineSpacingExtra="4dp" />

    <Button
        android:id="@+id/btn_back"
        android:layout_width="200dp"
        android:layout_height="48dp"
        android:text="@string/back_to_main"
        android:textSize="16sp"
        android:background="@drawable/widget_background"
        android:textColor="@color/white"
        android:elevation="4dp" />

</LinearLayout>
