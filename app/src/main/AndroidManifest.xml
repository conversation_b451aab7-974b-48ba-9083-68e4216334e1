<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.govee.app.cubedemo"
    >

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:theme="@style/Theme.MyApplication"
        android:name=".test.webview.App"
        android:networkSecurityConfig="@xml/app_network_security_config"
        tools:targetApi="31"
        >
        <!-- AutoSize使用 -->
        <meta-data
            android:name="design_width_in_dp"
            android:value="375"
            />
        <meta-data
            android:name="design_height_in_dp"
            android:value="667"
            />

        <activity
            android:name="com.govee.app.cubedemo.test.webview.WebViewActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.MyApplication"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".test.TestAc"
            android:hardwareAccelerated="true"
            android:launchMode="standard"
            android:exported="false"
            android:theme="@style/Theme.Transparent"
            />
        <activity
            android:name=".test.TestAc2"
            android:hardwareAccelerated="true"
            android:theme="@style/Theme.Transparent"
            />

        <!-- Widget Target Activity -->
        <activity
            android:name=".test.widget.WidgetTargetActivity"
            android:exported="true"
            android:theme="@style/Theme.MyApplication"
            android:launchMode="singleTop" />

        <!-- App Widget Provider -->
        <receiver
            android:name=".test.widget.CubeDemoWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.govee.app.cubedemo.test.widget.WIDGET_CLICKED" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/cube_demo_widget_info" />
        </receiver>

        <!-- Widget Internal Broadcast Receiver -->
        <receiver
            android:name=".test.widget.WidgetInternalReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.govee.app.cubedemo.test.widget.INTERNAL_BROADCAST" />
            </intent-filter>
        </receiver>

        <!--<activity android:name=".test.webview.WebViewActivity" />-->
    </application>

</manifest>