package com.govee.app.cubedemo

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.govee.app.cubedemo", appContext.packageName)
    }

    @Test
    fun main() {
        val array1 = booleanArrayOf(true, false, true)
        val array2 = booleanArrayOf(false, true, false)

        // 使用+运算符合并
        val mergedArray1 = array1 + array2

        // 使用concat函数合并
        val mergedArray2 = booleanArrayOf(*array1, *array2)

        println(mergedArray1.contentToString()) // 输出合并后的数组
        println(mergedArray2.contentToString()) // 输出合并后的数组
    }
}