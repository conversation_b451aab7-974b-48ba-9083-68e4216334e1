# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
##-renamesourcefileattribute SourceFile

#--------------------------1.实体类---------------------------------
# 如果使用了Gson之类的工具要使被它解析的JavaBean类即实体类不被混淆。（这里填写自己项目中存放bean对象的具体路径）

#--------------------------2.第三方包-------------------------------
#noinspection ShrinkerUnresolvedReference
# Gson
#-keepattributes Signature
#-keepattributes *Annotation*
#-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }
#-keep class com.google.gson.examples.android.model.** { *; }
#-keep class com.google.gson.* { *;}
#-dontwarn com.google.gson.**
#
## butterknife
#-keep class butterknife.** { *; }
#-dontwarn butterknife.internal.**
#-keep class **$$ViewBinder { *; }
#
## EventBus
#-keepclassmembers class * {
#    @org.greenrobot.eventbus.Subscribe <methods>;
#}
#-keep enum org.greenrobot.eventbus.ThreadMode { *; }
#
## Only required if you use AsyncExecutor
#-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
#    <init>(java.lang.Throwable);
#}
#
##-------------------------3.与js互相调用的类------------------------
#
#
##-------------------------4.反射相关的类和方法----------------------
#
#
##-------------------------5.基本不用动区域--------------------------
## 指定代码的压缩级别
#-optimizationpasses 5
#
## 包明不混合大小写
#-dontusemixedcaseclassnames
#
## 不去忽略非公共的库类
##-dontskipnonpubliclibraryclasses
##-dontskipnonpubliclibraryclassmembers
#
## 混淆时是否记录日志
#-verbose
#
## 优化  不优化输入的类文件
#-dontoptimize
#
## 预校验
##-dontpreverify
#
## 保留sdk系统自带的一些内容 【例如：-keepattributes *Annotation* 会保留Activity的被@override注释的onCreate、onDestroy方法等】
#-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
#
## 记录生成的日志数据,gradle build时在本项根目录输出
## apk 包内所有 class 的内部结构
##-dump proguard/class_files.txt
## 未混淆的类和成员
#-printseeds proguard/seeds.txt
## 列出从 apk 中删除的代码
#-printusage proguard/unused.txt
## 混淆前后的映射
#-printmapping proguard/mapping.txt
#
#
## 避免混淆泛型
#-keepattributes Signature
## 抛出异常时保留代码行号,保持源文件以及行号
#-keepattributes SourceFile,LineNumberTable
#
##-----------------------------6.默认保留区-----------------------
## 保持 native 方法不被混淆
#-keepclasseswithmembernames class * {
#    native <methods>;
#}
#
#-keepclassmembers public class * extends android.view.View {
# public <init>(android.content.Context);
# public <init>(android.content.Context, android.util.AttributeSet);
# public <init>(android.content.Context, android.util.AttributeSet, int);
# public void set*(***);
#}
#
## 保持 Serializable 不被混淆
#-keepclassmembers class * implements java.io.Serializable {
#    static final long serialVersionUID;
#    private static final java.io.ObjectStreamField[] serialPersistentFields;
#    !static !transient <fields>;
#    !private <fields>;
#    !private <methods>;
#    private void writeObject(java.io.ObjectOutputStream);
#    private void readObject(java.io.ObjectInputStream);
#    java.lang.Object writeReplace();
#    java.lang.Object readResolve();
#}
#
## 保持自定义控件类不被混淆
#-keepclassmembers class * extends android.app.Activity {
#    public void *(android.view.View);
#}
#
## 保持枚举 enum 类不被混淆
#-keepclassmembers enum * {
#    public static **[] values();
#    public static ** valueOf(java.lang.String);
#}
#
## 保持 Parcelable 不被混淆
#-keep class * implements android.os.Parcelable {
#  public static final android.os.Parcelable$Creator *;
#}
#
## 不混淆R文件中的所有静态字段，我们都知道R文件是通过字段来记录每个资源的id的，字段名要是被混淆了，id也就找不着了。
#-keepclassmembers class **.R$* {
#    public static <fields>;
#}
#
##Annotation
#-keep public class * implements java.lang.annotation.Annotation
#
## v4、v7包
#-dontwarn android.support.**
#
## 保持组件类名不被混淆，否则AndroidManifest找不到
#-keep  class * extends android.app.Appliction
#-keep  class * extends android.app.Activity
#-keep  class * extends android.app.Fragment
#-keep  class * extends android.app.Service
#-keep  class * extends android.content.BroadcastReceiver
#-keep  class * extends android.content.ContentProvider
#-keep  class * extends android.preference.Preference
#
## 保持Goveehome base2app不变
#-keep class com.ihoment.base2app.** {*;}
#-keep interface com.ihoment.base2app.** {*;}
## 保持Goveehome socketlink不变
#-keep class com.govee.home.socketlink.** {*;}
#-keep interface com.govee.home.socketlink.** {*;}
#
## 保持Goveehome app 混淆规则
## 保持Goveehome app中接口类名不变
#-keep interface com.govee.home.**
##-keep interface com.govee.**{*;}
#
## 保持io.objectbox不变
#-keep class io.objectbox.** {*;}
#-keep interface io.objectbox.** {*;}
#
##被KeepNoProguard注释的类，方法，构造，属性都不混淆
#-keep @com.ihoment.base2app.KeepNoProguard class * {*;}
#-keep class * {
#    @com.ihoment.base2app.KeepNoProguard <fields>;
#}
#-keepclassmembers class * {
#    @com.ihoment.base2app.KeepNoProguard <methods>;
#}
#
##基础网络请求响应类不被混淆
#-keep class * extends com.ihoment.base2app.network.BaseResponse {*;}
#-keep class * extends com.ihoment.base2app.network.BaseRequest {*;}
#
##AbsConfig类不被混淆
#-keep class * extends com.ihoment.base2app.infra.AbsConfig{*;}
#
## ============忽略警告，否则打包可能会不成功=============
#-ignorewarnings
#
##okhttp
#-dontwarn okhttp3.**
#-keep class okhttp3.**{*;}
#-keep interface okhttp3.**{*;}
#
##okio
#-dontwarn okio.**
#-keep class okio.**{*;}
#-keep interface okio.**{*;}
#
##RxJava
#-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
#    long producerIndex;
#    long consumerIndex;
#}
#
#-dontwarn com.baidu.**
#-keep class com.baidu.**{*;}
#-keep interface com.baidu.**{*;}
#
##androidx
#-keep class com.google.android.material.**{*;}
#-keep class androidx.**{*;}
#-keep class * extends androidx.**
#-keep interface androidx.**{*;}
#-keepclassmembers class *{
#    @androidx.annotation.Keep *;
#}
#
#-keep class cn.icomon.**{*;}
#
## Android data binding
#-dontwarn androidx.databinding.**
#-keep class androidx.databinding.** { *; }
#
## Android data binding
#-dontwarn com.xxxx.xx.databinding.**
#-keep class com.xxxx.xx.databinding.** { *; }
#-keepclassmembers class com.xxxx.xx.databinding.** { *; }
#
#-keep class com.xxxx.xx.BindingHelpers.** { *; }
#-keepclassmembers class com.xxxx.xx.BindingHelpers.** { *; }
#-keep class com.xxxx.xx.DataBinderMapperImpl { *; }
#-keepclasseswithmembernames class android.support.design.widget.TabLayout {
#    *;
#}
#
## BaseQuickAdapter
#-keep class com.chad.library.adapter.** {
#*;
#}
#-keep class * extends com.chad.library.adapter.base.BaseQuickAdapter
#-keep class * extends com.chad.library.adapter.base.viewholder.BaseViewHolder
#-keep class * extends com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
#-keepclassmembers  class **$** extends com.chad.library.adapter.base.viewholder.BaseViewHolder {
#     <init>(...);
#}
#
#-keep class com.govee.home.main.square.colourway.weekly.net.** { *; }
#-keep class com.govee.nfc.net.** { *; }
#-keep class com.govee.bulblightv3.multi.net.** { *; }
#
#-keep class com.qingniu.scale.model.BleScaleData{*;}
#
##ARouter相关去混淆
#-keep public class com.alibaba.android.arouter.routes.**{*;}
#-keep public class com.alibaba.android.arouter.facade.**{*;}
#-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}
## 如果使用了 byType 的方式获取 Service，需添加下面规则，保护接口
#-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider
## 如果使用了 单类注入，即不定义接口实现 IProvider，需添加下面规则，保护实现
## -keep class * implements com.alibaba.android.arouter.facade.template.IProvider