plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 34

    defaultConfig {
        applicationId "com.govee.app.cubedemo"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {

    implementation 'androidx.test.ext:junit-ktx:1.1.5'
    testImplementation 'androidx.test:core:1.0.0'
    testImplementation 'junit:junit:4.12'

    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0-beta01'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.4'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.4'

    implementation project(path: ':ui')
    implementation project(path: ':cubeview')
    androidTestImplementation 'org.testng:testng:6.9.6'
//    implementation project(path: ':cubeviewV2')
    api 'com.github.JessYanCoding:AndroidAutoSize:v1.2.1'
    api 'com.github.jadepeakpoet.ARouter:arouter-api:1.0.3'

    /*网络Lib 版本需要保持一致;*/
    api 'com.squareup.okhttp3:okhttp:4.10.0'
    api 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'

}