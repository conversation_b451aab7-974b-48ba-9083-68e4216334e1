package com.govee.cubeview.view

import android.content.Context
import com.govee.ui.R

/**
 * Create by lv<PERSON><PERSON><PERSON> on 2021/7/5 11:49
 * 添加shape按钮
 */
internal class ImgAddShapeView(context: Context) : BaseAssistView(context) {

    companion object {
        //边长
        const val SIZE_ADD = 38
    }

    //中心点相对于父布局位置
    var directionTag = 0//方向，绝对角度

    init {
        sizeWith = SIZE_ADD
        sizeHeight = SIZE_ADD
        init()
    }

    fun init() {
        maxWidth = sizeWith
        maxHeight = sizeHeight
        scaleType = ScaleType.FIT_CENTER
        setImageResource(R.mipmap.new_btn_6061_add_bian)
    }

    override fun doSomething(block: () -> Unit) {
        setOnClickListener {
            block()
        }
    }
}