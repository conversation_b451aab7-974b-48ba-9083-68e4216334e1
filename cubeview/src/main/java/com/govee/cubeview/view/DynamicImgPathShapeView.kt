package com.govee.cubeview.view

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import com.govee.cubeview.showLog
import com.govee.ui.R

/**
 * Create by l<PERSON><PERSON><PERSON><PERSON> on 2021/7/5 11:49
 * 路径图形
 */
class DynamicImgPathShapeView(context: Context) : ImgPathShapeView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 17//边长
        const val SIZE_HEIGHT = 40//边长

    }

    //中心点相对于父布局位置，不是真的图形的中心点，是映射成六边形的中心点
    private var mRotation = 0f//方向，绝对角度

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    override fun init() {
        scaleType = ScaleType.CENTER_INSIDE
        rotation = mRotation
        setImageResource(R.mipmap.new_light_6065_pics_buxian_start)
    }

    override fun setData(angle: Float, numText: Int) {
        this.mRotation = angle
        rotation = mRotation
    }

    override fun onDraw(canvas: Canvas) {
        numText = -1
        super.onDraw(canvas)
    }

}