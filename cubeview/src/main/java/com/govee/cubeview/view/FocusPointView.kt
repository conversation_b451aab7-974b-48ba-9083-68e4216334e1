package com.govee.cubeview.view

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.View
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/7/21
 *     version : 1.0.0
 *     desc    :
 */

class FocusPointView : androidx.appcompat.widget.AppCompatImageView {

    var shapeTotalQuantity = 0/*图形总数量*/
    var selectShapeIndex = 0/*当前选中块序号*/
    var selectEdgeIndex = 0/*选中边序号*/
    var selectEdgeIcIndex = 0/*当前选中边上 ic 的序号*/

    var pointF = PointF()

    var isCenterPoint = false// 是否是中心点
    var distance = 0

    companion object {
        const val SIZE_FOCUS = 29
        const val MIN_SCALE = 1f
        const val TAG = "FocusPointView"
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        maxWidth = SIZE_FOCUS
        maxHeight = SIZE_FOCUS
        scaleType = ScaleType.FIT_CENTER
        setBackgroundResource(R.drawable.component_ui_point_style_9_selector)
    }

    fun setScale(scale: Float) {
//        log(TAG, "setScale(),  scale = $scale , isSelected = $isSelected")
        visibility = if (scale < MIN_SCALE && !isSelected) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }
}