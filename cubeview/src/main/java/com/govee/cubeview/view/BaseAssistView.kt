package com.govee.cubeview.view

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.widget.FrameLayout
import com.govee.cubeview.showLog
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2022/10/8
 *     version : 1.0.0
 *     desc    : 基础辅助图形
 *     1、初始化
 *     2、设置位置
 *     3、作用
 *         --- 运行功能  （提示、选中框、）
 *         --- 点击功能（添加、删除、聚焦点）
 */
abstract class BaseAssistView : androidx.appcompat.widget.AppCompatImageView, IBaseAssistView {

    protected val TAG = this.javaClass.simpleName.toString()

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )
    companion object {
        var sizeWith = 19//边长
        var sizeHeight = 46//边长
    }

    val pos = PointF()


    override fun setLayoutParams(pointF: PointF) {
        pos.x = pointF.x
        pos.y = pointF.y
        val lp = FrameLayout.LayoutParams(sizeWith, sizeHeight)
        val left = pointF.x.roundToInt() - sizeWith / 2
        val top = pointF.y.roundToInt() - sizeHeight / 2
        lp.leftMargin = left
        lp.topMargin = top
        layoutParams = lp
    }

    override fun doSomething(block: () -> Unit) {
        block()
    }

}

interface IBaseAssistView {

    fun setLayoutParams(pointF: PointF)

    /**
     * 用于具体辅助类实现的的功能
     */
    fun doSomething(block: () -> Unit)

}
