package com.govee.cubeview

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Point
import android.graphics.PointF
import android.graphics.Rect
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.animation.Animation
import android.view.animation.DecelerateInterpolator
import android.view.animation.TranslateAnimation
import android.widget.FrameLayout
import androidx.annotation.UiThread
import com.govee.cubeview.Constants.fetchRoundColor4PreViewBg
import com.govee.cubeview.shape.AbsCubeShapeView
import com.govee.cubeview.shape.AbsCubeShapeView.Companion.INNER_LINE_LENGTH
import com.govee.cubeview.shape.AbsCubeShapeView.Companion.LINE_LENGTH
import com.govee.cubeview.shape.AbsCubeShapeView.Companion.OUTSIDE_LINE_LENGTH
import com.govee.cubeview.shape.FocusPoint
import com.govee.cubeview.shape.MultiPower
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.shape.ShapeTree
import com.govee.cubeview.shape.ShapeTree2
import com.govee.cubeview.shape.ShapeTree3
import com.govee.cubeview.shape.SolidHexagonOldView
import com.govee.cubeview.shape.SolidHexagonView
import com.govee.cubeview.shape.SquraViewNew
import com.govee.cubeview.shape.TriangleView
import com.govee.cubeview.shape.YView
import com.govee.cubeview.view.DynamicImgPathShapeView
import com.govee.cubeview.view.FocusDragView
import com.govee.cubeview.view.FocusPointView
import com.govee.cubeview.view.ImgAddShapeView
import com.govee.cubeview.view.ImgDelShapeView
import com.govee.cubeview.view.ImgPathShapeView
import com.govee.cubeview.view.ImgPowerShapeView
import com.govee.cubeview.view.PhysicalSizeView
import com.govee.cubeview.view.SelectBoxView
import com.govee.ui.R
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.roundToInt
import kotlin.math.sqrt


/**
 * Create by lvwenrui on 2021/7/3 10:58
 * 画布的布局
 *
 * <p>
 *  Tips 画布添加 view 的简单流程
 *  1、设置画布参数
 *  2、根据首次画布中心坐标，画出多边形图形，计算相关的参数
 *  3、根据多边形的中心坐标，以及多边形旋转的角度（不同的算法计算出的角度），计算出"添加" 的中心坐标
 *     获取已经添加的多边形、"电源"图形、"电源"图形所占用的区域。x1<x<x1, y1<y<y2),如果可以，则直接创建"添加"图形，添加到画布中
 *  4、在第一个多边形图形下，添加"电源"图形。
 *  </p>
 */
class CanvasLayout : FrameLayout {

    private var maxCount = Constants.getShapeSupportMaxCount()
    private var canvasSize = Constants.getCanvasLayoutSize()//画布布局

    companion object {
        val TAG: String = this::class.java.name

        const val CANVAS_PADDING = 40//边界检测的padding
        const val SCALE_DEFAULT = 1.5f//初始化时拉伸的比例

        /*创建默认Shape*/
        @JvmStatic
        fun generaDefaultShape(type: Int): ShapePosition {
            val canvasLayoutSize = Constants.getCanvasLayoutSize(type)
            val shapeDefaultAngle = Constants.getShapeDefaultAngle(type)
            return ShapePosition(
                type,
                (canvasLayoutSize / 2).toFloat(),
                (canvasLayoutSize / 2).toFloat(),
                shapeDefaultAngle, 0, 0
            )
        }

        @JvmStatic
        fun getShapeMaxCount(shapeType: Int): Int {
            return Constants.getShapeSupportMaxCount(shapeType)
        }

        @JvmStatic
        fun canvasLayoutSize(shapeType: Int): Int {
            return Constants.getCanvasLayoutSize(shapeType)
        }

        @JvmStatic
        fun getShapeMultiPowerBoundary(shapeType: Int): Int {
            return Constants.getShapePowerBoundary(shapeType)
        }

        @JvmStatic
        fun getInstallNumberWithDirectionTag(shapeType: Int, directionTag: Int): Int {
            return Constants.getInstallNumberWithDirectionTag(shapeType, directionTag)
        }
    }

    /*形状列表*/
    var shapeViews: MutableList<AbsCubeShapeView> = ArrayList()

    /*添加按钮View的集合*/
    private var addViews: MutableList<View> = ArrayList()

    /*路径的View的集合*/
    private var pathViews: MutableList<ImgPathShapeView> = ArrayList()

    private var powerViewPoints: MutableList<AbsCubeShapeView> = ArrayList()

    /*删除按钮*/
    var delView: View? = null

    /*电源View*/
    var powerView: ImgPowerShapeView? = null

    private var multiPowerView: ImgPowerShapeView? = null

    private var multiPowerPathView: ImgPathShapeView? = null

    /*聚焦View*/
    var focusDragView: FocusDragView? = null
    var isIn = false//聚焦，扩撒

    //整体旋转角度
    private var canvasRotation = 0f

    /*整个图形中心点*/
    private var mShapeCenterPoint: PointF? = null

    /*图形初始的相对于画布中心的偏移*/
    var mDefaultOffsetPoint: PointF? = null

    /*图形初始的缩放大小*/
    var mDefaultScale: Float? = null

    /*聚焦view的scale*/
    private var mFocusDragViewScale: Float = 0f

    /*UI类型*/
    var mType = Type.DefaultType

    private var animList: MutableList<Animator> = ArrayList()

    private var preView: Bitmap? = null

    private var roundColor4PreViewBg: IntArray? = null

    /*UI类型*/
    enum class Type {
        DefaultType,//默认，白色块
        Edit,//拼接
        Install,//安装
        Check,//校验
        ColorMode,///颜色模式
        Focus,///diy&场景聚焦模式
        MultiPower,// 新增电源位置选择
        Preview,//效果预览
        AddLamp;// 加装模式
    }

    private var cubeShapeViewType = Shape.TYPE_DEFAULT

    /*最后一个图形的旋转角度*/
    private var mLastOffsetRotation = 0

    private val shapeTree by lazy { ShapeTree() }
    private val shapeTree3 by lazy { ShapeTree3() }

    /*缩放和拖动回调*/
    var callBack: OnScaleAndTransitionChange? = null
    var colorModelCallback: OnSelectShapeChange? = null
    var focusDragCallback: OnFocusDragChange? = null
    var addDeleteCallback: OnAddDeleteChange? = null
    var changeLayoutParams: OnChangeLayoutParams? = null

    private val lastedPointF: PointF?
        get() {
            return shapeViews.lastOrNull()?.pos
        }

    private val lastedShape: AbsCubeShapeView?
        get() {
            return shapeViews.lastOrNull()
        }

    constructor(context: Context?) : super(context!!) {
        initAttrs(context, null)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {
        initAttrs(context, attrs)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context!!,
        attrs,
        defStyleAttr
    ) {
        initAttrs(context, attrs)
    }

    private fun initAttrs(context: Context?, attrs: AttributeSet?) {
        val obtainStyledAttributes =
            context?.obtainStyledAttributes(attrs, R.styleable.CanvasLayout)
        obtainStyledAttributes?.let {
            cubeShapeViewType =
                it.getInt(R.styleable.CanvasLayout_cube_shape_view_type, Shape.TYPE_DEFAULT)
            restCanvasLayoutParams(cubeShapeViewType)
        }
        obtainStyledAttributes?.recycle()
    }

    fun getColor(resId: Int): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            context.resources.getColor(resId, null)
        } else {
            context.resources.getColor(resId)
        }
    }

    private fun restCanvasLayoutParams(cubeShapeViewType: Int) {
        maxCount = Constants.getShapeSupportMaxCount(cubeShapeViewType)
        canvasSize = Constants.getCanvasLayoutSize(cubeShapeViewType)
        init()
    }

    fun init() {
        val lp: LayoutParams? = layoutParams as? LayoutParams
        lp?.apply {
            this.width = canvasSize
            this.height = canvasSize
            this.gravity = Gravity.CENTER
            layoutParams = this
        }
        clipChildren = false
    }

    /**
     * 设置多边形状
     */
    fun setCubeShapeViewType(type: Int) {
        this.cubeShapeViewType = type
        restCanvasLayoutParams(type)
//        changeLayoutParams?.apply { layoutParams(width, height) }
    }

    fun setCubeShapeViewType(type: Int, size: Int) {
        this.cubeShapeViewType = type
        maxCount = Constants.getShapeSupportMaxCount(cubeShapeViewType)
        canvasSize = size
        changeLayoutParams?.apply { layoutParams(width, height) }
        init()
    }

    /*添加，方块灯 direction 方向，Tag 0-5*/
    private fun addShape(
        directionTag: Int,
        updateOtherUi: Boolean,
        shapeViewIndex: Int = 0
    ): FloatArray {

        val (centerPointX: Float, centerPointY: Float) = setShapeViewParameters(
            directionTag,
            shapeViewIndex
        )

        if (updateOtherUi) {
            updateUIState()
        }

        /*返回view的四条边坐标*/
        return floatArrayOf(
            centerPointX - OUTSIDE_LINE_LENGTH / 2,
            centerPointX + OUTSIDE_LINE_LENGTH / 2,
            centerPointY - OUTSIDE_LINE_LENGTH / 2,
            centerPointY + OUTSIDE_LINE_LENGTH / 2
        )
    }

    /**
     *  设置形状视图参数 中心点，旋转角度，通过协议中的两个点确定是哪条边出
     */
    private fun setShapeViewParameters(shapeCentrePoint: PointF, angle: Int): Pair<Float, Float> {
        val createShapeView = createShapeView()
        createShapeView.apply {
            canvasRotation = <EMAIL>

            offsetRotation = angle.toFloat() - canvasRotation

            setPos(shapeCentrePoint.x, shapeCentrePoint.y)

            setLayoutParams(
                shapeCentrePoint.x.roundToInt(),
                shapeCentrePoint.y.roundToInt(),
                if (mType == Type.Install || mType == Type.Preview) LINE_LENGTH else INNER_LINE_LENGTH
            )

            /*上一个图形从哪边出的*/
            var lastShapeDirectionTag = 0
            var angleOffSet = 0f
            var roundAngle = 0

            /*通过两个点计算出从哪条边出*/
            lastedShape?.apply {
                /*计算出的绝对角度，相对于画布*/
                val calAngle = Utils.calAngle(pos, shapeCentrePoint)
                roundAngle = (calAngle / 30f).roundToInt() * 30 + 60
                angleOffSet = (roundAngle - canvasRotation - offsetRotation)
                angleOffSet = Utils.rangeRotation360(angleOffSet)

                lastShapeDirectionTag = (angleOffSet / 60f).toInt()

                /*赋值 -- 上一个图形是从哪条边出的*/
                nextDirectionTag = lastShapeDirectionTag

//                log(
//                    "setLastShapeDirectionTag",
//                    "calAngle = $calAngle roundAngle = $roundAngle  angleOffSet = $angleOffSet lastShapeDirectionTag = $lastShapeDirectionTag + nextDirectionTag = $nextDirectionTag"
//                )

            }

            /*设置自身的Tag偏移量*/
            Constants.getNextShapeSupportOffset(
                lastShapeDirectionTag,
                <EMAIL>
            ).let {
                val firOffsetInfo = it.first
                val secOffsetInfo = it.second
                val lastRotation =
                    getRotationByDirectionTag(lastShapeDirectionTag).let { rotation ->
                        var i = rotation
                        while (i <= 0) i += 360
                        while (i > 360) i -= 360
                        360 - i
                    }

//                log(
//                    "setOffsetTag",
//                    "SecTagOffset=${secOffsetInfo?.first} canvasRotation=$canvasRotation  lastRotation=$lastRotation  offsetRotation=$offsetRotation angleOffSet=$angleOffSet roundAngle=$roundAngle"
//                )


                if (lastedShape != null) {
                    val angVal =
                        360 - Utils.rangeRotation360(roundAngle - 60 - offsetRotation - canvasRotation)
//                        Utils.rangeRotation360(lastRotation - canvasRotation + offsetRotation )
                    offsetTag = if (secOffsetInfo?.first == angVal) {
                        secOffsetInfo
                    } else {
                        firOffsetInfo
                    }
//                    log(
//                        "setOffsetTag",
//                        "lastRotation=$lastRotation, lastShapeDirectionTag=$lastShapeDirectionTag, offsetTag=${offsetTag?.first}"
//                    )

                }
            }

            /*安装模式设置方块颜色透明*/
            if (mType == Type.Install) {
                alpha = 0.4f
            }

            /*颜色模式*/
            if (mType == Type.ColorMode) {
                val color = getColor(R.color.ui_color_block_style_16_1_color)
                setColorMode4Color(color)
                setOnClickListener {
                    mSelected = !mSelected
                    invalidate()
                    /*通知选中方块*/
                    val list = arrayListOf<Boolean>()
                    shapeViews.forEach {
                        list.add(it.mSelected)
                    }
                    colorModelCallback?.selectedShape(list)
                }
            }
//            setSolidHexagonShapeViewBg(this)
        }.also {
            addView(it)
            shapeViews.add(it)
        }
        return Pair(shapeCentrePoint.x, shapeCentrePoint.y)
    }

    /**
     *  添加形状时，设置形状视图参数 Tag 0-5
     */
    private fun setShapeViewParameters(
        directionTag: Int,
        shapeViewIndex: Int
    ): Pair<Float, Float> {
        val centerPointX: Float
        val centerPointY: Float

        val createShapeView = createShapeView()
        var touchableLength = when (mType) {
            Type.Install, Type.Check -> {
                LINE_LENGTH
            }

            Type.Preview -> {
                OUTSIDE_LINE_LENGTH
            }

            else -> {
                INNER_LINE_LENGTH
            }
        }
        createShapeView.apply {
            canvasRotation = <EMAIL>

            /*上一个的Offset*/
            var lastRotation = 0f
            /*根据方向获取多边形旋转的角度*/
            var rotationByDirectionTag = 0f
            /*下一个tag可能会变化，需要动态校正*/
            var realDirectionTag = directionTag
            var length = INNER_LINE_LENGTH * 2

            val lastShapeView =
                if (isYShape() && shapeViewIndex >= 0 && shapeViewIndex < shapeViews.size) shapeViews[shapeViewIndex] else lastedShape

            if (isYShape()) {
                length = OUTSIDE_LINE_LENGTH * 2
            }

            log(TAG, "addShape(), shapeViewIndex $shapeViewIndex , directionTag = $directionTag")

            val preShapePointF = lastShapeView?.pos

            lastShapeView?.apply {
                /*输出位置与电源冲突了*/
                if (directionTag == Constants.getShapeInputTag(<EMAIL>)) {
                    val (firOffSetTag, secOffsetTag) = Constants.getNextShapeSupportOffset(
                        directionTag,
                        <EMAIL>
                    )
                    if (secOffsetTag != null) {
                        val (firRotation, firTag) = firOffSetTag
                        val (secRotation, secTag) = secOffsetTag
                        if (secOffsetTag.first == offsetTag?.first) {
                            /*旋转上一个图形*/
                            this.offsetRotation -= secRotation - firRotation
                            this.offsetRotation = this.offsetRotation % 360
                            offsetTag = firOffSetTag
                            realDirectionTag -= secTag - firTag
                        } else {
                            /*旋转上一个图形*/
                            this.offsetRotation += secRotation - firRotation
                            this.offsetRotation = this.offsetRotation % 360
                            offsetTag = secOffsetTag
                            realDirectionTag += secTag - firTag
                        }
                    }
                }

                while (realDirectionTag >= 6) realDirectionTag -= 6
                while (realDirectionTag < 0) realDirectionTag += 6

                /*相对偏移角度*/
                this.nextDirectionTag = realDirectionTag
                lastRotation = this.offsetRotation
                this.offsetNextDirectionTag[shapeViews.size] = directionTag
                val rotationValue = this.getRotationValue()
                showLog(TAG, "setShapeViewParameters: rotationValue = $rotationValue ")
            }

            if (preShapePointF == null) {
                /*添加第一个*/
                centerPointX = (canvasSize / 2).toFloat()
                centerPointY = (canvasSize / 2).toFloat()
            } else {
                /*计算角度*/
                /*实际偏移角度 = 旋转过的角度 + 添加的方向角度*/
                rotationByDirectionTag = this.getRotationByDirectionTag(realDirectionTag)
                val realRotation = lastRotation + canvasRotation + rotationByDirectionTag
                val point = getShapeRotation(preShapePointF, length, realRotation)
                centerPointX = point.x
                centerPointY = point.y
            }

            Log.i(
                TAG,
                "setShapeViewParameters: centerPointX = $centerPointX , centerPointY = $centerPointY"
            )
            setPos(centerPointX, centerPointY)
            if (isYShape() && mType == Type.ColorMode) {
                touchableLength = OUTSIDE_LINE_LENGTH
            }
            setLayoutParams(
                centerPointX.roundToInt(), centerPointY.roundToInt(), touchableLength
            )

            /*设置自身offsetTag -- 需要计算输入位置冲突*/
            Constants.getNextShapeSupportOffset(realDirectionTag, cubeShapeViewType).let {
                offsetTag = it.first
            }

            offsetRotation = (lastRotation + rotationByDirectionTag + offsetTag!!.first) % 360

            this.lastPosition = shapeViewIndex
            this.lastShapeView = lastShapeView

            if (isYShape()) {
                shapeTree.insert(
                    lastPosition,
                    shapeViews.size,
                    directionTag == 2, toShapePosition()
                )
            }

            /*安装模式设置方块颜色透明*/
            if (mType == Type.Install) {
                alpha = 0.4f
            }

            /*颜色模式*/
            if (mType == Type.ColorMode) {
                val color = getColor(R.color.ui_color_block_style_16_1_color)
                setColorMode4Color(color)
                setOnClickListener {
                    mSelected = !mSelected
                    invalidate()
                    /*通知选中方块*/
                    val list = arrayListOf<Boolean>()
                    shapeViews.forEach {
                        list.add(it.mSelected)
                    }
                    colorModelCallback?.selectedShape(list)
                }
            }
//            setSolidHexagonShapeViewBg(this)
        }.also {
            addView(it)
            shapeViews.add(it)
        }
        return Pair(centerPointX, centerPointY)
    }

    private fun createShapeView(): AbsCubeShapeView {
        return when (cubeShapeViewType) {
            Shape.TYPE_HEXAGON -> SquraViewNew(context)
            Shape.TYPE_TRIANGLE -> TriangleView(context)
            Shape.TYPE_SOLID_HEXAGON -> SolidHexagonView(context)
            Shape.TYPE_SPACE_HEXAGON -> SolidHexagonOldView(context).apply {
                initAngle = -90
                LINE_LENGTH = 46f
                baseLineAngle = -30f
            }

            Shape.TYPE_Y -> YView(context)
            else -> SquraViewNew(context)
        }
    }

    /*根据协议中定义的点和角度来添加图形*/
    private fun addShapeWithAngle(x: Float, y: Float, angle: Int): FloatArray {
        /*真实旋转角度 = 协议角度 - 初始默认角度*/
        val realRotation = angle - Constants.getShapeDefaultAngle(cubeShapeViewType)
        val (centerPointX: Float, centerPointY: Float) = setShapeViewParameters(
            PointF(x, y),
            realRotation
        )
        /*返回view的四条边坐标*/
        return floatArrayOf(
            centerPointX - LINE_LENGTH,
            centerPointX + LINE_LENGTH,
            centerPointY - LINE_LENGTH,
            centerPointY + LINE_LENGTH
        )
    }

    /* Y 形*/
    private fun addShapeWithAngle2(
        index: Int,
        shapePosition: ArrayList<ShapePosition>,
        p: ShapePosition
    ): FloatArray {
        shapeTree3.insert(index, p)

        val findLastShape = shapeTree3.findLastShape(index)
        p.lastPosition = findLastShape
        mLastOffsetRotation = shapePosition[findLastShape].angle
//        if (shapePosition.size > p.lastPosition && p.lastPosition >= 0) {
//            mLastOffsetRotation = shapePosition[p.lastPosition].angle
//        }

        val angle = p.angle
        var diff = angle - mLastOffsetRotation

        if (diff < 0) {
            diff += 360
        }

        val max = if (diff == 60) 1 else 2


        return addShape(max, false, p.lastPosition)
    }

    /*三角形*/
    private fun addShapeWithAngle1(angle: Int): FloatArray {
        var diff = angle - mLastOffsetRotation

        if (diff < 0) {
            diff += 360
        }

        val max = if (diff == 60) 1 else 2

        mLastOffsetRotation = angle
        return addShape(max, false)
    }

    /*删除，最后一个图形*/
    private fun delLastedShape() {
        if (shapeViews.size <= 1) return
        shapeTree.deleteNode(shapeViews.size - 1)

        lastedShape?.let {
            val lastPosition = it.lastPosition
            val offsetNextDirectionTag = shapeViews[lastPosition].offsetNextDirectionTag
            offsetNextDirectionTag.remove(shapeViews.size - 1)
        }
        removeView(lastedShape)
        shapeViews.removeLastOrNull()

        updateUIState()
        addDeleteCallback?.shapeSizeChange(shapeViews.size)
    }

    /**
     * 只提供 Y 形灯获取 IC 序号
     * MutableList<Int>
     *  第一位：表示电源入口
     *  第二位：表示电源入口左
     *  第三位：表示电源入口右
     */
    fun getYShapeIc(shapeViewIndex: Int): MutableList<Int> {
        if (!isYShape()) return mutableListOf()
        return shapeTree.ergodic(shapeViewIndex)
    }

    /*旋转，遍历所有view，重绘 rotation一次旋转的角度*/
    fun setShapeRotation(onceRotation: Float) {
        canvasRotation += onceRotation
        canvasRotation %= 360
        val centerPos = shapeViews.first().pos

        shapeViews.forEachIndexed { index, shape ->
            shape.canvasRotation = canvasRotation

            val detPoint = floatArrayOf(shape.pos!!.x, shape.pos!!.y)
            rotatePoint(onceRotation, detPoint, centerPos!!)
            shape.setPos(detPoint[0], detPoint[1])
            log(TAG, "setShapeRotation(), $detPoint")
            updateShapePosition(shape)
        }

        updateUIState()
        val shapeRotations = getShapeRotations()
        log(TAG, "setShapeRotation(): $shapeRotations")
    }

    private fun rotatePoint(rotate: Float, detPoint: FloatArray, srcPointF: PointF) {
        val matrix = Matrix()
        matrix.setRotate(rotate, srcPointF.x, srcPointF.y)
        /*旋转*/
        matrix.mapPoints(detPoint)
    }

    private val mSelectBoxView by lazy {
        SelectBoxView(context)
    }

    private val mPhysicalSizeView by lazy {
        PhysicalSizeView(context)
    }

    fun showShapeSizeView(show: Boolean, zoomScale: Float) {
        show.yes {
            val parent = mSelectBoxView.parent
            if (parent == this) {
                return@yes
            }
            val shapeSize = getShapeSize()
            val shapeCenterX = abs(shapeViewsRange[1] + shapeViewsRange[0]) / 2
            val shapeCenterY = abs(shapeViewsRange[3] + shapeViewsRange[2]) / 2
            val dp2px = Utils.dp2px(context, 2f)

            mSelectBoxView.apply {
                val lp =
                    LayoutParams(shapeSize.x.roundToInt() + dp2px, shapeSize.y.roundToInt() + dp2px)
                val left = shapeCenterX - shapeSize.x / 2 - dp2px / 2
                val top = shapeCenterY - shapeSize.y / 2 - dp2px / 2
                lp.leftMargin = left.roundToInt()
                lp.topMargin = top.roundToInt()
                layoutParams = lp
            }.also {
                addView(it)
            }

            mPhysicalSizeView.apply {
                scaleY = 1 / zoomScale
                scaleX = 1 / zoomScale

                val physicalShapeSize = getPhysicalShapeSize()

                val width = ceil(physicalShapeSize.x.toDouble()).roundToInt()
                val height = ceil(physicalShapeSize.y.toDouble()).roundToInt()

                val base = 0.3937f // 英尺尺寸换算单位

                val inchW = ceil((physicalShapeSize.x * base * 10).toDouble()) / 10
                val inchH = ceil((physicalShapeSize.y * base * 10).toDouble()) / 10

                val stringFormat: String =
                    Utils.getStringFormat(
                        context,
                        R.string.cubelight_effect_preview_hint,
                        width,
                        height,
                        inchW,
                        inchH
                    )
                this.text = stringFormat

                val makeMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)

                measure(makeMeasureSpec, makeMeasureSpec)

                val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                val left = shapeCenterX - measuredWidth / 2
                val top = shapeViewsRange[3] + Utils.dp2px(context, 10f) / zoomScale

                lp.leftMargin = left.roundToInt()
                lp.topMargin = top.roundToInt()
                layoutParams = lp
            }.also {
                addView(it)
            }
        }.no {
            removeView(mSelectBoxView)
            removeView(mPhysicalSizeView)
        }
    }

    /**
     * 根据翻转点 x 轴坐标进行翻转
     * <p>
     *     翻转动画步骤：1、整个画布从 0 到 90 度旋转以 Y 轴，取第一块的中心点的 X 坐标。
     *                2、旋转到 90 度时，计算翻转后的图形中心点以及角度，同时移动到翻转后的图形的中心点。
     *                3、整个画布从 270 翻转到 360 以防坐标系发生变化。
     *     注意点：1、翻转的x 需要改变 cameraDistance ，详见：FlipAnimationUtil.startAnimation
     *           2、不同图形翻转的角度算法不一致，可直接看代码逻辑
     *           3、需要重新计算每一块图形的方向
     *           4、翻转后的画布旋转角度以及图形旋转角度需要重新计算
     *
     * </p>
     */
    fun flipShapeViewByX() {
        FlipAnimationUtil.startAnimation(this) {
            var lastedShape: AbsCubeShapeView? = null
            var lastOffsetRotation = 0
            this.canvasRotation = 0f
            // 根据中心点坐标翻转
            val first = shapeViews.first()
            val firstPos = first.pos
            firstPos?.let {
                shapeViews.forEachIndexed { index, it ->
                    /* 计算中心点坐标 */
                    it.pos?.let {
                        val x = it.x
                        it.x = 2 * firstPos.x - x
                    }
                    val toShapePosition = it.toShapePosition()
                    /*重新设置中心点*/
                    it.setPos(toShapePosition.x, toShapePosition.y)
                    /*画布重新设置*/
                    it.canvasRotation = 0f

                    var diff =
                        toShapePosition.angle - Constants.getShapeDefaultAngle(cubeShapeViewType)
                    if (diff < 0) {
                        diff += 360
                    }
                    // 实际旋转的角度
                    toShapePosition.angle = diff

                    /*设置图形旋转的角度*/
                    when (cubeShapeViewType) {
                        Shape.TYPE_TRIANGLE, Shape.TYPE_Y -> {
                            toShapePosition.angle = 360 - toShapePosition.angle
                            it.offsetRotation = toShapePosition.angle.toFloat()
                        }

                        Shape.TYPE_SOLID_HEXAGON -> {
                            toShapePosition.angle = 360 - toShapePosition.angle
                            it.offsetRotation = toShapePosition.angle.toFloat()
                        }

                        Shape.TYPE_HEXAGON -> {
                            toShapePosition.angle = 360 - diff + 60
                            /*真实旋转角度 = 协议角度 - 初始默认角度*/
                            val realRotation =
                                toShapePosition.angle - Constants.getShapeDefaultAngle(
                                    cubeShapeViewType
                                )
                            it.offsetRotation = realRotation.toFloat()
                        }
                    }
                    /* 倒推形式，计算上一个图形的方向 */


                    (isYShape() && it.lastPosition < shapeViews.size && index != 0).yes {
                        lastedShape = shapeViews[it.lastPosition]
                        lastOffsetRotation =
                            lastedShape?.offsetRotation?.toInt() ?: lastOffsetRotation
                    }

                    lastedShape?.apply {
                        when (cubeShapeViewType) {
                            Shape.TYPE_TRIANGLE, Shape.TYPE_Y -> {
                                var diff = toShapePosition.angle - lastOffsetRotation
                                if (diff < 0) {
                                    diff += 360
                                }
                                val directionTag = if (diff == 60) 1 else 2
                                /*相对偏移角度*/
                                nextDirectionTag = directionTag

                                this.offsetNextDirectionTag[index] = directionTag

                                if (isYShape()) {
                                    val rotationByDirectionTag =
                                        this.getRotationByDirectionTag(directionTag)
                                    val realRotation =
                                        offsetRotation + canvasRotation + rotationByDirectionTag
                                    val length = OUTSIDE_LINE_LENGTH * 2
                                    val point = getShapeRotation(pos!!, length, realRotation)
                                    it.setPos(point.x, point.y)
                                }

                                log(TAG, "isTriangleShape() nextDirectionTag = $nextDirectionTag")
                            }

                            Shape.TYPE_HEXAGON, Shape.TYPE_SOLID_HEXAGON -> {
                                val shapeCentrePoint = PointF(toShapePosition.x, toShapePosition.y)
                                /*计算出的绝对角度，相对于画布*/
                                val calAngle = Utils.calAngle(pos, shapeCentrePoint)
                                val roundAngle = (calAngle / 30f).roundToInt() * 30 + 60
                                var angleOffSet = (roundAngle - canvasRotation - offsetRotation)
                                angleOffSet = Utils.rangeRotation360(angleOffSet)

                                /*赋值 -- 上一个图形是从哪条边出的*/
                                nextDirectionTag = (angleOffSet / 60f).toInt()
                            }
                        }
                    }
                    updateShapePosition(it)
                    lastedShape = it
                    lastOffsetRotation = toShapePosition.angle
                }
                updateUIState()

                val shapeSize = getShapeSize()
                /*整体相对于画布的拉伸*/
                val scale = max(shapeSize.x, shapeSize.y) / canvasSize

                moveShapeViewCenter(scale, true)
            }
        }
    }

    private fun moveShapeViewCenter(scale: Float = 0f, animation: Boolean = false) {

        val arrayArea =
            Array(shapeViews.size) { FloatArray(4) }
        for (i in shapeViews.size - 1 downTo 0) {
            arrayArea[i] = shapeViews[i].shapeArea4Parent
        }
        val canvasCenterPoint = canvasSize / 2f
        val allShapeArea =
            floatArrayOf(
                canvasCenterPoint,
                canvasCenterPoint,
                canvasCenterPoint,
                canvasCenterPoint
            )
        arrayArea.forEach {
            allShapeArea[0] = min(allShapeArea[0], it[0])
            allShapeArea[1] = max(allShapeArea[1], it[1])
            allShapeArea[2] = min(allShapeArea[2], it[2])
            allShapeArea[3] = max(allShapeArea[3], it[3])
        }
        /*整体的宽高*/
        val shapeWidth = allShapeArea[1] - allShapeArea[0]
        val shapeHeight = allShapeArea[3] - allShapeArea[2]
        /*整个图形相对于画布中心的坐标*/
        val fl = allShapeArea[1] - shapeWidth / 2f - canvasCenterPoint * 1.0f
        val fl1 = allShapeArea[3] - shapeHeight / 2f - canvasCenterPoint * 1.0f
        val shapeOffsetPoint = PointF(-fl, -fl1)

        callBack?.scaleAndTransition(scale, shapeOffsetPoint, animation)
        log(
            TAG,
            "mDefaultOffsetPoint = scale = $scale , $mDefaultOffsetPoint , shapeOffsetPoint = $shapeOffsetPoint , fl = $fl ,  fl1 = $fl1"
        )

    }

    private fun updateUIState() {

        if (mType == Type.Edit || mType == Type.Install || mType == Type.Check || mType == Type.MultiPower) {
            /*更新电源键*/
            updatePowerItem()
        }
        if (mType == Type.Edit || mType == Type.AddLamp) {
            /*更新添加按钮*/
            updateAddImgItem()
            /*更新删除按钮*/
            updateDelState()
        }
        if (mType == Type.Install) {
            /*更新图形的拼接路径*/
            updatePathItem()
        }
        if (mType == Type.Focus) {
            /*更新拖拽聚焦item*/
            updateFocusDragItem()
        }

        if (mType == Type.MultiPower) {
            checkMultiPowerSource()
        }
    }

    /*更新某个图形的具体位置*/
    private fun updateShapePosition(shape: AbsCubeShapeView) {
        shape.pos?.apply {
            shape.setLayoutParams(
                this.x.roundToInt(),
                this.y.roundToInt(),
                if (mType == Type.Install) LINE_LENGTH else INNER_LINE_LENGTH
            )
        }
        shape.invalidate()
    }

    /*更新删除按钮*/
    private fun updateDelState() {
        if (isYShape()) {
            updateAllDelState()
            return
        }
        /*最后一个图形可以删除*/
        var num = 1
        shapeViews.forEach {
            it.setNumberText("${num++}")
        }
        /*清除最后一个删除*/
        delView?.let { removeView(it) }
        delView = null

        if (shapeViews.size > 1) {
            lastedShape?.setNumberText("")
            /*删除按钮*/
            delView = ImgDelShapeView(context).apply {
                pos.also {
                    it.x = lastedPointF!!.x
                    it.y = lastedPointF!!.y
                }
                val lp = LayoutParams(ImgDelShapeView.SIZE_DEL, ImgDelShapeView.SIZE_DEL)
                lp.leftMargin = lastedPointF!!.x.toInt() - ImgDelShapeView.SIZE_DEL / 2
                lp.topMargin = lastedPointF!!.y.toInt() - ImgDelShapeView.SIZE_DEL / 2
                layoutParams = lp
                bgColor = lastedShape?.mColorModeColor
                setOnClickListener { delLastedShape() }
            }.also {
                addView(it)
            }
        }

    }

    private val delImg4YShape = mutableListOf<ImgDelShapeView>()

    private fun updateAllDelState() {
        delImg4YShape.forEach {
            removeView(it)
        }
        shapeViews.forEachIndexed { index, absCubeShapeView ->
            if (absCubeShapeView.state == 0 && index != 0) {
                // 没有连接，显示删号
                ImgDelShapeView(context).apply {

                    setLayoutParams(absCubeShapeView.pos!!)
                    setOnClickListener { onDelImgOnClick(absCubeShapeView, index) }
                }.also {
                    addView(it)
                    delImg4YShape.add(it)
                }
            }
        }
    }

    /*删除，最后一个图形*/
    private fun onDelImgOnClick(view: AbsCubeShapeView, index: Int) {
        if (shapeViews.size <= 1) return
        shapeTree.deleteNode(index)
        log(TAG, "testLog--- 点击删除后 --- ")

        view.let {
            val lastPosition = it.lastPosition
            val offsetNextDirectionTag = it.lastShapeView!!.offsetNextDirectionTag
            log(
                TAG,
                "testLog---, lastPosition = $lastPosition, offsetNextDirectionTag = $offsetNextDirectionTag , index = $index"
            )
            offsetNextDirectionTag.remove(index)
        }
        removeView(view)
        shapeViews.remove(view)

        val newTag = HashMap<Int, Int>()
        shapeViews.forEachIndexed { shapeIndex, it ->
            newTag.clear()
            if (it.lastPosition >= index) {
                log(TAG, "testLog---,修正前-- ${it.lastPosition}")
                it.lastPosition--
                log(TAG, "testLog---,修正后-- ${it.lastPosition}")
            }

            val offsetNextDirectionTag = it.offsetNextDirectionTag
            offsetNextDirectionTag.keys.forEach {
                val value = offsetNextDirectionTag[it]
                if (it >= index) {
                    log(TAG, "testLog--- 第 $shapeIndex 块 开始矫正 --- ")

                    val newKey = it - 1
                    newTag.put(newKey, value!!)
                    log(TAG, "testLog---，重新赋值-- newKey = $newKey , value = $value")
                } else {
                    newTag.put(it, value!!)
                }
            }

            if (newTag.isNotEmpty()) {
                it.offsetNextDirectionTag.clear()
                newTag.forEach { newOffset ->
                    it.offsetNextDirectionTag[newOffset.key] = newOffset.value
                }
                log(TAG, "testLog---,第 $shapeIndex 块 , newTag = $newTag")
                log(TAG, "testLog--- 第 $shapeIndex 块 矫正结束 --- ")
            }
        }

        updateUIState()
    }

    /**
     * 根据图形中心点获取相关图形辅助点的坐标
     * （多边形内切圆半径 + 辅助图形半径，计算出中心点到辅助点的距离；再通过，旋转角度，计算出相关的坐标）
     *
     * Get the coordinates of the auxiliary point
     */
    private fun getAuxiliaryPoints4Shape(
        lastShapeView: AbsCubeShapeView,
        viewSize: Int
    ): Array<FloatArray> {
        /* 偏移距离 = 内切圆半径 + 图形半径 */
        return lastShapeView.getShapePoint(
            lastShapeView.pos ?: PointF(),
            INNER_LINE_LENGTH + viewSize / 2,
            lastShapeView.offsetRotation + lastShapeView.canvasRotation + lastShapeView.addImgRotationOffset
        )

    }

    /*更新添加按钮数量及位置*/
    private fun updateAddImgItem() {
        /*清空所有添加按钮*/
        addViews.forEach {
            removeView(it)
        }
        addViews.clear()
        //不可用区域
        if (lastedShape == null || shapeViews.size >= maxCount) return
        /*根据最后一个添加的去做操作*/

        isYShape().yes {
            shapeViews.forEachIndexed { index, absCubeShapeView ->
                absCubeShapeView.apply {
                    val centerPointF = getShapePoint(
                        pos ?: PointF(),
                        OUTSIDE_LINE_LENGTH + ImgAddShapeView.SIZE_ADD / 2,
                        offsetRotation + canvasRotation + addImgRotationOffset, false
                    )

                    val shapePoints = getShapePoint(
                        pos ?: PointF(),
                        OUTSIDE_LINE_LENGTH + ImgAddShapeView.SIZE_ADD,
                        offsetRotation + canvasRotation + addImgRotationOffset, false
                    )
                    addAddImgView(index, centerPointF, shapePoints)

                }
            }
        }.no {
            val points =
                lastedShape!!.getShapePoint(
                    lastedShape!!.pos ?: PointF(),
                    INNER_LINE_LENGTH + ImgAddShapeView.SIZE_ADD / 2,
                    lastedShape!!.offsetRotation + lastedShape!!.canvasRotation + lastedShape!!.addImgRotationOffset
                )


            addAddImgView(shapeViews.lastIndex, points, points)
        }
    }

    private fun addAddImgView(
        shapeViewIndex: Int,
        centerPointF: Array<FloatArray>,
        points: Array<FloatArray>
    ) {
        points.forEachIndexed { index, floats ->
            val center = PointF(floats[0], floats[1])
            val points = PointF(centerPointF[index][0], centerPointF[index][1])
            val checkAreaValid = checkAreaValid(center)

            if (checkAreaValid) {
                /*有效范围内，可以添加*/
                ImgAddShapeView(context).apply {
                    directionTag = if (isTriangleShape() || isYShape()) {
                        2 - index
                    } else {
                        index
                    }

                    setLayoutParams(points)

                    pos.let {
                        it.x = center.x
                        it.y = center.y
                    }
                    setOnClickListener { onAddItemClick(shapeViewIndex, this.directionTag) }
                }.also {
                    addView(it)
                    addViews.add(it)
                }
            }
        }
    }

    /*更新电源位置*/
    private fun updatePowerItem() {
        /*清除最后一个删除*/
        powerView?.let { removeView(it) }
        shapeViews.getOrNull(0)?.apply {
            /*第一个*/
            val needRota = (<EMAIL> + offsetRotation + 180) % 360
            var point =
                this.getShapeRotation(
                    this.pos!!,
                    (INNER_LINE_LENGTH + ImgPowerShapeView.SIZE_POWER / 2),
                    needRota
                )

            /*电源图标占的格子的中心点*/
            var shapePoint = this.getShapeRotation(this.pos!!, (INNER_LINE_LENGTH * 2), needRota)

            isYShape().yes {
                shapePoint =
                    this.getShapeRotation(
                        this.pos!!,
                        (OUTSIDE_LINE_LENGTH + ImgPowerShapeView.SIZE_POWER), needRota
                    )

                point =
                    this.getShapeRotation(
                        this.pos!!,
                        (OUTSIDE_LINE_LENGTH + ImgPowerShapeView.SIZE_POWER / 2),
                        needRota
                    )
            }
            powerView = ImgPowerShapeView(context).apply {
                pos = shapePoint
                /*处于绘制最高级*/
                z = 2f
                val lp = LayoutParams(ImgPowerShapeView.SIZE_POWER, ImgPowerShapeView.SIZE_POWER)
                lp.leftMargin = point.x.toInt() - ImgPowerShapeView.SIZE_POWER / 2
                lp.topMargin = point.y.toInt() - ImgPowerShapeView.SIZE_POWER / 2
                layoutParams = lp
            }.also {
                addView(it)
            }
        }
    }

    /* 检测是否需要画多个电源图标 */
    private fun checkMultiPowerSource() {
        val size = shapeViews.size
        if (Constants.isMultiPower(size, cubeShapeViewType)) {
            //1、超过规定数量之后的图形才考虑是否需要接电源，接电源的起始点在总数量的一半
            //2、先确定哪个多边形可接入
            //3、点击时判断那条边的 Y 轴最大，
            //4、根据多边形的边确认新的电源方向在哪里

            powerViewPoints.clear()
            shapeViews.forEach {
                changeShapeLayoutParams(it, true)
                it.setMultiplePowerColor(false, it.color4unselected)
                it.z = 0f
            }
            val powerSize = Constants.fetchMultiPowerLimitIndex(size)
            val subList = shapeViews.subList(powerSize, size)

            subList.forEachIndexed { shapeIndex, absCubeShapeView ->
                var checkAreaValid = false
                val auxiliaryPoints =
                    getAuxiliaryPoints4Shape(absCubeShapeView, ImgPowerShapeView.SIZE_POWER)
                var maxY = 0f
                auxiliaryPoints.forEachIndexed { index, floats ->
                    val center = PointF(floats[0], floats[1])
                    /* 确认哪个点有空余地方 */
                    if (checkAreaValid(center)) {
                        val directionTag = when {
                            isTriangleShape() || isYShape() -> {
                                2 - index
                            }

                            isHexagon() -> {
                                getInstallNumberWithDirectionTag(Shape.TYPE_HEXAGON, index)
                            }

                            else -> {
                                index
                            }
                        }
                        val optionalEdgeNumbers = absCubeShapeView.mMultiPower.optionalEdgeNumbers
                        optionalEdgeNumbers.add(directionTag)

                        // 多个辅助点可画，优先 Y 轴最大（存在相同 Y 轴的情况下，与 iOS 保持一致）
                        if (center.y > maxY) {
                            maxY = center.y
                            // 记录辅助坐标（这个地方得确认下是什么关系,跟方向有关系）
                            // FIXME: 2021/12/20  这个地方需要修改

                            log(
                                TAG,
                                "shapeIndex=$shapeIndex index=$index directionTag = $directionTag"
                            )
                            absCubeShapeView.mMultiPower.powerEdgeNUmber = directionTag

                            absCubeShapeView.setPowerPointF(center.x, center.y)
                        }
                        checkAreaValid = true
                    }
                }
                /* 通用 */
                absCubeShapeView.isNeedPowerView = checkAreaValid
                changeShapeLayoutParams(absCubeShapeView, !checkAreaValid)

                /* 只有设置电源的的时候才有点击事件 */
                checkAreaValid.yes {
                    powerViewPoints.add(absCubeShapeView)
                    /* 给可点击的多边形设置点击事件 */
                    absCubeShapeView.setOnClickListener {
                        onPowerItemClick(powerSize + shapeIndex)
                    }
                    if (absCubeShapeView.isAddMultiPower) {
                        absCubeShapeView.setMultiplePowerColor(
                            true,
                            absCubeShapeView.color4selecting,
                            getColor(R.color.ui_color_block_style_16_6_stroke)
                        )
                        absCubeShapeView.z = 2f
                    } else {
                        absCubeShapeView.setMultiplePowerColor(
                            true,
                            absCubeShapeView.color4canSelect
                        )
                        absCubeShapeView.z = 0f
                    }
                }.no {
                    absCubeShapeView.setMultiplePowerColor(false, absCubeShapeView.color4unselected)
                    absCubeShapeView.z = 0f
                }
            }
            /* 根据 y 轴排序 */
            powerViewPoints.sortBy { abs: AbsCubeShapeView -> ceil(abs.powerPointF!!.y) }

            /* 辅助电源点集合如果不为空且在最后一个（Y轴最大） */
            powerViewPoints.lastOrNull()?.apply {
                multiPowerView?.apply {
                    val pos1 = this.pos
                    pos1?.let {
                        if (!checkAreaValid(it)) {
                            shapeViews.forEach { abs ->
                                if (abs.isAddMultiPower) {

                                    changeShapeLayoutParams(abs, !abs.isNeedPowerView)

                                    if (abs.isNeedPowerView) {
                                        // 是否需要添加
                                        abs.setMultiplePowerColor(true, abs.color4canSelect)
                                    } else {
                                        abs.setMultiplePowerColor(false, abs.color4unselected)
                                    }
                                    abs.isAddMultiPower = false
                                    abs.z = 0f
                                }
                            }
                            <EMAIL>(multiPowerView)
                            multiPowerView = null
                        }
                    }
                }
                if (multiPowerView == null) {
                    log(TAG, "checkMorePowerSource: addPowerSourceView(), $multiPowerView")
                    multiPowerView = addPowerSourceView(this.powerPointF!!)
                    this.setMultiplePowerColor(
                        true,
                        this.color4selecting,
                        getColor(R.color.ui_color_block_style_16_6_stroke)
                    )
                    this.isAddMultiPower = true
                    z = 2f
                }
            }
        }
    }

    private fun addPowerSourceView(pos: PointF): ImgPowerShapeView {
        return ImgPowerShapeView(context).apply {
            this.pos = pos
            val lp = LayoutParams(ImgPowerShapeView.SIZE_POWER, ImgPowerShapeView.SIZE_POWER)
            lp.leftMargin = pos.x.toInt() - ImgPowerShapeView.SIZE_POWER / 2
            lp.topMargin = pos.y.toInt() - ImgPowerShapeView.SIZE_POWER / 2
            layoutParams = lp
        }.also {
            addView(it)
        }
    }

    /*更新路径数量及位置*/
    private fun updatePathItem() {
        /*清空所有添加按钮*/
        pathViews.forEach {
            it.clearAnimation()
            removeView(it)
        }
        pathViews.clear()
        if (isYShape()) {
            // 动态
            shapeViews.filterIndexed { index, filter -> index > 0 && (filter.isInstalledUI || filter.isInstallingUI) }
                .forEach { absCubeShapeView ->
                    val offsetRotation =
                        absCubeShapeView.offsetRotation + absCubeShapeView.canvasRotation
                    val overRotation = 20f
                    val shapePoint =
                        absCubeShapeView.getShapePoint(
                            absCubeShapeView.pos!!,
                            OUTSIDE_LINE_LENGTH,
                            offsetRotation + overRotation,
                            false
                        )
                    val centerPoint = shapePoint[2]
                    val centerX = centerPoint[0] - DynamicImgPathShapeView.SIZE_WIDTH / 2
                    val centerY = centerPoint[1] - DynamicImgPathShapeView.SIZE_HEIGHT / 2
                    DynamicImgPathShapeView(context).apply {
                        setData(offsetRotation, -1)
                        val lp = LayoutParams(
                            DynamicImgPathShapeView.SIZE_WIDTH,
                            DynamicImgPathShapeView.SIZE_HEIGHT
                        )
                        lp.leftMargin = centerX.toInt()
                        lp.topMargin = centerY.toInt()
                        layoutParams = lp
                    }.also {
                        this.addView(it)
                        pathViews.add(it)
                    }.let {
                        if (absCubeShapeView.isInstallingUI) {
                            val centerPointF = absCubeShapeView.pos!!

                            val startShapePoint =
                                absCubeShapeView.getShapePoint(
                                    centerPointF,
                                    OUTSIDE_LINE_LENGTH - 20,
                                    offsetRotation + 30,
                                    false
                                )

                            val endShapePoint =
                                absCubeShapeView.getShapePoint(
                                    centerPointF,
                                    OUTSIDE_LINE_LENGTH + 20,
                                    offsetRotation + 13,
                                    false
                                )

                            val startPointFX =
                                startShapePoint[2][0] - DynamicImgPathShapeView.SIZE_WIDTH / 2 - centerX
                            val startPointFY =
                                startShapePoint[2][1] - DynamicImgPathShapeView.SIZE_HEIGHT / 2 - centerY

                            val endPointFX =
                                endShapePoint[2][0] - DynamicImgPathShapeView.SIZE_WIDTH / 2 - centerX
                            val endPointFY =
                                endShapePoint[2][1] - DynamicImgPathShapeView.SIZE_HEIGHT / 2 - centerY

                            val tAnim = TranslateAnimation(
                                startPointFX,
                                endPointFX,
                                startPointFY,
                                endPointFY
                            ) //设置视图上下移动的位置

                            tAnim.duration = 1000
                            tAnim.repeatCount = Animation.INFINITE
                            tAnim.repeatMode = Animation.INFINITE
                            it.animation = tAnim
                            tAnim.start()
                        }
                    }
                }
            return
        }

        if (lastedShape == null || shapeViews.size > maxCount) return

        var lastShape: AbsCubeShapeView? = null

        shapeViews.forEachIndexed { index, curShape ->
            /*画路径图标*/
            ImgPathShapeView(context).apply {
                val prevShapeView =
                    if (isYShape() && curShape.lastPosition >= 0 && curShape.lastPosition < shapeViews.size) shapeViews[curShape.lastPosition] else lastShape

                val lastPos = prevShapeView?.pos ?: powerView?.pos
                var directionTag = 0
                val realRotation = prevShapeView?.let {
                    log(
                        "cal updatePathItem",
                        "canvasRotation = ${it.canvasRotation}  offsetRotation = ${it.offsetRotation}  TagOffset = ${
                            it.getRotationByDirectionTag(it.nextDirectionTag)
                        }"
                    )

                    directionTag = it.offsetNextDirectionTag[index] ?: it.nextDirectionTag
                    it.canvasRotation + it.offsetRotation + it.getRotationByDirectionTag(
                        directionTag
                    )
                } ?: canvasRotation

                log(
                    "updatePathItem",
                    "lastShape.nextTag = ${prevShapeView?.nextDirectionTag}  realRotation = $realRotation"
                )

                val pathNumTag = if (Constants.isShowInstallPathNumber(cubeShapeViewType)) {
                    Constants.getInstallNumberWithDirectionTag(
                        cubeShapeViewType,
                        prevShapeView?.nextDirectionTag ?: -1
                    )
                } else -1

                var centerX = curShape.pos!!.x
                var centerY = curShape.pos!!.y

                lastPos?.let {
                    centerX = (centerX + it.x) / 2
                    centerY = (centerY + it.y) / 2
                }

                pos.let {
                    x = centerX
                    y = centerY
                }
                showLog(
                    TAG,
                    content = "updatePathView111:  realRotation = $realRotation , pos = $centerX ，$centerY , $directionTag"
                )

                /*画路径view*/
                /*实际灯的编号位置*/
                setData(realRotation, pathNumTag)

                if (mType == Type.Install && pathViews.size >= 2) {
                    alpha = 0.4f
                }

//                val lp = LayoutParams(ImgPathShapeView.SIZE_WIDTH, ImgPathShapeView.SIZE_HEIGHT)
//                lp.leftMargin = centerX.toInt() - ImgPathShapeView.SIZE_WIDTH / 2
//                lp.topMargin = centerY.toInt() - ImgPathShapeView.SIZE_HEIGHT / 2
//                layoutParams = lp
                setLayoutParams(pos)
            }.also {
                addView(it)
                pathViews.add(it)
            }
            lastShape = curShape

            checkMultiPowerPath(curShape)
        }
    }

    private fun checkMultiPowerPath(curShape: AbsCubeShapeView) {
        if (curShape.isAddMultiPower) {
            // 添加辅助电源
            val mMultiPower = curShape.mMultiPower
            val powerNumber = mMultiPower.powerNumber
            val powerEdgeNUmber = mMultiPower.powerEdgeNUmber
            if (powerNumber > 0 && powerEdgeNUmber > 0) {
                val tag = when {
                    isTriangleShape() || isYShape() -> {
                        2 - powerEdgeNUmber
                    }

                    isHexagon() -> {
                        when (powerEdgeNUmber) {
                            4 -> 0
                            3 -> 1
                            2 -> 2
                            1 -> 3
                            5 -> 5
                            else -> 4
                        }
                    }

                    else -> {
                        powerEdgeNUmber
                    }
                }
                val rotationByDirectionTag = curShape.getRotationByDirectionTag(tag)
                val rotation =
                    curShape.offsetRotation + curShape.canvasRotation + rotationByDirectionTag

                val real = rotation % 360 + 180 // 180为path箭头调转
                val centerOffset =
                    curShape.getShapeRotation(curShape.pos!!, INNER_LINE_LENGTH, rotation)

                ImgPathShapeView(context).apply {
                    setLayoutParams(centerOffset)

                    /*画路径view*/
                    /*实际灯的编号位置*/
                    setData(real, -1)

                    if (mType == Type.Install && pathViews.size >= 2) {
                        alpha = 0.4f
                    }

                }.also {
                    addView(it)
                    multiPowerPathView = it
                }
            }
        }
    }

    private val focusPointViews = mutableListOf<FocusPointView>()

    private fun updateFocusPoint4YShape() {
        focusPointViews.onEach {
            removeView(it)
        }.clear()
        if (mType != Type.Focus) return

        mFocusPointList.onEach {
            addFocusPointView(it.pointX, it.pointY).apply {
                shapeTotalQuantity = it.shapeTotalQuantity
                selectShapeIndex = it.selectShapeIndex
                selectEdgeIndex = it.selectEdgeIndex
                selectEdgeIcIndex = it.selectEdgeIcIndex
                isCenterPoint = it.isCenterPoint
            }
        }
    }

    private fun updateFocusPoint4YShapeScale() {
        if (mType != Type.Focus && !isYShape()) return
        focusPointViews.forEach {
            it.setScale(scaleX)
        }
    }

    private fun addFocusPointView(locationX: Float, locationY: Float): FocusPointView {
        return FocusPointView(context).apply {
            val lp = LayoutParams(FocusPointView.SIZE_FOCUS, FocusPointView.SIZE_FOCUS)
            lp.leftMargin = (locationX - FocusPointView.SIZE_FOCUS / 2f).toInt()
            lp.topMargin = (locationY - FocusPointView.SIZE_FOCUS / 2f).toInt()
            layoutParams = lp
            pointF.x = locationX
            pointF.y = locationY

            setOnClickListener {
                it?.let {
                    val focusPointView = it as FocusPointView
                    resetFocusPointStatus(focusPointView)
                    val params = byteArrayOf(
                        focusPointView.shapeTotalQuantity.toByte(),
                        focusPointView.selectShapeIndex.toByte(),
                        focusPointView.selectEdgeIndex.toByte(),
                        focusPointView.selectEdgeIcIndex.toByte()
                    )
                    focusDragCallback?.selectedFocusPoint(params)
                }
            }
        }.also {
            addView(it)
            focusPointViews.add(it)
        }
    }

    private fun resetFocusPointStatus(focusPointView: FocusPointView) {
        focusPointViews.forEach {
            it.isSelected = it == focusPointView
        }
    }

    fun resetDefaultFocusPoint(callback: Boolean): PointF {
        log(TAG, "resetDefaultFocusPoint(), callback = $callback")
        val handleYShapeCenterFocus = handleYShapeCenterFocus()

        handleYShapeCenterFocus.let {
            val byteArrayOf1 = byteArrayOf(
                it.shapeTotalQuantity.toByte(),
                it.selectShapeIndex.toByte(),
                it.selectEdgeIndex.toByte(),
                it.selectEdgeIcIndex.toByte()
            )
            if (callback) {
                focusDragCallback?.selectedFocusPoint(byteArrayOf1)
            } else {
                updateFocusPoint4YShape(byteArrayOf1)
            }
            return PointF(it.pointX, it.pointY)
        }
    }

    private val mFocusPointList by lazy { mutableListOf<FocusPoint>() }

    private fun handleYShapeCenterFocus(): FocusPoint {
        // Y 型灯，中心点计算方式
        // 1、计算当前图形的重心点，遍历当前可选择的点的位置累加得到重心点 x，y
        // 2、然后计算重心点到每一个可选择点的距离（图形端点*112，图形中心点系数*97）从小到大排序，得出最小距离的点的集合
        // 3、根据最小距离的点的集合，按照 Y 轴从小到大排序，取 Y 轴最大的集合
        // 4、若最大 Y 轴的点集合数量为1，则取该点。若数量>1，则按照 x 点从小到大排序，然后取中位数（/2），得到数组下标，取该点

        var heartPointX = 0f
        var heartPointY = 0f

        val calculateList = mutableListOf<FocusPoint>()

        mFocusPointList.onEach {
            heartPointX += it.pointX
            heartPointY += it.pointY
        }.apply {
            heartPointX /= size
            heartPointY /= size
        }.onEach {
            val distance =
                Utils.distance(PointF(heartPointX, heartPointY), PointF(it.pointX, it.pointY))
                    .roundToInt()
            if (it.isCenterPoint) {
                it.distance = distance * 97
            } else {
                it.distance = distance * 112
            }
        }.apply {
            sortBy { it.distance }
        }
        val first = mFocusPointList.first()
        calculateList.apply {
            addAll(mFocusPointList.filter { it.distance == first.distance })
        }.sortBy {
            it.pointY
        }
        val last = calculateList.last()
        calculateList.filter { it.pointY == last.pointY }
        if (calculateList.size > 1) {
            calculateList.sortBy { it.pointX }
            val centerPointIndex = calculateList.size / 2
            calculateList[centerPointIndex]
        } else {
            calculateList.first()
        }.apply {
            return this
        }
    }


    private fun initYShapeFocusPoints() {
        if (!isYShape()) return
        mFocusPointList.clear()
        shapeViews.forEachIndexed { index, absCubeShapeView ->
            val centerPointF = absCubeShapeView.getShapePoint(
                absCubeShapeView.pos ?: PointF(),
                OUTSIDE_LINE_LENGTH,
                absCubeShapeView.offsetRotation + canvasRotation, false
            )
            val pos = absCubeShapeView.pos
            pos?.let {
                /*中心点*/
                mFocusPointList.add(
                    FocusPoint(
                        it.x,
                        it.y,
                        shapeViews.size,
                        index,
                        0xFF,
                        0xFF,
                        true
                    )
                )
            }

            /*0、通过 index 可以得知当前选中的是哪一块*/
            /*2、确认 centerPointF 返回的点中的序号,且与 yShapeIc 保持一致*/
            centerPointF.forEachIndexed { pointFIndex, floats ->
                var tempIndex = pointFIndex + 1
                if (tempIndex > 2) tempIndex = 0
                mFocusPointList.add(
                    FocusPoint(
                        floats[0],
                        floats[1],
                        shapeViews.size,
                        index,
                        tempIndex,
                        /*默认值，这个参数是指，当前这点边沿着中心点的方向从0...3，默认参数:0xff*/
                        /*但是由于 5.2.0的 UI 效果是两个块中间，需要发 0xff*/
                        0xFF
                    )
                )
            }
        }
    }

    fun getYShapeDefaultFocusPoint(): ByteArray {
        log(TAG, "getCurrentFocusPoint4YShape()")
        val yShapeCenterFocusPoint = handleYShapeCenterFocus()

        val byteArrayOf = ByteArray(4)
        byteArrayOf[0] = yShapeCenterFocusPoint.shapeTotalQuantity.toByte()
        byteArrayOf[1] = yShapeCenterFocusPoint.selectShapeIndex.toByte()
        byteArrayOf[2] = yShapeCenterFocusPoint.selectEdgeIndex.toByte()
        byteArrayOf[3] = yShapeCenterFocusPoint.selectEdgeIcIndex.toByte()
        return byteArrayOf
    }

    fun updateFocusPoint4YShape(params: ByteArray?): PointF {
        val pointF = PointF(mShapeCenterPoint!!.x, mShapeCenterPoint!!.y)
        if (focusPointViews.isEmpty()) return pointF
        params?.apply {
            log(TAG, "updateFocusPoint4YShape params = ${this[0]} ${this[1]} ${this[2]} ${this[3]}")
            if (this[0].toInt() != shapeViews.size) {
                val yShapeCenterFocus = handleYShapeCenterFocus()
                this[0] = yShapeCenterFocus.shapeTotalQuantity.toByte()
                this[1] = yShapeCenterFocus.selectShapeIndex.toByte()
                this[2] = yShapeCenterFocus.selectEdgeIndex.toByte()
                this[3] = yShapeCenterFocus.selectEdgeIcIndex.toByte()
            }
            focusPointViews.onEach { it.isSelected = false }
                .filter {
                    it.selectShapeIndex == Utils.byte2Int(this[1])
                        && it.selectEdgeIndex == Utils.byte2Int(this[2])
                        && it.selectEdgeIcIndex == Utils.byte2Int(this[3])
                }.forEach {
                    it.isSelected = true
                    pointF.x = it.pointF.x
                    pointF.y = it.pointF.y
                }
        }
        return pointF
    }

    /*返回选中的点*/
    private fun updateFocusDragItem(): Point {
        /*图标占的整个图形的中心点*/
        if (isYShape()) {
            updateFocusPoint4YShape()
            return Point(0, 0)
        }
        updateFocusDragItem(mShapeCenterPoint!!)
        return Point(mShapeCenterPoint!!.x.toInt(), mShapeCenterPoint!!.y.toInt())
    }

    /*更新聚焦item位置*/
    private fun updateFocusDragItem(selectPoint: PointF) {
        showLog(TAG, "updateFocusDragItem: selectPoint = ${selectPoint} ")
        /*清除最后一个删除*/
        focusDragView?.let { removeView(it) }
        /*图标占的整个图形的中心点*/
        focusDragView = FocusDragView(context).apply {
            pos = selectPoint
            /*处于绘制最高级*/
            z = 2f
            /*聚焦&扩散*/
            setIsIn(isIn)
            /*拖拽回调*/
            focusDragCallback = { pointF: PointF ->
                <EMAIL>?.focusDragPointChange(pointF)
            }
            val lp = LayoutParams(FocusDragView.SIZE_FOCUS, FocusDragView.SIZE_FOCUS)
            lp.leftMargin = selectPoint.x.toInt() - FocusDragView.SIZE_FOCUS / 2
            lp.topMargin = selectPoint.y.toInt() - FocusDragView.SIZE_FOCUS / 2
            layoutParams = lp
        }.also {
            addView(it)
        }

        updateDragViewScale(mFocusDragViewScale)
    }

    /*获取到已占用的区域*/
    private fun getSelectedArea(): Array<FloatArray> {
        val arrayArea =
            Array(shapeViews.size + if (powerView?.pos != null) 1 else 0) { FloatArray(4) }
        for (i in shapeViews.size - 1 downTo 0) {
            arrayArea[i] = shapeViews[i].shapeArea4Parent
        }
        /*加上电源位置*/
        powerView?.pos?.apply {
            arrayArea[shapeViews.size] =
                floatArrayOf(
                    x - Shape.LINE_LENGTH_4_SQURA,
                    x + Shape.LINE_LENGTH_4_SQURA,
                    y - Shape.LINE_LENGTH_4_SQURA,
                    y + Shape.LINE_LENGTH_4_SQURA
                )
        }
        return arrayArea
    }

    /*检查点是否在有效区域内*/
    private fun checkAreaValid(point: PointF): Boolean {
        val area = getSelectedArea()
        area.forEach {
            if (point.x >= it[0] && point.x <= it[1] && point.y >= it[2] && point.y <= it[3]) {
                return false
            }
        }
        /*是否在在画布范围内*/

        if (point.x - CANVAS_PADDING <= 0 || point.x + CANVAS_PADDING >= canvasSize || point.y - CANVAS_PADDING <= 0 || point.y + CANVAS_PADDING >= canvasSize) {
            return false
        }

        //点击位置x坐标与圆心的x坐标的距离
        val distanceX: Float = abs(canvasSize / 2 - point.x)
        //点击位置y坐标与圆心的y坐标的距离
        val distanceY: Float = abs(canvasSize / 2 - point.y)
        //点击位置与圆心的直线距离
        val distanceZ = sqrt(distanceX.toDouble().pow(2.0) + distanceY.toDouble().pow(2.0))

        //如果点击位置与圆心的距离大于圆的半径，证明点击位置没有在圆内
        if (distanceZ > canvasSize / 2 - CANVAS_PADDING) {
            return false
        }
        return true
    }

    private val simpleShapeViewsRange: FloatArray
        get() {
            var minX = 0f
            var maxX = 0f
            var minY = 0f
            var maxY = 0f
            shapeViews.apply {
                first().let {
                    var self = true
                    if (isYShape()) {
                        self = false
                    }
                    val shapePoint4Canvas = it.getShapePoint4Canvas(self)
                    shapePoint4Canvas.forEach {
                        minX = it[0]
                        maxX = it[0]
                        minY = it[1]
                        maxY = it[1]
                    }
                }
            }.forEach {
                var self = true
                if (isYShape()) {
                    self = false
                }
                val shapePoint = it.getShapePoint4Canvas(self)
                shapePoint.forEach {
                    minX = min(it[0], minX)
                    maxX = max(it[0], maxX)
                    minY = min(it[1], minY)
                    maxY = max(it[1], maxY)
                }
            }
            return FloatArray(4).apply {
                this[0] = minX
                this[1] = maxX
                this[2] = minY
                this[3] = maxY
            }
        }

    private val shapeViewsRange: FloatArray
        get() {
            var minX = 0f
            var maxX = 0f
            var minY = 0f
            var maxY = 0f
            shapeViews.apply {
                first().let {
                    val shapePoint4Canvas = it.getShapePoint4Canvas()
                    shapePoint4Canvas.forEach {
                        minX = it[0]
                        maxX = it[0]
                        minY = it[1]
                        maxY = it[1]
                    }
                }
            }.forEach {
                val shapePoint = it.getShapePoint4Canvas()
                shapePoint.forEach {
                    minX = min(it[0], minX)
                    maxX = max(it[0], maxX)
                    minY = min(it[1], minY)
                    maxY = max(it[1], maxY)
                }
            }
            return FloatArray(4).apply {
                this[0] = minX
                this[1] = maxX
                this[2] = minY
                this[3] = maxY
            }
        }
    private val tempShapeViews: MutableList<AbsCubeShapeView>
        get() {
            return ArrayList<AbsCubeShapeView>().apply {
                addAll(shapeViews)
            }
        }

    private val shapeViewsMoveRange: FloatArray
        get() {
            /* 根据 y 轴排序 */
            tempShapeViews.sortBy { abs: AbsCubeShapeView -> ceil(abs.pos!!.x) }
            val minXShape: AbsCubeShapeView = tempShapeViews.first()
            val maxXShape: AbsCubeShapeView = tempShapeViews.last()

            tempShapeViews.sortBy { abs: AbsCubeShapeView -> ceil(abs.pos!!.x) }

            val shapePoint4Canvas = minXShape.getShapePoint4Canvas()
            val shapePoint4Canvas1 = maxXShape.getShapePoint4Canvas()

            shapePoint4Canvas.sortBy { it[1] }
            shapePoint4Canvas1.sortBy { it[1] }

            /*
            * 中心点最左上角的图形，取其图形的点的最大 y 和最大的 x
            * 中心点最左下角的图形，取其图形的点的最小 y 和最大的 x
            * 中心点最右上角的图形，取其图形的点的最大 y 和最小的 x
            * 中心点最右下角的图形，取其图形的点的最小 y 和最小的 x
            * */

            log(TAG, "minXShape = ${shapePoint4Canvas.last()}")
            log(TAG, "maxXShape = ${shapePoint4Canvas1.first()}")



            return shapePoint4Canvas.first()
        }

    fun getShapeMovableRange(): FloatArray {
        // TODO:  搁置
        return shapeViewsMoveRange

    }

    fun getShapeSize(): PointF {
        val shapeWidth = abs(shapeViewsRange[1] - shapeViewsRange[0])
        val shapeHeight = abs(shapeViewsRange[3] - shapeViewsRange[2])
        return PointF(shapeWidth, shapeHeight)
    }

    fun getSingleShapeViewLength(): Float {
        if (isYShape()) {
            return 112f
        }
        return shapeViews.first().getShapeLineLength()
    }

    fun getPhysicalShapeSize(): PointF {
        val shapeWidth = abs(simpleShapeViewsRange[1] - simpleShapeViewsRange[0])
        val shapeHeight = abs(simpleShapeViewsRange[3] - simpleShapeViewsRange[2])

        val fetchShapePhysicalSize = Constants.fetchShapePhysicalSize(cubeShapeViewType)

        val shapeLineLength = shapeViews.first().getShapeLineLength().roundToInt()
        val fl = fetchShapePhysicalSize / 10f
        val proportion = fl / shapeLineLength

        val physicalShapeSize = PointF(shapeWidth * proportion, shapeHeight * proportion)
        log(
            TAG,
            "getPhysicalShapeSize(), physicalShapeSize = $physicalShapeSize ,  ${physicalShapeSize.x} ${physicalShapeSize.y}"
        )
        return physicalShapeSize
    }

    /*点击了添加按钮*/
    private fun onAddItemClick(shapeViewIndex: Int, directionFlag: Int) {
        /*最大数量限制*/
        if (shapeViews.size >= maxCount) return
        addShape(directionFlag, updateOtherUi = true, shapeViewIndex)

        addDeleteCallback?.shapeSizeChange(shapeViews.size)
    }

    /**
     * 可选择多个电源的点击事件
     * @param position 点击对象的下标
     * */
    private fun onPowerItemClick(position: Int) {
        shapeViews.forEachIndexed { index, absCubeShapeView ->
            if (index == position) {
                changeShapeLayoutParams(absCubeShapeView, false)
                absCubeShapeView.setMultiplePowerColor(
                    true,
                    absCubeShapeView.color4selecting,
                    getColor(R.color.ui_color_block_style_16_6_stroke)
                )

                absCubeShapeView.isAddMultiPower = true
                absCubeShapeView.z = 2f

                /* 移除当前的辅助电源图形 */
                multiPowerView?.apply { removeView(this) }
                val powerPointF = absCubeShapeView.powerPointF
                absCubeShapeView.isAddMultiPower = true
                powerPointF?.let {
                    /* 绘制当前点击多边形的辅助电源 */
                    multiPowerView = addPowerSourceView(it)
                }
            } else {
                changeShapeLayoutParams(absCubeShapeView, !absCubeShapeView.isNeedPowerView)

                if (absCubeShapeView.isNeedPowerView) {
                    // 是否需要添加
                    absCubeShapeView.setMultiplePowerColor(true, absCubeShapeView.color4canSelect)
                } else {
                    absCubeShapeView.setMultiplePowerColor(false, absCubeShapeView.color4unselected)
                }
                absCubeShapeView.isAddMultiPower = false
                absCubeShapeView.z = 0f
            }
        }
    }

    private fun changeShapeLayoutParams(absCubeShapeView: AbsCubeShapeView, lineLength: Boolean) {
        absCubeShapeView.setLayoutParams(
            absCubeShapeView.pos!!.x.roundToInt(),
            absCubeShapeView.pos!!.y.roundToInt(),
            if (lineLength) LINE_LENGTH else INNER_LINE_LENGTH
        )
    }

    private fun isTriangleShape(): Boolean {
        return cubeShapeViewType == Shape.TYPE_TRIANGLE
    }

    private fun isHexagon(): Boolean {
        return cubeShapeViewType == Shape.TYPE_HEXAGON
    }

    private fun isSolidHexagon(): Boolean {
        return cubeShapeViewType == Shape.TYPE_SOLID_HEXAGON
    }

    private fun isSpaceHexagon(): Boolean {
        return cubeShapeViewType == Shape.TYPE_SPACE_HEXAGON
    }

    private fun isYShape(): Boolean {
        return cubeShapeViewType == Shape.TYPE_Y
    }

    fun setShapeData(points: ArrayList<ShapePosition>, ty: Type) {
//        points.firstNotNullOf {
//            setCubeShapeViewType(it.type)
//        }
        setShapeData(points, ty, true)
    }

    /*更新Type*/
    fun updateDataWithType(ty: Type, needResetCanvas: Boolean) {
        val data = getShapeRotations()
        setShapeData(data, ty, needResetCanvas)
    }

    fun resetPreViewRandomBackground() {
        val color4PreViewBg = fetchRoundColor4PreViewBg()
        val newRondColor = IntArray(5)
        color4PreViewBg?.let {
            newRondColor[0] = it[0]
            newRondColor[1] = it[1]
            newRondColor[2] = it[2]
        }
        roundColor4PreViewBg = newRondColor
    }

    fun setShapePreViewBackground(strokeColor: Int, inlineColor: Int = 0) {
        if (mType != Type.Preview) return
        if (isSolidHexagon() || isYShape() || isSpaceHexagon()) {
            roundColor4PreViewBg?.apply {
                this[3] = Color.WHITE
                this[4] = inlineColor
            }
            shapeViews.forEach {
                setSolidHexagonShapeViewBg(it)
            }
            return
        }

        val copy = BitmapFactory.decodeResource(context.resources, R.mipmap.new_light_panel_bg)
            .copy(Bitmap.Config.ARGB_8888, true)
        val matrix = Matrix()

        // 最小尺寸
        val with = when (cubeShapeViewType) {
            Shape.TYPE_TRIANGLE -> {
                (OUTSIDE_LINE_LENGTH * 2).roundToInt() * 1.0f
            }

            else -> {
                (LINE_LENGTH * 2).roundToInt() * 1.0f
            }
        }

        val shapeSize = getShapeSize()
        val maxShapeSize = max(max(shapeSize.x, shapeSize.y), with)
        val scale = maxShapeSize / copy.width
        matrix.postScale(scale, scale) //长和宽放大缩小的比例
        preView = Bitmap.createBitmap(copy, 0, 0, copy.width, copy.height, matrix, true)


        val minPointF = PointF(shapeViewsRange[0], shapeViewsRange[2])// 图形左上角的点
        shapeViews.forEach { shape ->
            shape.pos?.let {
                val moveChangePointFx = it.x - minPointF.x
                val moveChangePointFy = it.y - minPointF.y
                setShapeViewBg(shape, PointF(moveChangePointFx, moveChangePointFy))
            }
            shape.setPreViewMode(strokeColor)
        }
    }

    private fun setSolidHexagonShapeViewBg(it: AbsCubeShapeView) {
        if (mType != Type.Preview) return
        var preRotate: Float

        if (shapeViews.isEmpty()) {
            preRotate = it.canvasRotation + it.offsetRotation
        } else {
            shapeViews.first().let {
                preRotate = it.canvasRotation + it.offsetRotation
            }
        }

        it.setPreView(preRotate, roundColor4PreViewBg)
    }

    private fun setShapeViewBg(it: AbsCubeShapeView, point: PointF) {

        val with = when (cubeShapeViewType) {
            Shape.TYPE_TRIANGLE -> {
                (OUTSIDE_LINE_LENGTH * 2).roundToInt()
            }

            else -> {
                (LINE_LENGTH * 2).roundToInt()
            }
        }

        point.x = min(abs(point.x) - with / 2, preView!!.width.toFloat() - with).coerceAtLeast(0f)
        point.y = min(abs(point.y) - with / 2, preView!!.height.toFloat() - with).coerceAtLeast(0f)
        if (point.x + with > preView!!.width) {
            point.x = (preView!!.width.toFloat() - with).coerceAtLeast(0f)
        }
        if (point.y + with > preView!!.height) {
            point.y = (preView!!.height.toFloat() - with).coerceAtLeast(0f)
        }

        it.pos?.apply {
            try {
                val createBitmap =
                    Bitmap.createBitmap(
                        preView!!,
                        point.x.toInt(),
                        point.y.toInt(),
                        with,
                        with,
                        null,
                        false
                    )
                it.setBackgroundRes(createBitmap)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /*设置图形数据，根据类型*/
    fun setShapeData(points: ArrayList<ShapePosition>, ty: Type, needUpdateCanvas: Boolean) {
        this.mType = ty
        reSetCanvas(false)
        val defRotation = Constants.getShapeDefaultAngle(cubeShapeViewType)
        canvasRotation = ((points.getOrNull(0)?.angle ?: defRotation).toFloat()) - defRotation
        val pointSize = points.size
        val canvasCenterPoint = canvasSize / 2f
        if (ty == Type.DefaultType || ty == Type.Edit || ty == Type.Install || ty == Type.Check || ty == Type.ColorMode || ty == Type.Focus || ty == Type.MultiPower || ty == Type.Preview || ty == Type.AddLamp) {
            val allShapeArea =
                floatArrayOf(
                    canvasCenterPoint,
                    canvasCenterPoint,
                    canvasCenterPoint,
                    canvasCenterPoint
                )
            if (points.isNotEmpty()) {
                mLastOffsetRotation = points.first().angle
            }
            shapeTree.reset()
            shapeTree3.reset()
            points.forEachIndexed { index, p ->

                /*添加图形，不更新其他view*/
                val areaPoint = when {
                    isTriangleShape() -> {
                        addShapeWithAngle1(p.angle)
                    }

                    isYShape() -> {
                        addShapeWithAngle2(index, points, p)
                    }

                    isSpaceHexagon() -> {
                        addShapeWithAngle(p.x, p.y, p.angle)
                    }

                    else -> {
                        Utils.checkNextPoint(
                            lastedPointF,
                            PointF(p.x, p.y),
                            30f,
                            INNER_LINE_LENGTH * 2
                        )
                            ?.let {
                                log(
                                    "checkNextPoint",
                                    "canvasRotation = $canvasRotation  origin x,y=${p.x}, ${p.y}; after x,y=${it.x}, ${it.y}"
                                )
                                p.x = it.x
                                p.y = it.y
                            }
                        addShapeWithAngle(p.x, p.y, p.angle)
                    }
                }
                /*左右上下*/
                allShapeArea[0] = min(allShapeArea[0], areaPoint[0])
                allShapeArea[1] = max(allShapeArea[1], areaPoint[1])
                allShapeArea[2] = min(allShapeArea[2], areaPoint[2])
                allShapeArea[3] = max(allShapeArea[3], areaPoint[3])

                /* 根据类型进行处理 */
                if (p.isExt && (ty == Type.Edit || ty == Type.MultiPower || ty == Type.Install || ty == Type.Check || ty == Type.AddLamp)) {
                    addMultiPower(pointSize, p)
                }
            }

            if ((ty == Type.Check && shapeViews.getOrNull(0)?.cubeConfig?.showNumTagCheckedMode == true)
                || (ty == Type.Install && shapeViews.getOrNull(0)?.cubeConfig?.showNumTagInInstallMode == true)
            ) {
                /*更新编号*/
                var num = 1
                shapeViews.forEach {
                    it.setNumberText("${num++}")
                }
            }
            /*整体的宽高*/
            val shapeWidth = allShapeArea[1] - allShapeArea[0]
            val shapeHeight = allShapeArea[3] - allShapeArea[2]
            /*整个图形相对于画布中心的坐标*/
            val shapeOffsetPoint =
                PointF(
                    -(allShapeArea[1] - shapeWidth / 2 - canvasCenterPoint),
                    -(allShapeArea[3] - shapeHeight / 2 - canvasCenterPoint)
                )
            mShapeCenterPoint =
                PointF(
                    (allShapeArea[1] + allShapeArea[0]) / 2,
                    (allShapeArea[3] + allShapeArea[2]) / 2
                )
            /*整体相对于画布的拉伸*/
            if (needUpdateCanvas) {
                val scale = max(shapeWidth, shapeHeight) / canvasSize
                mDefaultScale = scale
                mDefaultOffsetPoint = shapeOffsetPoint
                callBack?.scaleAndTransition(scale, shapeOffsetPoint)
            }
            initYShapeFocusPoints()
            /*全部加完再更新UI*/
            updateUIState()
        }
    }

    private fun addMultiPower(pointSize: Int, p: ShapePosition) {
        if (p.ext is MultiPower) {
            val ext = p.ext as MultiPower
            val powerNumber = ext.powerNumber
            val powerEdgeNUmber = ext.powerEdgeNUmber
            val optionalEdgeNumbers = ext.optionalEdgeNumbers
            val flag = Constants.isMultiPower(pointSize, cubeShapeViewType)
            val powerSize = Constants.fetchMultiPowerLimitIndex(pointSize)
            log(
                TAG,
                "addMultiPower: pointSize = $pointSize,  powerNumber = $powerNumber , powerEdgeNUmber = $powerEdgeNUmber , powerSize = $powerSize"
            )
            if (flag && (shapeViews.size >= powerNumber) && (powerNumber > 0) && (powerEdgeNUmber > 0) && powerSize < powerNumber) {

                val absCubeShapeView = shapeViews[powerNumber - 1]
                absCubeShapeView.mMultiPower.powerEdgeNUmber = powerEdgeNUmber
                absCubeShapeView.mMultiPower.powerNumber = powerNumber
                absCubeShapeView.mMultiPower.optionalEdgeNumbers = optionalEdgeNumbers
                absCubeShapeView.isNeedPowerView = true
                absCubeShapeView.isAddMultiPower = true

                /* 编辑模式下，保留已存的辅助电源信息 */
                if (mType == Type.Edit) return
                absCubeShapeView.apply {
                    val rotationByDirectionTag: Float = if (isHexagon()) {
                        val dt = when (powerEdgeNUmber) {
                            4 -> 0
                            3 -> 1
                            2 -> 2
                            1 -> 3
                            5 -> 5
                            else -> 4
                        }
                        this.getRotationByDirectionTag(dt)
                    } else {
                        this.getRotationByDirectionTag(powerEdgeNUmber)
                    }
                    /* 实际旋转 = 自己旋转 + 画布旋转 + 方向旋转的坐标 */
                    val realRotation = this.offsetRotation + canvasRotation + rotationByDirectionTag
                    /* 根据当前多边形的中心点+电源中心点，旋转的角度得到，辅助电源中心点 */
                    val point =
                        getShapeRotation(
                            this.pos!!,
                            INNER_LINE_LENGTH + ImgPowerShapeView.SIZE_POWER / 2,
                            realRotation
                        )
                    multiPowerView = addPowerSourceView(point)
                    if (mType == Type.MultiPower) {
                        this.setMultiplePowerColor(
                            true,
                            this.color4selecting,
                            getColor(R.color.ui_color_block_style_16_6_stroke)
                        )
                        z = 2f
                    }
                    log(TAG, "addMultiPower: addPowerSourceView() $multiPowerView")
                }
            }
        }
    }

    /*获取最终传输给设备的旋转角度*/
    fun getShapeRotations(): ArrayList<ShapePosition> {
        val result = arrayListOf<ShapePosition>()

        shapeViews.forEachIndexed { index, absCubeShapeView ->
            val toShapePosition = absCubeShapeView.toShapePosition()
            if (absCubeShapeView.isAddMultiPower && absCubeShapeView.isNeedPowerView && absCubeShapeView.mMultiPower.powerEdgeNUmber > 0) {
                val multiPower = MultiPower(-1, -1, mutableListOf())
                multiPower.powerEdgeNUmber = absCubeShapeView.mMultiPower.powerEdgeNUmber
                multiPower.powerNumber = index + 1
                multiPower.optionalEdgeNumbers = absCubeShapeView.mMultiPower.optionalEdgeNumbers
                toShapePosition.ext = multiPower
            }

            result.add(toShapePosition)
        }

        if (isYShape()) {
            newShape.reset()
            shapeViews.forEachIndexed { index, absCubeShapeView ->
                if (index == 0) {
                    newShape.insert(0, index, false, absCubeShapeView.toShapePosition())
                } else {
                    val lastPosition = absCubeShapeView.lastPosition
                    val lastView = shapeViews[lastPosition]
                    val i = lastView.offsetNextDirectionTag[index]
                    //
                    newShape.insert(lastPosition, index, i == 2, absCubeShapeView.toShapePosition())
                }
            }

            return newShape.ergodicMid()
        }
        return result
    }

    val newShape by lazy { ShapeTree2() }

    val curInstallIndex: Int
        get() {
            if (mType != Type.Install) return 0
            return shapeViews.let { list ->
                var i = 0
                list.forEach { if (it.alpha >= 1) i++ }
                i
            }
        }


    fun getInstallingIcPos(index: Int): Int {
        shapeViews.getOrNull(index)?.let {
            if (it is SolidHexagonView) {
                return it.getCheckStateIcIndex()
            }
        }
        return 0
    }

    /*安装下一个方块 return true全部校验完了*/
    fun installNextShape(changeLastShapeState: Boolean = false): Pair<AbsCubeShapeView, Int>? {
        if (mType != Type.Install) return null

        val hasInstallNum = shapeViews.let { list ->
            var i = 0
            list.forEach { if (it.alpha >= 1) i++ }
            i
        }

        /*最后一个校验完*/
        if (hasInstallNum >= shapeViews.size) {
            changeLastShapeState.yes {
                lastedShape?.change2InstalledState()
            }
            if (isYShape()) {
                updateUIState()
            }
            return null
        }
        /*更新之前的View*/
        for (i in 0 until hasInstallNum) {
            val absCubeShapeView = shapeViews[i]
            if (absCubeShapeView.isAddMultiPower) {
                multiPowerPathView?.apply {
                    this.alpha = 1.0f
                }
            }
            absCubeShapeView.change2InstalledState()
        }
        /*更新最后一个*/
        shapeViews[hasInstallNum].change2NextInstallState()
        pathViews.getOrNull(hasInstallNum + 1)?.change2InstalledState()
        if (isYShape()) {
            updateUIState()
        }
        /*相对于画布中心的位置*/
        return Pair(shapeViews[hasInstallNum], hasInstallNum)
    }

    /*安装上一个方块 return null 到第一个了*/
    fun installLastShape(): Pair<AbsCubeShapeView, Int>? {
        if (mType != Type.Install) return null

        val curInstallIndex = shapeViews.let { list ->
            var i = 0
            list.forEach { if (it.isInstalledUI) i++ }
//            isCalibrationAll.yes { i++ }
            i
        }

        /*最后一个校验完*/
        if (curInstallIndex <= 0) {
            return null
        }
        /*更新之前的View*/
        for (i in curInstallIndex until shapeViews.size) {
            shapeViews[i].change2UnInstallState()
        }
        /*更新最后一个*/
        shapeViews[curInstallIndex - 1].change2NextInstallState()
        pathViews.getOrNull(curInstallIndex - 1)?.change2UnInstalledState()

        if (isYShape()) {
            updateUIState()
        }
        /*相对于画布中心的位置*/
        return Pair(shapeViews[curInstallIndex - 1], curInstallIndex - 1)
    }

    /*直接安装完所有方块*/
    fun installAllShape(): Boolean {
        if (mType != Type.Install) return false
        /*更新之前的View*/
        for (i in 0 until shapeViews.size) {
            shapeViews[i].change2InstalledState()
            pathViews.getOrNull(i)?.change2InstalledState()

        }
        if (isYShape()) {
            updateUIState()
        }
        return true
    }

    /*校准下一个方块 return null表示校准完*/
    fun checkNextShape(nextPos: Int): AbsCubeShapeView? {
        if (mType != Type.Check) return null

        if (nextPos == 0) {
            /*若重新开始校准，则恢复状态*/
            shapeViews.forEach {
                it.change2UnCheckState(0, shoNum = false, transparentBgCheckedMode = false)
            }
        }
        /*更新之前的View*/
        for (i in 0 until nextPos) {
            shapeViews.getOrNull(i)?.change2CheckedState()
        }
        shapeViews.getOrNull(nextPos)?.change2CheckingState()
        if (nextPos == 0 && shapeViews.size == 2) {
            shapeViews.getOrNull(nextPos + 1)?.apply {
                change2UnCheckState(
                    2,
                    cubeConfig.showNumTagCheckedMode,
                    cubeConfig.transparentBgCheckedMode
                )
            }
        }
        if (nextPos + 1 >= shapeViews.size - 1) return null

        for (i in nextPos + 1 until shapeViews.size) {
            val cubeShapeView = shapeViews.getOrNull(i)
            cubeShapeView?.apply {
                change2UnCheckState(
                    i + 1,
                    cubeConfig.showNumTagCheckedMode,
                    cubeConfig.transparentBgCheckedMode
                )
            }
        }
        /*相对于画布中心的位置*/
        return if (nextPos >= shapeViews.size - 1) null else shapeViews[nextPos]
    }

    /*校准完全部方块*/
    fun checkAllShape(): Boolean {
        if (mType != Type.Check) return true

        /*更新之前的View*/
        for (i in 0 until shapeViews.size) {
            shapeViews.getOrNull(i)?.change2CheckedState()
        }
        return true
    }

    /*全选&取消全选*/
    fun setSelectAll(isSelectAll: Boolean): Boolean {
        if (mType == Type.ColorMode) {
            shapeViews.forEach {
                it.mSelected = isSelectAll
                it.invalidate()
            }
            return true
        }
        return false
    }

    @UiThread
    fun defColorUi() {
        log(TAG, "defColorUi() type = $mType")
        /*关闭动画*/
        cancelAnim()
        /*重置颜色*/
        val size = shapeViews.size
        for (i in 0 until size) {
            val shapeView = shapeViews[i]
            shapeView.resetColorAndBrightness()
            shapeView.invalidate()
        }
    }

    private fun cancelAnim() {
        val iterator = animList.iterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            next.removeAllListeners()
            next.pause()
            next.cancel()
            iterator.remove()
        }
    }

    /*设置颜色参数*/
    fun setColorParams(
        colorList: ArrayList<Int>,
        brightnessList: ArrayList<Int>,
        selectedList: ArrayList<Boolean>
    ) {
        if (mType != Type.ColorMode) return
        /*选中状态*/
        for (i in 0 until selectedList.size) {
            shapeViews.getOrNull(i)?.mSelected = selectedList[i]
        }
        /*颜色,亮度*/
        val len4Color = minOf(colorList.size, shapeViews.size)
        /*先取消之前的动画*/
        cancelAnim()
        for (i in 0 until len4Color) {
            ValueAnimator.ofArgb(shapeViews[i].mColorModeColor, colorList[i]).apply {
                animList.add(this)
                addUpdateListener { valueAnimator -> shapeViews[i].setColorMode4Color(valueAnimator.animatedValue as Int) }
                interpolator = DecelerateInterpolator()
                duration = 300
                start()
            }
        }
        val len4Brightness = minOf(brightnessList.size, shapeViews.size)
        for (i in 0 until len4Brightness) {
            shapeViews[i].setColorMode4Brightness(brightnessList[i])
        }

    }

    /*设置选点位置*/
    fun setSelectPoint(point: PointF?, isIn: Boolean) {
        this.isIn = isIn

        if (mType == Type.Focus && point != null) {
            updateFocusDragItem(PointF(point.x, point.y))
        } else {
            updateDataWithType(Type.Focus, false)
        }
    }

    /*获取已选中图形*/
    fun getSelectedShape(): ArrayList<ShapePosition> {
        val result = arrayListOf<ShapePosition>()
        shapeViews.forEach {
            if (it.mSelected) {
                result.add(it.toShapePosition())
            }
        }
        return result
    }

    /*移动到默认位置*/
    fun move2defaultPosition() {
        if (mDefaultOffsetPoint != null) {
            callBack?.scaleAndTransition(0f, mDefaultOffsetPoint!!)
        }
    }

    /*图形是否在画布上不可见，有图形可见 或没有图形数据或图形未绘制完返回false*/
    fun isShapeInvisible(): Boolean {
        val rect = Rect()
        shapeViews.forEach {
            if (it.getGlobalVisibleRect(rect)) return false
        }
        return (shapeViews.size > 0 && !rect.isEmpty)
    }

    /*图形是否在画布上完全显示*/
    /* Rect.top 的值不为 0 时,View 要么部分可见,要么完全不可见*/
    fun isShapeAllVisible(): Boolean {
        val rect = Rect()
        shapeViews.forEach {
            if (!it.getGlobalVisibleRect(rect)) return false
        }
        return (shapeViews.size > 0 && !rect.isEmpty)
    }

    /*更新拖动布局的缩放比例*/
    fun updateDragViewScale(scale: Float) {
        if (scale <= 0) return
        mFocusDragViewScale = scale
        focusDragView?.scaleX = scale
        focusDragView?.scaleY = scale
        //updateFocusPoint4YShapeScale()
    }

    fun reSetCanvas(keepOneView: Boolean) {
        reSetCanvas(keepOneView, 0f)
    }

    /*重置画布,是否保留一个view*/
    fun reSetCanvas(keepOneView: Boolean, defaultOffsetRotation: Float = 0f) {
        removeAllViews()
        /*清空数据*/
        shapeViews = arrayListOf()
        addViews = arrayListOf()
        pathViews = arrayListOf()
        delView = null
        powerView = null
        focusDragView = null
        canvasRotation = defaultOffsetRotation
        mShapeCenterPoint = null
        shapeTree.reset()
        shapeTree3.reset()

        if (keepOneView) {
            val pointXY = canvasSize / 2f
            val allShapeArea = floatArrayOf(pointXY, pointXY, pointXY, pointXY)
            val areaPoint = addShape(1, true)
            /*左右上下*/
            allShapeArea[0] = min(allShapeArea[0], areaPoint[0])
            allShapeArea[1] = max(allShapeArea[1], areaPoint[1])
            allShapeArea[2] = min(allShapeArea[2], areaPoint[2])
            allShapeArea[3] = max(allShapeArea[3], areaPoint[3])
            /*宽高*/
            val shapeWidth = allShapeArea[1] - allShapeArea[0]
            val shapeHeight = allShapeArea[3] - allShapeArea[2]
            mShapeCenterPoint =
                PointF(
                    (allShapeArea[1] + allShapeArea[0]) / 2,
                    (allShapeArea[3] + allShapeArea[2]) / 2
                )
            /*整体相对于画布的拉伸*/
            val scale = max(shapeWidth, shapeHeight) / canvasSize
//            callBack?.scaleAndTransition(scale, PointF(0f, 0f))
            moveShapeViewCenter(scale)
        }
        addDeleteCallback?.shapeSizeChange(shapeViews.size)
    }

    fun currentScaleShapeArea(): Float {
        val selectedArea = getSelectedArea()
        var minX = 1.0f
        var maxX = 1.0f
        var minY = 1.0f
        var maxY = 1.0f
        for (floats in selectedArea) {
            minX = min(minX, floats[0])
            maxX = max(maxX, floats[1])
            minY = min(minY, floats[2])
            maxY = max(maxY, floats[3])
        }
        // 获取到当前占有的最大区域，根据区域来设置
        val diffX = maxX - minX
        val diffY = maxY - minY
        return max(diffX, diffY) / canvasSize
    }

    //获取图形中心，
    fun getShapeCenterPoint(): PointF? {
        val isInShapeList = arrayOfNulls<Boolean>(shapeViews.size)
        var inShapeCount = 0
        shapeViews.forEachIndexed { index, absCubeShapeView ->
            mShapeCenterPoint?.let {
                absCubeShapeView.isPointInShape(it).let { inShape ->
                    isInShapeList[index] = inShape
                    if (inShape) inShapeCount++
                    log("getShapeCenterPoint", "index = $index ; in = $inShape")
                }
            }
        }

        if (inShapeCount == 1) {
            /*在单个图形内（不包括边界）*/
            val index = isInShapeList.indexOfFirst { it == true }

            //默认中点
            var minDistance = Utils.distance(shapeViews[index].pos, mShapeCenterPoint)
            var minDistancePointF = shapeViews[index].pos
            val offsetX = minDistancePointF?.x ?: 0f
            val offsetY = minDistancePointF?.y ?: 0f

            /*每个顶点*/
            val points: Array<FloatArray> =
                shapeViews[index].getShapePoint(
                    PointF(offsetX, offsetY),
                    OUTSIDE_LINE_LENGTH,
                    shapeViews[index].offsetRotation + canvasRotation
                )

            /*遍历计算距离*/
            points.forEachIndexed { _, floats ->
                val edgePointF = PointF(floats[0], floats[1])
                val dis = Utils.distance(edgePointF, mShapeCenterPoint)
                if (dis < minDistance) {
                    minDistancePointF = edgePointF
                    minDistance = dis
                }
            }
            return minDistancePointF
        }

        return mShapeCenterPoint
    }

    interface OnScaleAndTransitionChange {
        fun scaleAndTransition(scale: Float, point: PointF, animation: Boolean = false)
    }

    /*颜色模式下选择图形回调*/
    interface OnSelectShapeChange {
        fun selectedShape(selectedShapes: ArrayList<Boolean>)
    }

    /*聚焦模式下选择的点*/
    interface OnFocusDragChange {
        fun focusDragPointChange(point: PointF)
        fun selectedFocusPoint(params: ByteArray)
    }

    /*添加删除方块*/
    interface OnAddDeleteChange {
        fun shapeSizeChange(size: Int)
    }

    interface OnChangeLayoutParams {
        fun layoutParams(with: Int, height: Int)
    }
}