package com.govee.cubeview.shape

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.log
import com.govee.ui.R
import kotlin.math.ceil
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2021/11/11
 *     version : 1.0.0
 *     desc    :
 */
abstract class AbsCubeShapeView : View, ICubeShapeView {

    companion object {
        //边长
        var LINE_LENGTH = 40f
        var SHAPE_TYPE = Shape.TYPE_DEFAULT

        var DEF_STROKE_WIDTH = 2f
        const val COLOR_MODE_STROKE_WIDTH = 5

        /**
         * 用于计算相对正多边形其他图形的坐标
         * 如：加号、电源、下一个中心坐标
         */
        val INNER_LINE_LENGTH: Float
            get() {
                return Shape.getShapeInnerRadius(LINE_LENGTH, SHAPE_TYPE, DEF_STROKE_WIDTH)
            }

        /**
         * 用于计算多边形顶点坐标
         */
        val OUTSIDE_LINE_LENGTH: Float
            get() {
                return Shape.getShapeOutsideRadius(LINE_LENGTH, SHAPE_TYPE)
            }
    }

    //形状路径
    private var lineRect: Rect? = null
    var path: Path? = null

    val shapePath = Path()

    /*安装完*/
    var color4installed = Color.WHITE
    var color4installedStroke = Color.WHITE

    /*安装中*/
    var color4installing = Color.WHITE
    var color4installingStroke = Color.WHITE

    /*校准完*/
    private var color4checked = Color.WHITE
    private var color4checkedStroke = Color.WHITE

    /*校准中*/
    var color4checking = Color.GREEN

    /*未校准*/
    var color4unchecked = Color.WHITE
    var color4uncheckedStroke = Color.WHITE

    /* 辅助电源 */
    var color4unselected = Color.WHITE
    var color4canSelect = Color.WHITE
    var color4selecting = Color.WHITE

    /*默认*/
    var color4default = Color.WHITE
    var color4defaultStroke = Color.WHITE

    /*获取世界坐标*/
    //中心点相对于父布局位置
    var pos: PointF? = null

    /* 辅助电源坐标 */
    var powerPointF: PointF? = null

    val mMultiPower by lazy { MultiPower(-1, -1, mutableListOf()) }

    //画布旋转角度
    var canvasRotation = 0f

    /*自己的旋转偏移量*/
    var offsetRotation = 0f

    /*自己的tag偏移量，默认电源入口的tag值 -- first 偏移角度， sec 对应的tag值*/
    var offsetTag: Pair<Float, Int>? = null

    /*默认偏移量*/
    var addImgRotationOffset = 30

    var nextDirectionTag: Int = -1//下一个的边的编号

    var offsetNextDirectionTag = HashMap<Int, Int>()

    var lastPosition: Int = 0
    var lastShapeView: AbsCubeShapeView? = null

    var state: Int = 0 // 连接边的状态位
        get() {
            var temp = 0
            val values = offsetNextDirectionTag.values
            // 1 表示右边
            // 2 表示左边
            values.forEach {
                if (it == 1) {
                    temp += 2
                } else if (it == 2) {
                    temp += 1
                }
            }
            return temp
        }

    protected var mColor: Int = color4default

    protected var strokeColor: Int = color4defaultStroke
    protected var STROKE_WIDTH = 2


    var numText = ""

    protected var mTextColor = Color.BLACK

    protected open fun getTextColor(): Int {
        return mTextColor
    }

    protected var mBrightness = 0//亮度值

    var mColorModeColor: Int = color4default

    var mSelected = false//是否选中

    var isNeedPowerView = false// 是否需要可以增加辅助电源

    var isAddMultiPower = false // 是否增加辅助电源

    var isNeedDrawColorMode = false// 是否需要画颜色

    var isCheckingUI = false//是否在校准中
    var isCheckedUI = false// 是否完成校准

    var isInstalledUI = false//是否是安装完
    var isInstallingUI = false//是否在安装中
    var isPreViewUI = false // 是否是预览
    var preRotate = 0f

    val preViewColors by lazy {
        IntArray(5).apply {
            this[0] = Color.RED
            this[1] = Color.BLUE
            this[2] = Color.YELLOW
            this[3] = Color.YELLOW
            this[4] = Color.YELLOW
        }
    }

    var mColorProjection: Int = R.color.green
    var mColorBottomEdge: Int = R.color.FFFFFFFF
    var mColorSheetMetal: Int = Color.parseColor("#D0E1E4")
    var mColorGuideLight: Int = R.color.FFFFFFFF

    var cubeConfig: CubeConfig

    /*获取边框画笔*/
    protected val strokePaint: Paint
        get() {
            val choosePaint = Paint(Paint.ANTI_ALIAS_FLAG)
            choosePaint.color = strokeColor
            choosePaint.style = Paint.Style.STROKE
            choosePaint.strokeWidth = STROKE_WIDTH.toFloat()
            return choosePaint
        }

    /*获取边框画笔*/
    protected val colorModeStrokePaint: Paint
        get() {
            val choosePaint = Paint(Paint.ANTI_ALIAS_FLAG)
            val near = isNearLowBlueColor(mColorModeColor)
            val colorRes =
                if (near) com.govee.ui.R.color.ui_color_block_style_16_3_stroke_near_blue else com.govee.ui.R.color.ui_color_block_style_16_3_stroke
            choosePaint.color = getColor(colorRes)
            choosePaint.style = Paint.Style.STROKE
            choosePaint.strokeWidth = COLOR_MODE_STROKE_WIDTH.toFloat()
            return choosePaint
        }

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    init {
        cubeConfig = generateConfig()
    }

    fun getColor(resId: Int): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            context.resources.getColor(resId, null)
        } else {
            context.resources.getColor(resId)
        }
    }

    override fun init() {
        pos = PointF()
        powerPointF = PointF()
        path = Path()
        lineRect = Rect()

        mTextColor = getColor(com.govee.ui.R.color.font_style_185_1_textColor)

        color4installed = getColor(com.govee.ui.R.color.ui_color_block_style_16_5_color)
        color4installedStroke = getColor(com.govee.ui.R.color.ui_color_block_style_16_5_stroke)

        color4installing = getColor(com.govee.ui.R.color.ui_color_block_style_16_4_color)
        color4installingStroke = getColor(com.govee.ui.R.color.ui_color_block_style_16_4_stroke)

        /*校准完固定白色*/
        color4checkedStroke = getColor(com.govee.ui.R.color.ui_color_block_style_16_1_stroke)

        color4unchecked = getColor(com.govee.ui.R.color.ui_color_block_style_16_2_color)
        color4uncheckedStroke = getColor(com.govee.ui.R.color.ui_color_block_style_16_2_stroke)

        color4default = getColor(com.govee.ui.R.color.ui_color_block_style_16_1_color)
        color4defaultStroke = getColor(com.govee.ui.R.color.ui_color_block_style_16_1_stroke)

        mColor = color4default
        strokeColor = color4defaultStroke
    }

    /*子类创建配置类*/
    abstract fun generateConfig(): CubeConfig

    /**
     *  根据父布局提供的中心坐标，设置 LayoutParams 参数
     *  @param centerPointX
     *  @param centerPointY
     */
    fun setLayoutParams(centerPointX: Int, centerPointY: Int, with: Float) {
        val lp = FrameLayout.LayoutParams((with * 2).roundToInt(), (with * 2).roundToInt())
        val roundToInt = (centerPointX - with).roundToInt()
        val roundToInt1 = (centerPointY - with).roundToInt()
        //showLog("", "setShapeLayoutParams: roundToInt = $roundToInt roundToInt1 = $roundToInt1")
        lp.leftMargin = roundToInt
        lp.topMargin = roundToInt1
        layoutParams = lp
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let {
            drawShape(it)
            drawNumberText(it)
            drawColorMode(it)
            drawShapeBg(it)
            drawPreView(it)
        }
    }

    protected open fun drawPreView(it: Canvas) {
        if (!isPreViewUI) return

        colorModeStrokePaint.apply {
            color = strokeColor
            strokeWidth = 4f
        }.run {
            drawSelectedStroke(it, this)
        }
    }

    private fun drawShapeBg(it: Canvas) {
        if (!drawShapeBg) return
        it.save()
        it.clipPath(shapePath)
        it.drawBitmap(decodeResource!!, 0f, 0f, paint)
        it.restore()
    }

    var drawShapeBg = false
    var decodeResource: Bitmap? = null
    private val paint = Paint()

    fun setBackgroundRes(decodeResource: Bitmap) {
        this.decodeResource = decodeResource
        drawShapeBg = true
        invalidate()
    }

    protected open fun drawShape(canvas: Canvas) {
        val shapePoint = getShapePoint()

        path?.let {
            it.reset()
            shapePath.reset()
            shapePoint.forEachIndexed { index, floats ->
//                showLog("", "old drawShape:  = ${PointF(floats[0], floats[1])} ")
                if (index == 0) {
                    it.moveTo(floats[0], floats[1])
                    shapePath.moveTo(floats[0], floats[1])
                } else {
                    it.lineTo(floats[0], floats[1])
                    shapePath.lineTo(floats[0], floats[1])
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = if (isNeedDrawColorMode) mColorModeColor else mColor
            paint.strokeWidth = STROKE_WIDTH.toFloat()

            /*画路径*/
            it.transform(matrix)
            canvas.drawPath(it, paint)
            canvas.drawPath(it, strokePaint)

            it.reset()
        }


        drawShapeModifier(
            canvas,
            PointF((width / 2).toFloat(), (height / 2).toFloat()),
            OUTSIDE_LINE_LENGTH,
            offsetRotation
        )

    }

    /*绘制图形修饰属性*/
    protected open fun drawShapeModifier(
        canvas: Canvas,
        pointF: PointF,
        length: Float,
        rotation: Float
    ) {

    }


    private fun drawColorMode(canvas: Canvas) {
        if (!isNeedDrawColorMode) return

        /*画文字底色*/
        drawNumTextBackground(canvas)

        /*选中画框*/
        if (mSelected) {
            drawSelectedStroke(canvas)
        }

        val textPaint = Paint()
        /*颜色反转*/
        val red = Color.red(mColorModeColor)
        val green = Color.green(mColorModeColor)
        val blue = Color.blue(mColorModeColor)

        if (cubeConfig.handleNumColorMode) {
            if (red * 0.299 + green * 0.578 + blue * 0.114 >= 192) {
                //浅色
                textPaint.color = Color.BLACK
            } else {
                //深色
                textPaint.color = Color.WHITE
            }
        } else {
            val curLightMode = Utils.curLightMode(context)
            if (curLightMode) {
                textPaint.color = Color.BLACK
            } else {
                textPaint.color = Color.WHITE
            }
        }
        /*大小*/
        textPaint.textSize = 16f
        textPaint.style = Paint.Style.FILL
        //该方法即为设置基线上那个点究竟是left,center,还是right  这里我设置为center
        textPaint.textAlign = Paint.Align.CENTER
        drawTextOnCenter(canvas, textPaint, "${mBrightness}%")
    }

    /*画选中线*/
    protected open fun drawSelectedStroke(canvas: Canvas, paint: Paint = colorModeStrokePaint) {
        val len = OUTSIDE_LINE_LENGTH - STROKE_WIDTH / 2 - COLOR_MODE_STROKE_WIDTH / 2
        val shapePoint = getShapePoint(len)

        path?.let {
            it.reset()
            shapePoint.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats[0], floats[1])
                } else {
                    it.lineTo(floats[0], floats[1])
                }
            }
            it.close()

            /*画路径*/
            it.transform(matrix)
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    private fun drawNumberText(it: Canvas) {
        drawNumText(it)
    }

    /*画编号*/
    private fun drawNumText(canvas: Canvas) {
        if (numText.isNotEmpty()) {
            drawNumTextBackground(canvas)
            val textPaint = Paint()
            textPaint.color = getTextColor()
            textPaint.textSize = 20.8f
            textPaint.style = Paint.Style.FILL
            //该方法即为设置基线上那个点究竟是left,center,还是right  这里我设置为center
            textPaint.textAlign = Paint.Align.CENTER

            drawTextOnCenter(canvas, textPaint, numText)
        }
    }

    protected open fun drawNumTextBackground(canvas: Canvas) {

    }

    private fun drawTextOnCenter(canvas: Canvas, textPaint: Paint, text: String) {
        val fontMetrics = textPaint.fontMetrics
        val top = fontMetrics.top//为基线到字体上边框的距离,即上图中的top
        val bottom = fontMetrics.bottom//为基线到字体下边框的距离,即上图中的bottom

        val baseLineY = (height / 2).toFloat() - top / 2 - bottom / 2//基线中间点的y轴计算公式

        canvas.drawText(text, (width / 2).toFloat(), baseLineY, textPaint)
    }

    /**
     * 是否接近浅蓝色
     *
     * @param rgb
     * @return
     */
    private fun isNearLowBlueColor(rgb: Int): Boolean {
        val hsv = FloatArray(3)
        Color.colorToHSV(rgb, hsv)
        val h = hsv[0].toInt()
        val s = (hsv[1] * 100).toInt()
        val v = (hsv[2] * 100).toInt()
        Log.e("TAG", "isNearLowBlueColor: h = $h ; s = $s ; v = $v")
        val nearH = 195 - 5 <= h && 195 + 5 >= h
        val nearS = 95 - 2 <= s && s <= 100
        val nearV = 82 - 2 <= v && v <= 100
        Log.e("TAG", "isNearLowBlueColor: nearH = $nearH ; nearS = $nearS ; nearV = $nearV")
        return nearH && nearS && nearV
    }

    /*获取六边形的点*/
    fun getShapePoint(len: Float = OUTSIDE_LINE_LENGTH): Array<FloatArray> {
        return getShapePoint(
            PointF((width / 2).toFloat(), (height / 2).toFloat()),
            len,
            offsetRotation + canvasRotation
        )
    }

    fun getShapePoint4Canvas(forSelf: Boolean = true): Array<FloatArray> {
        return getShapePoint(pos!!, OUTSIDE_LINE_LENGTH, offsetRotation + canvasRotation, forSelf)
    }

    fun isPointInShape(point: PointF): Boolean {
        val list = mutableListOf<PointF>()

        val path = Path()
        path.reset()
        getShapePoint(
            PointF(pos?.x ?: 0f, pos?.y ?: 0f),
            OUTSIDE_LINE_LENGTH,
            offsetRotation + canvasRotation
        ).forEachIndexed { index, floats ->
            list.add(PointF(floats[0], floats[1]))

            if (index == 0) {
                path.moveTo(floats[0], floats[1])
            } else {
                path.lineTo(floats[0], floats[1])
            }
            //log(this::class.java.name, "isPointInShape() TopPoint=${floats[0]},${floats[1]} ")
        }
        path.close()
        //log(this::class.java.name, "isPointInShape() curPoint=${point.x},${point.y} ")

        return Utils.isPointInPolygon(PointF(point.x, point.y), list)
//        return Utils.PtInPolygonV2(Point((point.x - (pos?.x?:0f)).toInt() ,(point.y - (pos?.y?:0f)).toInt()), path)
    }

    /*设置世界坐标*/
    fun setPos(x: Float, y: Float) {
        pos!![x] = y
    }

    fun setPowerPointF(x: Float, y: Float) {
        powerPointF!![x] = y
    }

    fun getShapeLineLength(): Float {
        return LINE_LENGTH
    }

    fun getShapeType(): Int {
        return SHAPE_TYPE
    }

    /*画编号*/
    fun setNumberText(num: String) {
        if (numText != num) {
            this.numText = num
            invalidate()
        }
    }

    /*安装完状态*/
    fun change2InstalledState() {
        this.alpha = 1f
        /*填充颜色*/
        this.mColor = color4installed
        this.strokeColor = color4installedStroke
        isCheckingUI = false
        isCheckedUI = false
        isInstalledUI = true
        isInstallingUI = false
        invalidate()
    }

    /*正在安装中状态*/
    fun change2NextInstallState() {
        this.alpha = 1f
        this.mColor = color4installing
        this.strokeColor = color4installingStroke
        isCheckingUI = false
        isCheckedUI = false
        isInstalledUI = false
        isInstallingUI = true
        invalidate()
    }

    /*未安装状态*/
    fun change2UnInstallState() {
        this.mColor = color4default
        this.strokeColor = color4defaultStroke
        isCheckingUI = false
        isCheckedUI = false
        isInstalledUI = false
        isInstallingUI = false
        invalidate()
        alpha = 0.4f
    }

    /*校准中状态*/
    fun change2CheckingState() {
        if (cubeConfig.showCustomCheckingUI) {
            isCheckingUI = true
            this.mColor = color4checked
        } else {
            isCheckingUI = false
            this.mColor = color4checking
        }
        this.strokeColor = color4defaultStroke
        this.isCheckedUI = false
        numText = ""
        this.alpha = 1f
        invalidate()
    }

    /*校准完成状态*/
    fun change2CheckedState() {
        this.mColor = color4checked
        this.strokeColor = color4checkedStroke
        numText = ""
        this.isCheckedUI = true
        isCheckingUI = false
        this.alpha = 1f
        invalidate()
    }

    fun change2UnCheckState(position: Int, shoNum: Boolean, transparentBgCheckedMode: Boolean) {
        this.mColor = color4unchecked
        this.strokeColor = color4uncheckedStroke
        numText = if (shoNum) {
            "$position"
        } else {
            ""
        }
        isCheckingUI = false
        isCheckedUI = false
        isInstalledUI = false
        isInstallingUI = false
        invalidate()
        this.alpha = if (transparentBgCheckedMode) {
            0.4f
        } else {
            1f
        }
    }

    /*设置颜色模式，亮度*/
    fun setColorMode4Brightness(brightness: Int) {
        isNeedDrawColorMode = true
        isClickable = true
        strokeColor = color4defaultStroke
        mBrightness = brightness
        invalidate()
    }

    /*设置颜色模式， 颜色*/
    fun setColorMode4Color(color: Int) {
        isNeedDrawColorMode = true
        isClickable = true
        mColorModeColor = color
        mColorProjection = color
        mColorGuideLight = color
        strokeColor = color4defaultStroke
        invalidate()
    }

    fun setPreViewMode(color: Int) {
        log("tag -- ", "setPreViewMode setPreView -- ")
        //isNeedDrawColorMode = true
        isPreViewUI = true
        isClickable = false
        strokeColor = color
        invalidate()
    }

    /* 设置多个电源接入接口 */
    fun setMultiplePowerColor(enable: Boolean, color: Int, strokeColor: Int = color4defaultStroke) {
        isClickable = enable
        isNeedDrawColorMode = false
        mColor = color
        this.strokeColor = strokeColor

        this.alpha = if (enable) 1.0f else 0.4f
        invalidate()
    }

    fun setPreView(preRotate: Float, preViewColors: IntArray?) {
        isPreViewUI = true
        this.preRotate = preRotate
        log("TAG--", "setPreView() preViewColors0 = ${preViewColors?.get(1)}")
        log("TAG--", "setPreView() preViewColors1 = ${this.preViewColors[1]}")
        preViewColors?.copyInto(this.preViewColors)
        log("TAG--", "setPreView() preViewColors2 = ${this.preViewColors[1]}")
        isClickable = false
        strokeColor = this.preViewColors[3]
        invalidate()
    }

    private var tempArea = FloatArray(4)
    val shapeArea4Parent: FloatArray
        get() {
            val x = pos!!.x
            val y = pos!!.y
            tempArea[0] = x - OUTSIDE_LINE_LENGTH / 2
            tempArea[1] = x + OUTSIDE_LINE_LENGTH / 2
            tempArea[2] = y - OUTSIDE_LINE_LENGTH / 2
            tempArea[3] = y + OUTSIDE_LINE_LENGTH / 2
            return tempArea
        }

    fun isPointInShapeArea(point: PointF): Boolean {
        return point.x >= shapeArea4Parent[0] && point.x <= shapeArea4Parent[1] && point.y >= shapeArea4Parent[2] && point.y <= shapeArea4Parent[3]
    }

    /**
     * 获取传输协议时的旋转角度。
     * <p>
     *  初始角度默认：
     *  六边形的是 60 度
     *  三边形的是 0 度
     * </p>
     * */
    override fun getRotationValue(): Int {
        val defRotation = Constants.getShapeDefaultAngle(SHAPE_TYPE)
        var angle = ((offsetRotation + canvasRotation).toInt() + defRotation) % 360
        angle = Utils.rangeRotation360(angle)
        return angle
    }

    fun toShapePosition(): ShapePosition {
        return ShapePosition(
            SHAPE_TYPE,
            ceil(pos!!.x),
            ceil(pos!!.y),
            getRotationValue(),
            lastPosition, state
        )
    }

    /**
     * 重置状态
     */
    fun reset() {
        path!!.reset()
        mColorModeColor = color4default
        canvasRotation = 0f
        /*自己的旋转偏移量*/
        offsetRotation = 0f
        nextDirectionTag = -1//下一个的边的编号
        offsetTag = null
    }

    fun resetColorAndBrightness() {
        mColorModeColor = color4default
        mBrightness = 0
    }


}