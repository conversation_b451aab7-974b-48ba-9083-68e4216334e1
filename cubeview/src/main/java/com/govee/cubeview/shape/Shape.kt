package com.govee.cubeview.shape

import android.content.Context
import android.graphics.PointF
import android.os.Build
import android.text.TextUtils
import androidx.annotation.RequiresApi
import com.govee.cubeview.Utils
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.DataProcessCenter
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.configuration.GameWallConfig
import com.govee.cubeview.reconfigure.proxy.CanvasLayoutProxyV1
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.data.CubeConnectStatusData
import com.govee.cubeview.reconfigure.shape.data.CubeConnectTemp
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.showLog
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sin
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2021/11/16
 *     version : 1.0.0
 *     desc    :
 */
object Shape {

    /**
     *  正六边形
     * <p>
     *     蓝牙协议中定义类型为 0
     * </p>
     */
    const val TYPE_HEXAGON = 0

    /**
     * 三角形
     * <p>
     *     蓝牙协议中定义类型为 3
     * </p>
     */
    const val TYPE_TRIANGLE = 3

    /**
     * 立体 六边形
     * <p>
     *     蓝牙协议中定义类型为 4
     * </p>
     */
    const val TYPE_SOLID_HEXAGON = 4

    /**
     * 空间 六边形
     * <p>
     *     蓝牙协议中定义类型为 5
     * </p>
     */
    const val TYPE_SPACE_HEXAGON = 6

    /**
     * 游戏上墙灯条 - h6063
     * <p>
     *     蓝牙协议中根据形状灯的定义
     *     长条灯 0x10
     *     三角转接件 0x11
     *     正方转接件 0x12
     * </p>
     */
    const val TYPE_GAME_RECTANGLE = 0x10
    const val TYPE_GAME_TRIANGLE = 0x11
    const val TYPE_GAME_SQUARE = 0x12

    /**
     * Y 形灯
     */
    const val TYPE_Y = 0x0F

    /**
     * 正方形
     * <p>
     *     蓝牙协议中定义类型为 0x20
     * </p>
     */
    const val TYPE_SQUARE = 0x20

    const val TYPE_DEFAULT = TYPE_HEXAGON

    /**
     * 六边形的边长
     */
    const val LINE_LENGTH_4_SQURA = 40

    const val op_type_splice = 0
    const val op_type_calibration = 1
    const val op_type_adjust = 2

    private const val diffValue = 3

    private var supportShape = arrayOf(TYPE_HEXAGON)

    var useSoftLineNum: MutableList<Int> = mutableListOf<Int>().apply {
        add(5)
        add(18)
        add(38)
    }

    /**
     * 实际设备的 ic 数
     */
    fun getTypeIc(type: Int): Int {
        return when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 6
            TYPE_SOLID_HEXAGON -> 6
            TYPE_Y -> 6
            else -> 6
        }
    }

    /**
     * 获取正多边形内切圆半径
     * @param length 边长
     * @param type 多边形类型
     * <p>
     * 内切圆半径计算公式,
     * x = 半径
     * n = 边数
     * 公式 ：x*cos(π/n)
     *</p>
     */
    fun getShapeInnerRadius(length: Float, type: Int, stroke: Float = 0f): Float {
        val n = when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 3
            TYPE_SOLID_HEXAGON -> 6
            TYPE_SPACE_HEXAGON -> 6
            TYPE_Y -> 3
            TYPE_GAME_TRIANGLE -> 3
            TYPE_GAME_RECTANGLE,
            TYPE_GAME_SQUARE ->
                4

            TYPE_SQUARE -> 4

            else -> 6
        }
        if (type == TYPE_GAME_RECTANGLE) {
            return GameWallConfig.GameRectangleViewHeight
        }
        if (type == TYPE_HEXAGON || type == TYPE_SOLID_HEXAGON || type == TYPE_SPACE_HEXAGON) {
            return ((length - stroke / 2f) * sqrt(3f) + stroke / 2f) / 2f
        }
        val r = (length / 2) * cos(PI / n) / sin(PI / n)
        return r.toFloat()
    }

    /**
     *  获取正多边形外接圆半径
     *  <p>
     *      公式： r = a/(2*sin(π/n))
     *      r：半径
     *      a：边长
     *      n：边数
     *  </p>
     */
    fun getShapeOutsideRadius(length: Float, type: Int): Float {
        val n = when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 3
            TYPE_SOLID_HEXAGON -> 6
            TYPE_SPACE_HEXAGON -> 6
            TYPE_Y -> 3
            TYPE_GAME_TRIANGLE -> 3
            TYPE_GAME_RECTANGLE,
            TYPE_GAME_SQUARE ->
                4

            TYPE_SQUARE ->
                4

            else -> 6
        }
        val result = length * 1 / (2 * sin(PI / n))
        return result.toFloat()
    }

    fun filterSupportType(list: MutableList<Int>): List<Int> {
        val supportList = listOf(*supportShape)
        val iterator = list.iterator()
        if (iterator.hasNext()) {
            val next = iterator.next()
            if (!supportList.contains(next)) {
                iterator.remove()
            }
        }
        return list
    }

    //Bh5ABkAGAAAeGQb8BQAAHvIFuAUAAB5ABrgFAAAejwa4BQAAHmgG/AUAAAEBAQEBBCAEIBACEAIBCAA=
    fun getShapes(shapes: String?): ArrayList<ShapePosition> {
        val shapePositions = ArrayList<ShapePosition>()
        if (shapes == null || TextUtils.isEmpty(shapes)) return shapePositions
        val bytes: ByteArray = Utils.decryByBase64(shapes) ?: return shapePositions
        val size = bytes[0].toInt()
        if (bytes.size != 1 + 7 * size // 没有辅助电源
            && bytes.size != 1 + 7 * size + 2 // 带辅助电源
            && bytes.size != 1 + 7 * size + 2 + size // Y形灯
            && bytes.size != 1 + 7 * size + 4 + size * 2 // 空间灯
        ) {
            return shapePositions
        }
        var pos = 1
        for (i in 0 until size) {
            val type = bytes[pos++].toInt()
            val x: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])
            val y: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])
            val angle: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])

            shapePositions.add(ShapePosition(type, x.toFloat(), y.toFloat(), angle, 0, 0))
        }

        if (bytes.size == (1 + 7 * size + 2)) {
            // 扩展内容
            val powerNumber = bytes[pos++].toInt()
            val powerEdgeNumber = bytes[pos].toInt()
            shapePositions[powerNumber - 1].ext =
                MultiPower(powerNumber, powerEdgeNumber, mutableListOf())
        }
        if (bytes.size == 1 + 7 * size + 2 + size) {
            pos += 2
            shapePositions.forEach {
                it.state = bytes[pos++].toInt()
            }
        }
        if (bytes.size == 1 + 7 * size + 4 + size * 2) {
            val mainPowerNumber = bytes[pos++].toInt()
            val mainPowerEdgeNumber = bytes[pos++].toInt()
            if (mainPowerNumber in 1 until size && mainPowerEdgeNumber in 0..5) {
                shapePositions[mainPowerNumber - 1].mainPower =
                    MultiPower(mainPowerNumber, mainPowerEdgeNumber, mutableListOf())
            }

            val powerNumber = bytes[pos++].toInt()
            val powerEdgeNumber = bytes[pos++].toInt()

            if (powerNumber in 1 until size && powerEdgeNumber in 0..5) {
                shapePositions[powerNumber - 1].ext =
                    MultiPower(powerNumber, powerEdgeNumber, mutableListOf())
            }

            shapePositions.forEach {
                it.inputNum = bytes[pos++].toInt()
                it.outputNum = bytes[pos++].toInt()
            }
        }
        return shapePositions
    }

    /**
     * 校验是否编辑过
     * */
    fun checkHadEdit(
        shapes1: ArrayList<ShapePosition>, shapes2: ArrayList<ShapePosition>
    ): Boolean {
        if (shapes1.size != shapes2.size) return true
        for (i in shapes1.indices) {
            val curShape = shapes1[i]
            val checkShape = shapes2[i]
            if (curShape.type != checkShape.type || curShape.angle != checkShape.angle) {
                return true
            }
            val diffX = abs(curShape.x - checkShape.x)
            val diffY = abs(curShape.y - checkShape.y)

            if (diffX > diffValue || diffY > diffValue) {
                return true
            }
        }
        return false
    }

    fun isOpSplice(opType: Int): Boolean {
        return opType == op_type_splice
    }

    fun isOpCalibration(opType: Int): Boolean {
        return opType == op_type_calibration
    }

    fun isOpAdjust(opType: Int): Boolean {
        return opType == op_type_adjust
    }

    /**
     * 该方法用于图形倒推
     */
    fun getShapesByStream(
        firstShapeView: BaseShapeView,
        firstAngle: Int, connectStateList: MutableList<ByteArray>
    ): ArrayList<ShapePosition> {
        if (connectStateList.isEmpty()) return arrayListOf()
        // 第一块是接电源的位置,通过接电源的位置，递归形式
        val shapes = arrayListOf<ShapePosition>()
        val allShapePairs = mutableListOf<Triple<PointF, Int, Int>>()// 中心点和旋转角度
        val tempShapePairs = mutableListOf<Triple<PointF, Int, Int>>()// 中心点和旋转角度

        connectStateList.onEachIndexed { indexState, bytes ->// 数量
            val generaDefaultShape =
                CanvasLayoutProxyV1.generaDefaultShape(TYPE_SPACE_HEXAGON)// 默认(1600,1600)
            val input = bytes[0]
            val output = bytes[1]

            generaDefaultShape.inputNum = input.toInt()
            generaDefaultShape.outputNum = output.toInt()

            val inputResult = Utils.parseBytes4Bit(input, true)// false
            inputResult.onEachIndexed { index, b ->
                if (b) {
                    if (indexState == 0) { // 电源
                        // 加上第一块的旋转角度
                        generaDefaultShape.angle = firstAngle
                        //因为从设备读取到的信息，默认第一块是电源位置，所以需要根据此信息倒推生成图形
                        generaDefaultShape.mainPower =
                            MultiPower(1, input.toInt(), mutableListOf())
                        generaDefaultShape.ext = generaDefaultShape.mainPower
                    } else {
                        //
                        allShapePairs.firstOrNull()?.let {// 压栈
                            val first = it.first// 中心点坐标
                            val second = it.second// 上一个图形的旋转角度
                            val third = it.third //  上一个图形的输出边

                            generaDefaultShape.x = (first.x)
                            generaDefaultShape.y = (first.y)

                            val subAngle = ((third + 3) % 6 - index) * 60 //  上一块与当前块的入边差值为3，所以就为了此算法。

                            generaDefaultShape.angle = (second + subAngle.checkAngle()).checkAngle()

                        }
                        allShapePairs.removeFirstOrNull()
                    }
                }
            }
            firstShapeView.initShapeData(ShapeData(generaDefaultShape, modeType = CanvasLayoutModeType.DefaultType, 0))// 生成当前

            val outEdges = Utils.parseBytes4Bit(output, true)// true  0010 0010  false ture
            tempShapePairs.clear()
            outEdges.onEachIndexed { index, b ->
                if (b) {
                    // 有出边，然后根据当前的边类型
                    val nextShapePosition = firstShapeView.getNextShapePosition(index) //
                    showLog("outEdges1 index = $index x = ${nextShapePosition.x} y = ${nextShapePosition.y}")
                    tempShapePairs.add(Triple(PointF(nextShapePosition.x, nextShapePosition.y), generaDefaultShape.angle, index))
                }
            }
            allShapePairs.addAll(0, tempShapePairs)
            shapes.add(generaDefaultShape.apply {
                this.x = ceil(this.x)
                this.y = ceil(this.y)
            })

        }
        return shapes
    }

    /**
     * 该方法用于图形倒推
     *  h6063
     */
    fun getShapesByStream4H6063(
        context: Context,
        panelLampType: PanelLampType,
        firstAngle: Int,
        connectStateList: MutableList<CubeConnectStatusData>
    ): ArrayList<ShapePosition> {
        if (connectStateList.isEmpty()) return arrayListOf()
        // 第一块是接电源的位置,通过接电源的位置，递归形式
        val shapes = arrayListOf<ShapePosition>()
        val allShapePairs = mutableListOf<CubeConnectTemp>()// 中心点和旋转角度
        val tempShapePairs = mutableListOf<CubeConnectTemp>()// 中心点和旋转角度
        val tempMap = HashMap<Long, BaseShapeView?>()
        var tag = 0L

        connectStateList.onEachIndexed { indexState, status ->// 数量
            val input = status.inputStatus
            val output = status.outputStatus
            val type = status.type
            val generaDefaultShape =
                CanvasLayoutProxyV1.generaDefaultShape(type)// 默认(1600,1600)

            generaDefaultShape.inputNum = input.toInt()
            generaDefaultShape.outputNum = output.toInt()

            val inputResult = Utils.parseBytes4Bit(input, true)
            inputResult.onEachIndexed { index, b ->
                if (b) {// 选中
                    if (indexState == 0) { // 第一块的输入边是电源
                        // 加上第一块的旋转角度
                        generaDefaultShape.angle = firstAngle
                        //因为从设备读取到的信息，默认第一块是电源位置，所以需要根据此信息倒推生成图形
                        generaDefaultShape.mainPower =
                            MultiPower(1, input.toInt() - 1, mutableListOf())
                        generaDefaultShape.ext = generaDefaultShape.mainPower
                    } else {
                        allShapePairs.firstOrNull()?.let { // 压栈
                            val first = it.centerOffset// 中心点坐标
                            val nextAngleReal = it.parseNextAngle(type, index).checkAngle().toInt()// 下一个图形的旋转角度

                            generaDefaultShape.angle = nextAngleReal
                            generaDefaultShape.x = (first.x)
                            generaDefaultShape.y = (first.y)
                            generaDefaultShape.lastPosition = it.lastNote

                            val lastView = tempMap[it.tag]
                            if (lastView != null) {
                                // 这里有点问题，看看如何计算旋转，要拿到下一个图形的半径才行。
                                // 角度是从上一个图形出边计算出来的

                                val innerLineLength = lastView.params.innerLineLength // 当前块的内切圆长度，
                                val shapeType = lastView.params.shapeType

                                val radius = it.getRadius(type)// 下一个的半径
                                val nextLineLength = innerLineLength + radius
                                // 使用上一个图形的内切圆半径+下一个图形的内切圆半径计算出，两个图形的距离，再通过上一个图形中心点旋转逻辑，计算下一个图形的中心点。
                                val nextShapePosition =
                                    lastView.getNextShapeCenterPoints(it.getNextAngleReal(nextAngleReal), nextLineLength)
                                //showLog("outEdges1 innerLineLength111 = $innerLineLength ,shapeType = $shapeType , radius = $radius x = ${nextShapePosition.x} y = ${nextShapePosition.y}")

                                generaDefaultShape.x = (nextShapePosition.x)
                                generaDefaultShape.y = (nextShapePosition.y)
                            } else {
                                showLog("上一个没找到")
                            }
                        }
                        allShapePairs.removeFirstOrNull()
                    }
                }
            }
            // 根据入边创建图形
            val createShapeView = panelLampType.config.createShapeView(context, type)
            createShapeView.initShapeData(ShapeData(generaDefaultShape, modeType = CanvasLayoutModeType.DefaultType, 0))// 生成当前

            val outEdges = Utils.parseBytes4Bit(output, true)// true  0010 0010  false ture
            tempShapePairs.clear()
            outEdges.onEachIndexed { index, b ->
                if (b) {
                    // 有出边，然后根据当前的边类型
                    val offsetRotation = createShapeView.offsetRotation
                    tag++
                    val cubeConnectTemp = CubeConnectTemp(type, PointF(1f, 1f), offsetRotation, index, indexState, tag)
                    tempMap[cubeConnectTemp.tag] = createShapeView
                    tempShapePairs.add(cubeConnectTemp)
                }
            }
            allShapePairs.addAll(0, tempShapePairs)
            shapes.add(generaDefaultShape.apply {
                this.x = ceil(this.x)
                this.y = ceil(this.y)
            })
        }
        allShapePairs.clear()
        tempShapePairs.clear()
        tempMap.clear()
        return shapes
    }

    fun checkH6063Points(shapes: ArrayList<ShapePosition>): ArrayList<ShapePosition> {
        if (shapes.isEmpty()) return shapes
        val defCenterX = 1600f
        val defCenterY = 1600f

        val offsetCenterX: Float
        val offsetCenterY: Float

        var tempMinX = 1600f
        var tempMaxX = 1600f
        var tempMixY = 1600f
        var tempMaxY = 1600f
        shapes.forEach {
            tempMinX = min(it.x, tempMinX)
            tempMaxX = max(it.x, tempMaxX)
            tempMixY = min(it.y, tempMixY)
            tempMaxY = max(it.y, tempMaxY)
        }

        offsetCenterX = (tempMaxX + tempMinX) / 2
        offsetCenterY = (tempMaxY + tempMixY) / 2

        val difX = defCenterX - offsetCenterX
        val difY = defCenterY - offsetCenterY

        val newShapeList = ArrayList<ShapePosition>()
        shapes.forEach {
            val copy = it.copy()
            copy.inputNum = it.inputNum
            copy.outputNum = it.outputNum
            copy.mainPower = it.mainPower
            copy.ext = it.ext
            copy.editable = it.editable

            copy.x = it.x + difX
            copy.y = it.y + difY
            newShapeList.add(copy)
        }
        return shapes
    }

    /**
     * 该方法用于图形倒推
     *  h6069
     */
    fun getShapesByStream4H6069(
        context: Context,
        panelLampType: PanelLampType,
        firstAngle: Int,
        connectStateList: MutableList<CubeConnectStatusData>,
        supportH6069IterateV1: Boolean,
        supportSecondSize: Int = 70,
    ): Pair<ArrayList<ShapePosition>, ArrayList<Int>> {
        if (connectStateList.isEmpty()) return Pair(arrayListOf(), arrayListOf())
        // 第一块是接电源的位置,通过接电源的位置，递归形式
        val shapes = arrayListOf<ShapePosition>()
        val allShapePairs = mutableListOf<CubeConnectTemp>()// 中心点和旋转角度
        val tempShapePairs = mutableListOf<CubeConnectTemp>()// 中心点和旋转角度
        val tempMap = HashMap<Long, BaseShapeView?>()
        var tag = 0L
        val sortHashMap = HashMap<BaseShapeView, BaseShapeView>()

        connectStateList.onEachIndexed { indexState, status ->// 数量
            val input = status.inputStatus
            val output = status.outputStatus
            val type = status.type
            val generaDefaultShape =
                CanvasLayoutProxyV1.generaDefaultShape(type)// 默认(1600,1600)
            val isPowerShape = type == 0x7F

            generaDefaultShape.inputNum = input.toInt()
            generaDefaultShape.outputNum = output.toInt()

            // 根据入边创建图形
            val createShapeView = panelLampType.config.createShapeView(context, type)
            createShapeView.note.position = indexState

            val inputResult = Utils.parseBytes4Bit(input, true)
            inputResult.onEachIndexed { index, b ->
                if (b) {// 选中
                    if (indexState == 0) { // 第一块的输入边是电源
                        // 加上第一块的旋转角度
                        generaDefaultShape.angle = firstAngle
                        //因为从设备读取到的信息，默认第一块是电源位置，所以需要根据此信息倒推生成图形
                        generaDefaultShape.mainPower =
                            MultiPower(1, input.toInt() - 1, mutableListOf())
                        generaDefaultShape.ext = generaDefaultShape.mainPower
                    } else {
                        allShapePairs.firstOrNull()?.let { // 压栈
                            val first = it.centerOffset// 中心点坐标
                            val nextAngleReal = it.parseNextAngle(type, index).checkAngle().toInt()// 下一个图形的旋转角度

                            generaDefaultShape.angle = nextAngleReal
                            generaDefaultShape.x = (first.x)
                            generaDefaultShape.y = (first.y)
                            generaDefaultShape.lastPosition = it.lastNote

                            val lastView = tempMap[it.tag]
                            if (lastView != null) {
                                // 这里有点问题，看看如何计算旋转，要拿到下一个图形的半径才行。
                                // 角度是从上一个图形出边计算出来的
                                sortHashMap[createShapeView] = lastView

                                if (isPowerShape) {
                                    val parent4Power = shapes[it.lastNote]
                                    val outputNum = parent4Power.outputNum// 连接关系
                                    val inputNum = parent4Power.inputNum// 输出边关系
                                    val outputNum4Bit = Utils.parseBytes4Bit(outputNum.toByte(), true)
                                    val inputNum4Bit = Utils.parseBytes4Bit(inputNum.toByte(), true)
                                    val outputEdge = it.outputEdge// 边序号,从0开始
                                    outputNum4Bit[outputEdge] = false
                                    inputNum4Bit[outputEdge] = true
                                    // 相互改变之后，转成 int 在重新赋值

                                    parent4Power.inputNum = Utils.makeSelectByOneBit(inputNum4Bit).toInt()
                                    parent4Power.outputNum = Utils.makeSelectByOneBit(outputNum4Bit).toInt()
                                    parent4Power.ext = MultiPower(it.lastNote + 1, outputEdge, mutableListOf())
                                    // 重新赋值上一个参数
                                    showLog("辅助电源位置，重新赋值上一个参数，旧 outputNum = $outputNum inputNum = $inputNum , 新 outputNum = ${parent4Power.outputNum} inputNum = ${parent4Power.inputNum} , 出边 outputEdge = $outputEdge")
                                }

                                val innerLineLength = lastView.params.innerLineLength // 当前块的内切圆长度，
                                val shapeType = lastView.params.shapeType

                                val radius = it.getRadius(type)// 下一个的半径
                                val nextLineLength = innerLineLength + radius
                                // 使用上一个图形的内切圆半径+下一个图形的内切圆半径计算出，两个图形的距离，再通过上一个图形中心点旋转逻辑，计算下一个图形的中心点。
                                val nextShapePosition =
                                    lastView.getNextShapeCenterPoints(it.getNextAngleReal(nextAngleReal), nextLineLength)
                                //showLog("outEdges1 innerLineLength111 = $innerLineLength ,shapeType = $shapeType , radius = $radius x = ${nextShapePosition.x} y = ${nextShapePosition.y}")

                                generaDefaultShape.x = (nextShapePosition.x)
                                generaDefaultShape.y = (nextShapePosition.y)
                            } else {
                                showLog("上一个没找到")
                            }
                        }
                        allShapePairs.removeFirstOrNull()
                    }
                }
            }
            createShapeView.initShapeData(ShapeData(generaDefaultShape, modeType = CanvasLayoutModeType.DefaultType, 0))// 生成当前

            val outEdges = Utils.parseBytes4Bit(output, true)// true  0010 0010  false ture
            tempShapePairs.clear()
            outEdges.onEachIndexed { index, b ->
                if (b) {
                    // 有出边，然后根据当前的边类型
                    val offsetRotation = createShapeView.offsetRotation
                    tag++
                    val cubeConnectTemp = CubeConnectTemp(type, PointF(1f, 1f), offsetRotation, index, indexState, tag)
                    tempMap[cubeConnectTemp.tag] = createShapeView
                    tempShapePairs.add(cubeConnectTemp)
                }
            }
            allShapePairs.addAll(0, tempShapePairs)

            shapes.add(generaDefaultShape.apply {
                this.x = (this.x)
                this.y = (this.y)
            })
        }

        var location = arrayListOf<Int>()
        if (supportH6069IterateV1 && shapes.size > supportSecondSize) {// 大于70块开始计算第二电源位置
            location = DataProcessCenter.calculateSupply2Location(sortHashMap, shapes)
            showLog("calculateSupply2Location() ,计算得出合理位置 location = $location")
        }
        val contentList = mutableListOf<MutableList<Int>>()
        // 按 position 排序，保证递归入口顺序稳定
        sortHashMap.keys.sortedBy { it.note.position }.forEach {
            sortHashMap[it]?.let { value ->
                showLog("最终结果打印, index = ${it.note.position}, 父类 index = $value.note.position")
                contentList.add(mutableListOf<Int>().apply {
                    add(value.note.position)
                    add(it.note.position)
                })
            }
        }

        allShapePairs.clear()
        tempShapePairs.clear()
        tempMap.clear()
        sortHashMap.clear()
        return Pair(shapes, location)
    }

    /**
     * 该方法用于图形倒推
     *  h6069
     *  新增：返回每个 shape 的区域编号集合，顺序与 shapes 保持一致
     *  @param useSoftLineNum 当前块和上一块的位置标识，index 从0开始
     */
    @RequiresApi(Build.VERSION_CODES.N)
    fun getShapesByStreamSoftLine4H6069(
        context: Context,
        panelLampType: PanelLampType,
        firstAngle: Int,
        connectStateList: MutableList<CubeConnectStatusData>,
        supportH6069IterateV1: Boolean,
        supportSecondSize: Int = 70,
        useSoftLineNum: MutableList<Int>,
    ): Triple<ArrayList<ShapePosition>, ArrayList<Int>, List<Int>> {
        if (connectStateList.isEmpty()) return Triple(arrayListOf(), arrayListOf(), listOf())
        val shapes = arrayListOf<ShapePosition>()
        val allShapePairs = mutableListOf<CubeConnectTemp>()
        val tempShapePairs = mutableListOf<CubeConnectTemp>()
        val tempMap = HashMap<Long, BaseShapeView?>()
        var tag = 0L
        val sortHashMap = HashMap<BaseShapeView, BaseShapeView>()

        connectStateList.onEachIndexed { indexState, status ->// 数量
            val input = status.inputStatus
            val output = status.outputStatus
            val type = status.type
            val generaDefaultShape =
                CanvasLayoutProxyV1.generaDefaultShape(type)// 默认(1600,1600)
            val isPowerShape = type == 0x7F

            generaDefaultShape.inputNum = input.toInt()
            generaDefaultShape.outputNum = output.toInt()

            // 根据入边创建图形
            val createShapeView = panelLampType.config.createShapeView(context, type)
            createShapeView.note.position = indexState

            val inputResult = Utils.parseBytes4Bit(input, true)
            inputResult.onEachIndexed { index, b ->
                if (b) {// 选中
                    if (indexState == 0) { // 第一块的输入边是电源
                        // 加上第一块的旋转角度
                        generaDefaultShape.angle = firstAngle
                        //因为从设备读取到的信息，默认第一块是电源位置，所以需要根据此信息倒推生成图形
                        generaDefaultShape.mainPower =
                            MultiPower(1, input.toInt() - 1, mutableListOf())
                        generaDefaultShape.ext = generaDefaultShape.mainPower
                    } else {
                        allShapePairs.firstOrNull()?.let { // 压栈
                            val first = it.centerOffset// 中心点坐标
                            val nextAngleReal =
                                it.parseNextAngle(type, index).checkAngle().toInt()// 下一个图形的旋转角度

                            generaDefaultShape.angle = nextAngleReal
                            generaDefaultShape.x = (first.x)
                            generaDefaultShape.y = (first.y)
                            generaDefaultShape.lastPosition = it.lastNote

                            val lastView = tempMap[it.tag]
                            if (lastView != null) {
                                // 这里有点问题，看看如何计算旋转，要拿到下一个图形的半径才行。
                                // 角度是从上一个图形出边计算出来的
                                sortHashMap[createShapeView] = lastView

                                if (isPowerShape) {
                                    val parent4Power = shapes[it.lastNote]
                                    val outputNum = parent4Power.outputNum// 连接关系
                                    val inputNum = parent4Power.inputNum// 输出边关系
                                    val outputNum4Bit =
                                        Utils.parseBytes4Bit(outputNum.toByte(), true)
                                    val inputNum4Bit = Utils.parseBytes4Bit(inputNum.toByte(), true)
                                    val outputEdge = it.outputEdge// 边序号,从0开始
                                    outputNum4Bit[outputEdge] = false
                                    inputNum4Bit[outputEdge] = true
                                    // 相互改变之后，转成 int 在重新赋值

                                    parent4Power.inputNum =
                                        Utils.makeSelectByOneBit(inputNum4Bit).toInt()
                                    parent4Power.outputNum =
                                        Utils.makeSelectByOneBit(outputNum4Bit).toInt()
                                    parent4Power.ext =
                                        MultiPower(it.lastNote + 1, outputEdge, mutableListOf())
                                    // 重新赋值上一个参数
                                    showLog("辅助电源位置，重新赋值上一个参数，旧 outputNum = $outputNum inputNum = $inputNum , 新 outputNum = ${parent4Power.outputNum} inputNum = ${parent4Power.inputNum} , 出边 outputEdge = $outputEdge")
                                }
                                val innerLineLength = lastView.params.innerLineLength // 当前块的内切圆长度，
                                val radius = it.getRadius(type)// 下一个的半径

                                var nextLineLength = innerLineLength + radius

                                if (useSoftLineNum.contains(indexState)) {
                                    nextLineLength += nextLineLength
                                }
                                // 使用上一个图形的内切圆半径+下一个图形的内切圆半径计算出，两个图形的距离，再通过上一个图形中心点旋转逻辑，计算下一个图形的中心点。
                                val nextShapePosition =
                                    lastView.getNextShapeCenterPoints(it.getNextAngleReal(nextAngleReal), nextLineLength)
                                //showLog("outEdges1 innerLineLength111 = $innerLineLength ,shapeType = $shapeType , radius = $radius x = ${nextShapePosition.x} y = ${nextShapePosition.y}")

                                generaDefaultShape.x = (nextShapePosition.x)
                                generaDefaultShape.y = (nextShapePosition.y)
                            } else {
                                showLog("上一个没找到")
                            }
                        }
                        allShapePairs.removeFirstOrNull()
                    }
                }
            }
            createShapeView.initShapeData(ShapeData(generaDefaultShape, modeType = CanvasLayoutModeType.DefaultType, 0))// 生成当前

            val outEdges = Utils.parseBytes4Bit(output, true)// true  0010 0010  false ture
            tempShapePairs.clear()
            outEdges.onEachIndexed { index, b ->
                if (b) {
                    // 有出边，然后根据当前的边类型
                    val offsetRotation = createShapeView.offsetRotation
                    tag++
                    val cubeConnectTemp =
                        CubeConnectTemp(type, PointF(1f, 1f), offsetRotation, index, indexState, tag)
                    tempMap[cubeConnectTemp.tag] = createShapeView
                    tempShapePairs.add(cubeConnectTemp)
                }
            }
            allShapePairs.addAll(0, tempShapePairs)

            shapes.add(generaDefaultShape.apply {
                this.x = (this.x)
                this.y = (this.y)
            })
        }

        var location = arrayListOf<Int>()
        if (supportH6069IterateV1 && shapes.size > supportSecondSize) {
            location = DataProcessCenter.calculateSupply2Location(sortHashMap, shapes)
            showLog("calculateSupply2Location() ,计算得出合理位置 location = $location")
        }

        val regionIds = DataProcessCenter.calculateSoftLocation4Area(shapes, sortHashMap)
        showLog("根据软线加载，得出分布 regionIds = $regionIds")

        val contentList = mutableListOf<MutableList<Int>>()
        // 按 position 排序，保证递归入口顺序稳定
        sortHashMap.keys.sortedBy { it.note.position }.forEach {
            sortHashMap[it]?.let { value ->
                showLog("最终结果打印, index = ${it.note.position}, 父类 index = ${value.note.position}")
                contentList.add(mutableListOf<Int>().apply {
                    add(value.note.position)
                    add(it.note.position)
                })
            }
        }

        allShapePairs.clear()
        tempShapePairs.clear()
        tempMap.clear()
        sortHashMap.clear()
        return Triple(shapes, location, regionIds)
    }

}