package com.govee.cubeview.shape

import android.util.Log
import com.govee.cubeview.BuildConfig
import java.util.*


/**
 *     author  : sinrow
 *     time    : 2022/5/6
 *     version : 1.0.0
 *     desc    :
 */
class ShapeTree {

    companion object {
        private const val TAG = "ShapeTree"
        private const val showLog = false

        private fun log(tag: String, content: String) {
            if (showLog && BuildConfig.DEBUG) {
                Log.d(tag, content)
            }
        }
    }

    var root: Node? = null

    class Node(val id: Int, var isSub: Boolean) {
        var leftNode: Node? = null
        var rightNode: Node? = null
        var parentNode: Node? = null

        fun deleteNode(id: Int) {
            //如果左子树不为空且是要查找的节点，就删除
            if (leftNode != null && leftNode?.id == id) {
                leftNode = null
                log(TAG, "deleteNode() , 删除成功 leftNode")
                if (rightNode == null) {
                    isSub = true
                }
                return
            }
            //如果右子树不为空且是要查找的节点，就删除
            if (rightNode != null && rightNode?.id == id) {
                rightNode = null
                log(TAG, "deleteNode() , 删除成功 rightNode")
                if (leftNode == null) {
                    isSub = true
                }
                return
            }

            //左递归，继续查找
            if (leftNode != null) {
                leftNode?.deleteNode(id)
            }

            //右递归，继续查找
            if (rightNode != null) {
                rightNode?.deleteNode(id)
            }
        }
    }

    //插入节点
    fun insert(index: Int, nextId: Int, isLeft: Boolean, shapePosition: ShapePosition): Boolean {
        // index 表示上一个的下标，
        log(TAG, "insert = $nextId, isLeft = $isLeft")
        val newNodeParent = Node(nextId, false)
        val newNodeLeft = Node(nextId, true)
        val newNodeRight = Node(nextId, true)
        newNodeParent.leftNode = newNodeLeft
        newNodeParent.rightNode = newNodeRight

        if (root == null) { //以后树为空树，没有任何节点
            root = newNodeParent
            newNodeParent.parentNode = null //  跟节点没有父类
            return true
        } else {
            val stack = Stack<Node>()
            stack.push(root)// 装进栈内

            while (stack.isNotEmpty()) {
                val pop = stack.pop()//取第一个
                val leftNode = pop.leftNode
                val rightNode = pop.rightNode

                if (pop.id == index) {
                    // 访问当前节点
                    // 判断 tag 是左边还是右边，直接赋值
                    return if (isLeft) {
                        if (leftNode == null) {
                            pop.leftNode = newNodeParent
                        } else {
                            leftNode.leftNode = newNodeParent
                        }
                        pop.isSub = false
                        true
                    } else {
                        if (rightNode == null) {
                            pop.rightNode = newNodeParent
                        } else {
                            pop.rightNode?.rightNode = newNodeParent
                        }
                        pop.isSub = false
                        true
                    }
                } else {
                    // 没有找到，按照 tag 方向，寻找左边还是右边，找到之后，走上面的流程
                    if (leftNode?.leftNode != null) {
                        stack.push(leftNode.leftNode)
                    }
                    if (rightNode?.rightNode != null) {
                        stack.push(rightNode.rightNode)
                    }
                }
            }
        }

        return false
    }

    fun deleteNode(id: Int) {
        log(TAG, "deleteNode() , id = $id")
        if (root?.id == id) {
            root = null
            log(TAG, "deleteNode() , 根节点被删除")
            return
        }

        root?.deleteNode(id)
    }

    // 遍历
    fun ergodic(shapeIndex: Int): MutableList<Int> {// index 查找
        // 目的，计算出每一块图形的边的 ic 序号，电源入口左边起 ，开始：0 4，8

        // 构建了下标为 id 的图形的二叉树，
        // 从 root 的左边遍历，然后开始计算右边的。
        // id 可以定位到在哪个节点中，这时，就可以遍历得知

        val mapShapeIndex = mapShapeIndex()
        return mapShapeIndex[shapeIndex] ?: mutableListOf()
    }


    private fun mapShapeIndex(): HashMap<Int, MutableList<Int>> {

        if (root == null) return HashMap<Int, MutableList<Int>>()
        val list = mutableListOf<Int>()
        val stack = Stack<Node>()

        val visible = HashSet<Node>()
        stack.push(root)
        while (stack.isNotEmpty()) {
            val pop = stack.pop()
            val leftNode = pop.leftNode
            val rightNode = pop.rightNode
            if (!pop.isSub) {
                // 电源节点，左右必然不可能为空
                // 出现为空的情况只有，在两个灯板连接处的虚拟节点，该节点不作为排序
                // 记录电源节点的 id，记录当前电源节点已访问
                list.add(pop.id)
                visible.add(pop)
                log(TAG, "ergodic -- 电源节点 ${pop.id}")

                if (rightNode != null) {
                    stack.push(rightNode)
                }

                // 将子节点放入栈中
                if (leftNode != null) {
                    stack.push(leftNode)
                }
            } else {
                // 不会存在左右两边都有值的情况，因为永远都是单边的
                if (visible.contains(pop)) {
                    list.add(pop.id)
                    log(TAG, "ergodic -- 重新压栈 ${pop.id}")
                } else if ((pop.leftNode == null && pop.rightNode != null)) {
                    //访问此节点
                    log(TAG, "ergodic --- 左边为空，右边不为空")
                    stack.push(pop)// 重新压栈，通过是否访问来往外抛出
                    visible.add(pop)
                    stack.push(pop.rightNode)
                } else if (pop.rightNode == null && pop.leftNode != null) {
                    // 左边为空，右边不为空
                    log(TAG, "ergodic --- 左边为不空，右边为空")
                    stack.push(pop)
                    visible.add(pop)
                    stack.push(pop.leftNode)
                } else {
                    list.add(pop.id)
                    visible.add(pop)
                    log(
                        TAG,
                        "ergodic --- 两者都为空  id = ${pop.id}, left = ${pop.leftNode} , right = ${pop.rightNode}"
                    )
                }
            }
        }

        val map = HashMap<Int, MutableList<Int>>()
        var icPosition = 0
        list.forEach {
            map[it]?.apply {
                add(icPosition)
            } ?: run {
                map.put(it, mutableListOf<Int>().apply {
                    add(icPosition)
                })
            }
            icPosition += 4
        }
        return map
    }

    fun reset() {
        root = null
    }


}