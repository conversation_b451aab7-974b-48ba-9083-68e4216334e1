package com.govee.cubeview.shape

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import com.govee.ui.R
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2021/11/11
 *     version : 1.0.0
 *     desc    :
 */
class TriangleView(context: Context) : AbsCubeShapeView(context) {

    init {
        LINE_LENGTH = 90f
        SHAPE_TYPE = Shape.TYPE_TRIANGLE
        addImgRotationOffset = 180
        STROKE_WIDTH = 2

        color4canSelect = getColor(R.color.ui_color_block_style_16_1_color)
        color4unselected = getColor(R.color.ui_color_block_style_16_1_color)
        color4selecting = getColor(R.color.ui_color_block_style_16_6_color)
    }


    override fun getShapeRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        val matrix = Matrix()
        matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
        val fa =
            floatArrayOf((length * cos(2 * PI * 2 / 3) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 3) + pointF.y).toFloat())
        /*旋转*/
        matrix.mapPoints(fa)
        return PointF(fa[0], fa[1])
    }

    /**
     * 根据方向计算偏移角度
     */
    override fun getRotationByDirectionTag(directionTag: Int): Float {
        // 奇数 60 ，偶数 = 300
        return if (directionTag % 2 == 0) {
            300f
        }
        else {
            60f
        }
    }

    override fun generateConfig(): CubeConfig {
        return CubeConfig()
    }

    override fun getShapePoint(pointF: PointF,
            length: Float,
            rotation: Float,
            forSelf: Boolean): Array<FloatArray> {
        val matrix = Matrix()//旋转
        matrix.setRotate(rotation + 30f, pointF.x, pointF.y)

        val shapePoint = Array(3) { FloatArray(2) }

        for (i in shapePoint.indices) {
            shapePoint[i] =
                floatArrayOf(pointF.x + (length * cos(2 * PI * i / 3)).toFloat(),
                        pointF.y + (length * sin(2 * PI * i / 3)).toFloat())
            matrix.mapPoints(shapePoint[i])
        }
        return shapePoint
    }

}