package com.govee.cubeview.shape;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * Create by lins<PERSON><PERSON> on 4/22/21
 */
@Keep
public class ShapePosition implements Parcelable {
    public int type;
    public float x;
    public float y;
    public int angle;
    public MultiPower ext;
    public int lastPosition;
    public int state;
    public boolean editable = true;


    /**
     * 606A 参数定义，定义面板的输出和输出端，
     * 采用递归形式来确认当前图形,采用 bit 8位表示当前输入端和输出端
     * <p>
     * inputNum ：输入序号
     * outputNum: 输出序号
     * </p>
     */
    public int inputNum = 0;
    public int outputNum = 0;
    public MultiPower mainPower;//主电源


    public ShapePosition(int type, float x, float y, int angle) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.angle = angle;
    }

    public ShapePosition(int type, float x, float y, int angle, int lastPosition, int state) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.angle = angle;
        this.state = state;
        this.lastPosition = lastPosition;
    }


    protected ShapePosition(Parcel in) {
        type = in.readInt();
        x = in.readFloat();
        y = in.readFloat();
        angle = in.readInt();
        ext = in.readParcelable(MultiPower.class.getClassLoader());
        mainPower = in.readParcelable(MultiPower.class.getClassLoader());
        lastPosition = in.readInt();
        state = in.readInt();
        editable = in.readByte() != 0;
        inputNum = in.readInt();
        outputNum = in.readInt();
    }

    public boolean isExt() {
        return ext != null && ext.getPowerEdgeNUmber() > 0 && ext.getPowerEdgeNUmber() > 0;
    }

    public boolean isMainPower() {
        return mainPower != null && mainPower.getPowerNumber() > 0 && mainPower.getPowerEdgeNUmber() >= 0;
    }

    public ShapePosition copy() {
        return new ShapePosition(type, x, y, angle, lastPosition, state);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(type);
        dest.writeFloat(x);
        dest.writeFloat(y);
        dest.writeInt(angle);
        dest.writeParcelable(ext, flags);
        dest.writeParcelable(mainPower, flags);
        dest.writeInt(lastPosition);
        dest.writeInt(state);
        dest.writeByte((byte) (editable ? 1 : 0));
        dest.writeInt(inputNum);
        dest.writeInt(outputNum);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ShapePosition> CREATOR = new Creator<ShapePosition>() {
        @Override
        public ShapePosition createFromParcel(Parcel in) {
            return new ShapePosition(in);
        }

        @Override
        public ShapePosition[] newArray(int size) {
            return new ShapePosition[size];
        }
    };

    @NonNull
    @Override
    public String toString() {
        return "\nShapePosition{" +
                "type=" + type +
                ", x=" + x +
                ", y=" + y +
                ", angle=" + angle +
                ", lastPosition=" + lastPosition +
                ", state=" + state +
                ", ext=" + ext +
                ", mainPower=" + mainPower +
                ", editable=" + editable +
                ", inputNum=" + inputNum +
                ", outputNum=" + outputNum +
                "}\n";
    }
}