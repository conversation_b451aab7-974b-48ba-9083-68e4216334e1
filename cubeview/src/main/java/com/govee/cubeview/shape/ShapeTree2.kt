package com.govee.cubeview.shape

import java.util.*

/**
 *     author  : sinrow
 *     time    : 2022/5/25
 *     version : 1.0.0
 *     desc    :
 */
class ShapeTree2 {

    var root: Node? = null

    class Node(val id: Int, var isSub: Bo<PERSON>an, val shapePosition: ShapePosition) {
        var leftNode: Node? = null
        var rightNode: Node? = null
    }

    fun reset() {
        root = null
    }

    fun insert(id: Int, nowIndex: Int, isLeft: <PERSON><PERSON><PERSON>, shapePosition: ShapePosition): Bo<PERSON>an {
        val newNode = Node(nowIndex, false, shapePosition)

        if (root == null) {
            root = newNode
            return true
        }
        else {
            val stack = Stack<Node>()
            stack.push(root)
            while (stack.isNotEmpty()) {
                val pop = stack.pop()
                if (id == pop.id) {
                    // 相等
                    if (isLeft) {
                        pop.isSub = true
                        pop.leftNode = newNode
                    }
                    else {
                        pop.isSub = true
                        pop.rightNode = newNode
                    }
                    return true
                }
                else {
                    if (pop.leftNode != null) {
                        stack.push(pop.leftNode)
                    }
                    if (pop.rightNode != null) {
                        stack.push(pop.rightNode)
                    }
                }
            }
        }
        return false
    }


    private val resultList = arrayListOf<ShapePosition>()


    fun ergodicMid(): ArrayList<ShapePosition> {

        resultList.clear()
        if (root == null) resultList
        val stack = Stack<Node>()

        stack.push(root)

        while (stack.isNotEmpty()) {
            val pop = stack.pop()

            resultList.add(pop.shapePosition)

            if (pop.rightNode != null) {
                pop.rightNode?.shapePosition?.lastPosition = resultList.size - 1
                stack.push(pop.rightNode)
            }

            if (pop.leftNode != null) {
                pop.leftNode?.shapePosition?.lastPosition = resultList.size - 1
                stack.push(pop.leftNode)
            }
        }
//        resultList.forEach {
//            log("newShape", " it = $it")
//        }
        return resultList
    }

}