package com.govee.cubeview.shape

/**
 *Create by elon on 2021/12/28
 *$图形配置类，定义各种拓展属性，传入absCubeShapeView的构造方法 代替抽象方法
 */
class CubeConfig {

    /*是否在安装模式中显示序号*/
    var showNumTagInInstallMode = false

    /*是否在校准模式中显示序号*/
    var showNumTagCheckedMode = true
    var showCustomCheckingUI = false

    /*是否需要处理颜色模式下字体的颜色显示*/
    var handleNumColorMode = true

    /*校准模式下，是否需要透明未校准的图形：不透明 40% */
    var transparentBgCheckedMode = false


    init {
        showNumTagCheckedMode = true
        showNumTagInInstallMode = false
        showCustomCheckingUI = false
        handleNumColorMode = true
        transparentBgCheckedMode = false
    }

}