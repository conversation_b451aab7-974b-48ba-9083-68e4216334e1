package com.govee.cubeview;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.Size;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatDelegate;

/**
 * Create by lvwenrui on 2021/7/20 14:42
 */
public class Utils {

    @Nullable
    public static byte[] decryByBase64(String encrypted) {
        try {
            return Base64.decode(encrypted, Base64.NO_WRAP);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static int convertTwoBytesToShort(byte low, byte high) {
        return (short) ((high << 8) | (low & 0xFF));
    }

    /**
     * @param p1
     * @param p2
     * @return 垂直向上开始，顺时针旋转的角度，0-360
     */
    public static int calAngle(PointF p1, PointF p2) {
        double x = p2.x - p1.x;
        double y = p2.y - p1.y;
        double z = Math.sqrt(x * x + y * y);
        int angle = Math.round(((float) (Math.acos(y / z) / Math.PI * 180)));
        if (p2.x < p1.x) {
            angle = 360 - angle;
        }
        angle = angle % 360;
        angle = angle <= 180 ? 180 - angle : 360 - (angle - 180);
        return angle;
    }


    public static float getPixelByDp(Context context, int dpValue, int destiny) {
        WindowManager mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        mWindowManager.getDefaultDisplay().getMetrics(metrics);
        int width = metrics.widthPixels;//获取到的是px，像素，绝对像素

        return (width * dpValue) / (destiny * 1f);
    }

    /**
     * 根据给定的宽和高进行拉伸
     *
     * @param origin    原图
     * @param newWidth  新图的宽
     * @param newHeight 新图的高
     * @return new Bitmap
     */
    public static Bitmap scaleBitmap(Bitmap origin, int newWidth, int newHeight) {
        if (origin == null) {
            return null;
        }
        int height = origin.getHeight();
        int width = origin.getWidth();
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);// 使用后乘
        return Bitmap.createBitmap(origin, 0, 0, width, height, matrix, false);
    }

    /**
     * 根据给定的宽和高进行拉伸
     *
     * @param origin    原图
     * @param newWidth  新图的宽
     * @param newHeight 新图的高
     * @return new Bitmap
     */
    public static Bitmap scaleBitmap(Bitmap origin, boolean recycler, int newWidth, int newHeight, float scaleX, float scaleY) {
        if (origin == null) {
            return null;
        }
        int height = origin.getHeight();
        int width = origin.getWidth();
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);// 使用后乘
        matrix.postScale(scaleX, scaleY);
        Bitmap newBM = Bitmap.createBitmap(origin, 0, 0, width, height, matrix, true);
        if (recycler && !origin.isRecycled()) {
            origin.recycle();
        }
        return newBM;
    }

    /**
     * 限定旋转角度【0-360）
     *
     * @param rotation 角度
     * @return 限定范围角度
     */
    public static int rangeRotation360(int rotation) {
        int result = rotation;
        while (result < 0) result += 360;
        while (result >= 360) result -= 360;
        return result;
    }

    /**
     * 限定旋转角度【0-360）
     *
     * @param rotation 角度
     * @return 限定范围角度
     */
    public static float rangeRotation360(float rotation) {
        float result = rotation;
        while (result < 0) result += 360;
        while (result >= 360) result -= 360;
        return result;
    }

    /**
     * 校正下一个点位置
     *
     * @param p1            第一个点
     * @param p1            第二个点
     * @param angleInternal 最小角度间隔
     * @param distance      两点距离
     * @return PointF 校正后的第二个点
     */
    @Nullable
    public static PointF checkNextPoint(PointF p1, PointF p2, float angleInternal, float distance) {
        if (p1 == null || p2 == null) return null;
        int angle = Math.round(calAngle(p1, p2) / angleInternal) * Math.round(angleInternal);
        angle = angle <= 180 ? 180 - angle : 360 - (angle - 180);
        float x = (float) (Math.sin(angle * Math.PI / 180) * distance + p1.x);
        float y = (float) (Math.cos(angle * Math.PI / 180) * distance + p1.y);
        return new PointF(x, y);
    }

    /**
     * 限定数字范围【0-n）
     *
     * @param number 要限制的数字
     * @param n      限制范围
     * @return
     */
    public static float rangeFloat(float number, float n) {
        float result = number;
        while (result < 0) result += n;
        while (result >= n) result -= n;
        return result;
    }

    /**
     * 限定数字范围【0-n）
     *
     * @param number 要限制的数字
     * @param n      限制范围
     * @return
     */
    public static int rangeInt(int number, int n) {
        int result = number;
        while (result < 0) result += n;
        while (result >= n) result -= n;
        return result;
    }

    /**
     * 利用光投射算法计算点是否在多边形内
     *
     * @param point    需要判断的点的坐标
     * @param vertices 多边形按顺时针或逆时针顺序的顶点坐标集合
     * @return 点是否在多边形内
     */
    public static boolean isPointInPolygon(PointF point, List<PointF> vertices) {
        boolean contains = false;
        for (int i = 0, j = vertices.size() - 1; i < vertices.size(); j = i++) {
            if (((vertices.get(i).y >= point.y) != (vertices.get(j).y >= point.y)) &&
                    (point.x <= (vertices.get(j).x - vertices.get(i).x) * (point.y - vertices.get(i).y) / (vertices.get(j).y - vertices.get(i).y) + vertices.get(i).x))
                contains = !contains;
        }
        return contains;
    }

    /**
     * 两点间距【0-n）
     *
     * @return
     */
    public static float distance(PointF p1, PointF p2) {
        return (float) Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
    }

    public static int dp2px(Context context, float dp) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    public static PointF centerPoint(PointF first, PointF second) {
        PointF pointF = new PointF();
        pointF.x = (first.x + second.x) / 2f;
        pointF.y = (first.y + second.y) / 2f;
        return pointF;
    }

    public static String getStringFormat(Context context, @StringRes int stringResId, Object... formatArgs) {
        return context.getString(stringResId, formatArgs);
    }


    public static int getDimensionPixelSize(Context context, @DimenRes int dimenResId) {
        return context.getResources().getDimensionPixelSize(dimenResId);
    }

    /**
     * 是否是light主题
     *
     * @return
     */
    public static boolean curLightMode(Context context) {
        int nightMode = AppCompatDelegate.getDefaultNightMode();
        if (nightMode == AppCompatDelegate.MODE_NIGHT_NO) {
            /*当前选中lightMode*/
            return true;
        }
        if (nightMode == AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM) {
            /*跟随系统*/
            Configuration configuration = context.getResources().getConfiguration();
            if (configuration != null) {
                int curUiMode = configuration.uiMode & Configuration.UI_MODE_NIGHT_MASK;
                return curUiMode == Configuration.UI_MODE_NIGHT_NO;
            }
        }
        return false;
    }

    /**
     * 为了防止byte转int新增3个字节补符号位导致原码错误问题
     */
    public static int byte2Int(byte b) {
        return b & 0xFF;
    }


    /**
     * 生成选中bit
     *
     * @param checkPre
     * @param byteReverse byte描述是否从低位开始
     * @return
     */
    public static byte[] makeSelectedBytes(@NonNull boolean[] checkPre, boolean byteReverse) {
        int size = checkPre.length / 8 + (checkPre.length % 8 == 0 ? 0 : 1);
        byte[] result = new byte[size];
        boolean[] check = new boolean[checkPre.length];
        System.arraycopy(checkPre, 0, check, 0, checkPre.length);
        int startPos = 0;
        int remainSize = check.length;
        for (int i = 0; i < size; i++) {
            boolean[] check1 = new boolean[8];
            System.arraycopy(check, startPos, check1, 0, Math.min(check1.length, remainSize));
            StringBuilder sb1 = new StringBuilder();
            for (int j = 0; j < 8; j++) {
                if (check1[j]) {
                    sb1.append("1");
                } else {
                    sb1.append("0");
                }
            }
            if (byteReverse) {
                sb1 = sb1.reverse();
            }
            result[i] = (byte) Integer.parseInt(sb1.toString(), 2);
            startPos += 8;
            remainSize -= 8;
        }
        return result;
    }

    public static int getSignedInt(byte[] value, boolean hFirst) {
        int result = 0;
        byte loop;
        int length = value.length;
        if (hFirst) {
            for (int i = 0; i < length; i++) {
                loop = value[length - 1 - i];
                result += (loop & 0xFF) << (8 * i);
            }
        } else {
            for (int i = 0; i < length; i++) {
                loop = value[i];
                result += (loop & 0xFF) << (8 * i);
            }
        }
        return result;
    }

    public static byte[] getSignedBytesFor2(int value, boolean hFirst) {
        byte[] src = new byte[2];
        if (hFirst) {
            src[0] = (byte) ((value >> 8) & 0xFF);
            src[1] = (byte) (value & 0xFF);
        } else {
            src[0] = (byte) (value & 0xFF);
            src[1] = (byte) ((value >> 8) & 0xFF);
        }
        return src;
    }

    @Size(8)
    public static boolean[] parseBytes4Bit(byte b, boolean byteReverse) {
        boolean[] value = new boolean[8];
        String bStr = Integer.toBinaryString(b);
        if (bStr.length() < 8) {
            /*不足8位，前置位补0*/
            bStr = "00000000" + bStr;
        }
        StringBuilder sb = new StringBuilder(bStr.substring(bStr.length() - 8));
        if (byteReverse) {
            sb.reverse();
        }
        String s = sb.toString();
        int beginIndex = 0;
        for (int i = 0; i < 8; i++) {
            String subStr = s.substring(beginIndex, beginIndex + 1);
            value[i] = "1".equals(subStr);
            beginIndex++;
        }
        return value;
    }

    public static byte makeSelectByOneBit(boolean[] oneGroup) {
        byte result = 0x00;
        int temp = 1;
        for (boolean one : oneGroup) {
            if (one) {
                result = (byte) (result | temp);
            }
            temp = temp << 1;
        }
        return result;
    }

    /**
     * 字节选中对应的下标 , 从 0 开始
     *
     * @param b 字节
     */
    public static List<Integer> parseSelectedBytes4Bit(byte b) {
        boolean[] booleans = parseBytes4Bit(b, true);
        List<Integer> selectedList = new ArrayList<>();
        for (int i = 0; i < booleans.length; i++) {
            if (booleans[i]) {
                selectedList.add(i);
            }
        }
        return selectedList;
    }
}
