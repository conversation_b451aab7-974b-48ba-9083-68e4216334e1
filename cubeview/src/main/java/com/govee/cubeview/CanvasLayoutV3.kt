package com.govee.cubeview

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import kotlin.math.atan2
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2025/6/9
 *     version : 1.0.0
 *     desc    : 支持基础功能外，仅支持 h6069 正方形的添加流程软件加装手势流程
 *              新增：区域选中、拖拽、旋转功能
 */
class CanvasLayoutV3(context: Context, attrs: AttributeSet? = null) : CanvasLayoutV2(
    context, attrs
) {

    companion object {
        private const val ROTATION_BUTTON_SIZE = 24f
        private const val ROTATION_BUTTON_RADIUS = ROTATION_BUTTON_SIZE / 2
        private const val ROTATION_BUTTON_CLICK_RADIUS = ROTATION_BUTTON_RADIUS * 1.5f // 点击区域比显示区域大50%
        private const val ROTATION_BUTTON_MARGIN = 6f // 按钮与边框的间隙
        private const val SCALE_MAX = 3f
        private const val SCALE_MIN = 0.5f
        private const val TOUCH_SLOP = 20f
    }

    // 旋转按钮图片
    private var rotationButtonBitmap: Bitmap? = null

    init {
        // FrameLayout 默认设置 setWillNotDraw(true)，需要设置为 false 来启用 onDraw
        setWillNotDraw(false)

        // 加载旋转按钮图片
        try {
            val resourceId = context.resources.getIdentifier("h6069_btn_add_rotate", "mipmap", context.packageName)
            if (resourceId != 0) {
                rotationButtonBitmap = BitmapFactory.decodeResource(context.resources, resourceId)
            }
        } catch (e: Exception) {
            // 如果加载失败，保持为 null，将使用绘制的图形
        }
    }

    private var isZoom = false
    private var downX1 = 0F
    private var downX2 = 0F
    private var downY1 = 0F
    private var downY2 = 0F
    private var oldDist = 0f
    private var moveDist = 0f

    // 手势状态
    private var isRotatingRegion = false
    private var isDraggingRegion = false
    private var isDraggingCanvas = false
    private var initialTouchPoint = PointF()
    private var touchStartPoint = PointF()

    // 区域选中和旋转相关
    private var selectedRegionId: Int = -1 // 当前选中的区域ID
    private var selectedShapeGroup: MutableList<BaseShapeView> = mutableListOf() // 选中区域的所有 BaseShapeView
    private var regionCenterPoint = PointF() // 选中区域的中心点
    private var rotationButtonPoint = PointF() // 旋转按钮位置
    private var currentRegionRotation = 0f // 当前区域旋转角度
    private var lastRotationAngle = 0f

    // 选中区域的边框（只在选中时计算一次）
    private var selectedRegionBounds: RectF? = null

    // 画笔
    private val selectionPaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeWidth = 2f
        color = toColor(com.govee.ui.R.color.ui_color_block_style_16_3_stroke)
    }

    // 区域编号集合，顺序与 shapeViews 保持一致
    private var regionIds: List<Int> = emptyList()

    // 潜在拖拽状态：用于区分单击和拖拽
    private var isPotentialDrag: Boolean = false
    private var potentialDragRegionId: Int = -1

    fun setRegionIds(ids: List<Int>) {
        regionIds = ids

        // 重置选中状态
        selectedRegionId = -1
        selectedShapeGroup.clear()
    }

    // 根据触摸点获取区域ID
    private fun getRegionIdAtPoint(x: Float, y: Float): Int {

        for (i in canvasLayoutProxyV1.shapeViews.indices.reversed()) {
            val shape = canvasLayoutProxyV1.shapeViews[i]
            val regionId = regionIds.getOrNull(i) ?: -1
            val isInShape = shape.isPointInShape(PointF(x, y))

            if (isInShape) {
                return regionId
            }
        }

        return -1
    }

    // 根据区域ID获取该区域的所有 BaseShapeView
    private fun getShapesByRegionId(regionId: Int): List<BaseShapeView> {
        if (regionId == -1) return emptyList()

        val shapes = mutableListOf<BaseShapeView>()
        regionIds.forEachIndexed { index, id ->
            if (id == regionId && index < canvasLayoutProxyV1.shapeViews.size) {
                shapes.add(canvasLayoutProxyV1.shapeViews[index])
            }
        }

        return shapes
    }

    // 计算区域中心点
    private fun calculateRegionCenter(shapes: List<BaseShapeView>): PointF {
        if (shapes.isEmpty()) return PointF()
        var sumX = 0f
        var sumY = 0f
        shapes.forEach { shape ->
            sumX += shape.centerPos.x
            sumY += shape.centerPos.y
        }
        return PointF(sumX / shapes.size, sumY / shapes.size)
    }

    // 计算区域边界框：根据区域内所有 BaseShapeView 的最大最小 x/y 坐标
    private fun calculateRegionBounds(shapes: List<BaseShapeView>): RectF {
        if (shapes.isEmpty()) return RectF()
        var minX = Float.MAX_VALUE
        var maxX = Float.MIN_VALUE
        var minY = Float.MAX_VALUE
        var maxY = Float.MIN_VALUE

        shapes.forEach { shape ->
            // 获取每个 BaseShapeView 的中心点坐标
            val centerX = shape.centerPos.x
            val centerY = shape.centerPos.y
            // 获取形状的实际半径
            val radius = shape.params.outsideLineLength / 2f

            // 计算形状的实际边界
            val left = centerX - radius
            val right = centerX + radius
            val top = centerY - radius
            val bottom = centerY + radius

            minX = kotlin.math.min(minX, left)
            maxX = kotlin.math.max(maxX, right)
            minY = kotlin.math.min(minY, top)
            maxY = kotlin.math.max(maxY, bottom)
        }

        // 添加最小边距，让框更精确
        val padding = 5f
        return RectF(minX - padding, minY - padding, maxX + padding, maxY + padding)
    }

    /**
     * 计算从中心点出发，指定角度与边界的交点（射线法）
     * @param center 区域中心点
     * @param bounds 区域边界（RectF）
     * @param angle 角度（度），0为x正方向，逆时针
     * @return 边界交点坐标
     */
    private fun calculateEdgePointByAngle(center: PointF, bounds: RectF, angle: Float): PointF {
        // 将角度转为弧度
        val rad = Math.toRadians(angle.toDouble())
        val dx = kotlin.math.cos(rad).toFloat()
        val dy = kotlin.math.sin(rad).toFloat()
        // 计算射线与四条边的交点
        val candidates = mutableListOf<PointF>()
        // 与 left 边交点
        if (dx != 0f) {
            val t = (bounds.left - center.x) / dx
            if (t > 0) {
                val y = center.y + t * dy
                if (y in bounds.top..bounds.bottom) candidates.add(PointF(bounds.left, y))
            }
        }
        // 与 right 边交点
        if (dx != 0f) {
            val t = (bounds.right - center.x) / dx
            if (t > 0) {
                val y = center.y + t * dy
                if (y in bounds.top..bounds.bottom) candidates.add(PointF(bounds.right, y))
            }
        }
        // 与 top 边交点
        if (dy != 0f) {
            val t = (bounds.top - center.y) / dy
            if (t > 0) {
                val x = center.x + t * dx
                if (x in bounds.left..bounds.right) candidates.add(PointF(x, bounds.top))
            }
        }
        // 与 bottom 边交点
        if (dy != 0f) {
            val t = (bounds.bottom - center.y) / dy
            if (t > 0) {
                val x = center.x + t * dx
                if (x in bounds.left..bounds.right) candidates.add(PointF(x, bounds.bottom))
            }
        }
        // 取距离中心点最近的交点
        return candidates.minByOrNull { (it.x - center.x) * (it.x - center.x) + (it.y - center.y) * (it.y - center.y) } ?: center
    }

    // 更新旋转按钮位置
    private fun updateRotationButtonPosition() {
        if (selectedShapeGroup.isEmpty()) return
        // 计算区域边界
        val bounds = calculateRegionBounds(selectedShapeGroup)
        // 按钮角度（正上方，叠加区域旋转）
        val buttonAngle = currentRegionRotation + 270f
        // 计算边界交点
        val edgePoint = calculateEdgePointByAngle(regionCenterPoint, bounds, buttonAngle)
        // 按钮中心点 = 边界点 + 间隙（沿射线方向外移）
        val rad = Math.toRadians(buttonAngle.toDouble())
        val offsetX = ((ROTATION_BUTTON_RADIUS + ROTATION_BUTTON_MARGIN) * kotlin.math.cos(rad)).toFloat()
        val offsetY = ((ROTATION_BUTTON_RADIUS + ROTATION_BUTTON_MARGIN) * kotlin.math.sin(rad)).toFloat()
        rotationButtonPoint.set(edgePoint.x + offsetX, edgePoint.y + offsetY)
    }

    // 检查触摸点是否在旋转按钮上
    private fun isTouchOnRotationButton(x: Float, y: Float): Boolean {
        if (selectedRegionId == -1) return false
        val distance = sqrt(
            (x - rotationButtonPoint.x) * (x - rotationButtonPoint.x) +
                (y - rotationButtonPoint.y) * (y - rotationButtonPoint.y)
        )
        return distance <= ROTATION_BUTTON_CLICK_RADIUS
    }

    // 计算旋转角度
    private fun calculateRotationAngle(currentX: Float, currentY: Float): Float {
        val startAngle = atan2(touchStartPoint.y - regionCenterPoint.y, touchStartPoint.x - regionCenterPoint.x)
        val currentAngle = atan2(currentY - regionCenterPoint.y, currentX - regionCenterPoint.x)

        var angle = Math.toDegrees((currentAngle - startAngle).toDouble()).toFloat()
        // 将角度限制在0-360度之间
        angle = (angle + 360) % 360
        return angle
    }

    // 旋转区域内的所有 BaseShapeView
    private fun rotateRegionShapes(deltaAngle: Float) {
        selectedShapeGroup.forEach { shape ->
            // 计算相对于区域中心的旋转
            val dx = shape.centerPos.x - regionCenterPoint.x
            val dy = shape.centerPos.y - regionCenterPoint.y

            val distance = sqrt(dx * dx + dy * dy)
            val originalAngle = atan2(dy, dx)
            val newAngle = originalAngle + Math.toRadians(deltaAngle.toDouble())

            val newX = regionCenterPoint.x + distance * kotlin.math.cos(newAngle).toFloat()
            val newY = regionCenterPoint.y + distance * kotlin.math.sin(newAngle).toFloat()

            shape.setShapeLayoutParams(newX, newY)
            shape.offsetRotation = (shape.offsetRotation + deltaAngle) % 360
            shape.invalidate()
        }

        currentRegionRotation = (currentRegionRotation + deltaAngle) % 360
        updateRotationButtonPosition()

        // 旋转后刷新电源图标，保证其跟随旋转
        updatePowerItemForMainRegion()
    }

    // 选中区域
    private fun selectRegion(regionId: Int) {
        // 如果是同一个区域，仍然要确保状态正确并重绘
        if (selectedRegionId == regionId && regionId != -1) {
            invalidate()
            return
        }

        val isNewRegion = selectedRegionId != regionId
        selectedRegionId = regionId
        selectedShapeGroup.clear()
        // 只在切换到新区域时重算边框
        if (isNewRegion) {
            selectedRegionBounds = null
        }

        if (regionId != -1) {
            selectedShapeGroup.addAll(getShapesByRegionId(regionId))
            regionCenterPoint = calculateRegionCenter(selectedShapeGroup)
            currentRegionRotation = 0f
            // 只在首次选中该区域时计算一次边框
            if (selectedRegionBounds == null) {
                selectedRegionBounds = calculateRegionBounds(selectedShapeGroup)
            }
            updateRotationButtonPosition()
        } else {
            invalidate()
        }

        invalidate()
    }

    // 绘制选中状态和旋转按钮
    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (selectedRegionId != -1 && selectedShapeGroup.isNotEmpty()) {
            // 绘制蓝色边界框，随区域旋转，大小固定
            selectedRegionBounds?.let { bounds ->
                canvas.save()
                canvas.rotate(currentRegionRotation, regionCenterPoint.x, regionCenterPoint.y)
                canvas.drawRect(bounds, selectionPaint)
                canvas.restore()
            }
            // 绘制旋转按钮
            if (rotationButtonBitmap != null) {
                val bitmap = rotationButtonBitmap!!
                val buttonSize = ROTATION_BUTTON_SIZE
                val left = rotationButtonPoint.x - buttonSize / 2
                val top = rotationButtonPoint.y - buttonSize / 2
                val right = rotationButtonPoint.x + buttonSize / 2
                val bottom = rotationButtonPoint.y + buttonSize / 2
                val destRect = RectF(left, top, right, bottom)
                canvas.drawBitmap(bitmap, null, destRect, null)
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val isSoftLine = mModeType == CanvasLayoutModeType.SoftLine

        if (!isSoftLine) {
            return super.onTouchEvent(event)
        }

        event?.let {
            when (it.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    initialTouchPoint.set(it.x, it.y)
                    touchStartPoint.set(it.x, it.y)

                    // 重置所有状态
                    isDraggingRegion = false
                    isDraggingCanvas = false
                    isRotatingRegion = false
                    isPotentialDrag = false
                    potentialDragRegionId = -1

                    // 检查是否点击在旋转按钮上
                    val isOnRotationButton = isTouchOnRotationButton(it.x, it.y)

                    if (isOnRotationButton) {
                        isRotatingRegion = true
                        lastRotationAngle = currentRegionRotation
                        return true
                    }

                    // 获取触摸点所在的区域ID
                    val touchedRegionId = getRegionIdAtPoint(it.x, it.y)

                    if (touchedRegionId != -1) {
                        // 点击在某个区域上
                        if (selectedRegionId == touchedRegionId) {
                            // 已选中的区域，只允许单击切换和旋转，不允许拖拽
                            isPotentialDrag = true
                            potentialDragRegionId = touchedRegionId
                        } else if (selectedRegionId != -1) {
                            // 有其他区域选中，点击了不同区域，选中新区域
                            selectRegion(touchedRegionId)
                        } else {
                            // 没有选中任何区域，点击区域进入拖拽模式
                            isPotentialDrag = true
                            potentialDragRegionId = touchedRegionId
                        }
                        return true
                    } else {
                        // 点击在空白处，开始画布拖拽，不取消选中状态
                        showLog(TAG, "🖐️ 点击空白处（画布拖拽）")
                        isPotentialDrag = false
                        isDraggingCanvas = true
                        return super.onTouchEvent(event)
                    }
                }

                MotionEvent.ACTION_MOVE -> {
                    val pointerCount = event.pointerCount
                    if (pointerCount == 2) {
                        // 双指缩放逻辑
                        val x1 = event.getX(0)
                        val x2 = event.getX(1)
                        val y1 = event.getY(0)
                        val y2 = event.getY(1)
                        val changeX1 = (x1 - downX1).toDouble()
                        val changeX2 = (x2 - downX2).toDouble()
                        val changeY1 = (y1 - downY1).toDouble()
                        val changeY2 = (y2 - downY2).toDouble()

                        if (scaleX > 1) {
                            val lessX = (changeX1 / 2 + changeX2 / 2).toFloat()
                            val lessY = (changeY1 / 2 + changeY2 / 2).toFloat()
                            setSelfPivot(-lessX, -lessY)
                            showLog(TAG, "此时为滑动")
                        }

                        moveDist = spacing(event)
                        val space = moveDist - oldDist
                        val scale = (scaleX + space / width)
                        if (scale < SCALE_MIN) {
                            setScale(SCALE_MIN)
                        } else if (scale > SCALE_MAX) {
                            setScale(SCALE_MAX)
                        } else {
                            setScale(scale)
                        }
                    } else if (pointerCount == 1) {
                        // 区分选中状态和非选中状态的移动处理
                        if (isPotentialDrag) {
                            if (selectedRegionId == potentialDragRegionId) {
                                // 已选中区域：忽略移动（只支持旋转）
                                return true
                            } else {
                                // 未选中区域：检测拖拽距离
                                val dx = it.x - initialTouchPoint.x
                                val dy = it.y - initialTouchPoint.y
                                val distance = sqrt(dx * dx + dy * dy)

                                if (distance > TOUCH_SLOP) {
                                    // 移动距离超过阈值，进入区域拖拽模式
                                    isDraggingRegion = true
                                    isPotentialDrag = false
                                    showLog(TAG, "🖐️ 开始拖拽区域 $potentialDragRegionId")
                                } else {
                                    // 移动距离不够，继续等待
                                    return true
                                }
                            }
                        } else if (isRotatingRegion) {
                            // 旋转区域
                            val deltaAngle = calculateRotationAngle(it.x, it.y)
                            rotateRegionShapes(deltaAngle)
                            touchStartPoint.set(it.x, it.y) // 更新起始点，实现连续旋转

                            // 旋转后刷新电源图标，保证其跟随旋转
                            updatePowerItemForMainRegion()

                            invalidate()
                            return true
                        } else if (isDraggingRegion) {
                            // 拖拽未选中的区域
                            val dx = it.x - initialTouchPoint.x
                            val dy = it.y - initialTouchPoint.y

                            // 获取要拖拽的区域内所有形状
                            val dragShapes = getShapesByRegionId(potentialDragRegionId)

                            // 更新所有拖拽区域内图形的位置
                            dragShapes.forEach { shape ->
                                val oldX = shape.centerPos.x
                                val oldY = shape.centerPos.y
                                val newX = oldX + dx
                                val newY = oldY + dy

                                shape.setShapeLayoutParams(newX, newY)
                                shape.invalidate()
                            }

                            // 拖动后刷新电源图标，保证其跟随第一块移动
                            // 注意：现在 updatePowerItem 会跟随 regionId=0 的 shape
                            updatePowerItemForMainRegion()

                            // 更新初始触摸点
                            initialTouchPoint.set(it.x, it.y)

                            invalidate()
                            return true
                        } else if (isDraggingCanvas) {
                            // 移动画布
                            return super.onTouchEvent(event)
                        }
                    }
                    return true
                }

                MotionEvent.ACTION_POINTER_DOWN -> {
                    showLog(TAG, "onTouchEvent() 涂鸦模式 双指按下")
                    isZoom = event.pointerCount >= 2
                    downX1 = event.getX(0)
                    downX2 = event.getX(1)
                    downY1 = event.getY(0)
                    downY2 = event.getY(1)
                    oldDist = moveDistance(event)
                    return false
                }

                MotionEvent.ACTION_POINTER_UP -> {
                    isZoom = false
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 如果是潜在拖拽状态（没有真正拖拽），执行选中/取消选中操作
                    if (isPotentialDrag && !isDraggingRegion && potentialDragRegionId != -1) {
                        if (selectedRegionId == potentialDragRegionId) {
                            // 点击已选中的区域，取消选中
                            selectRegion(-1)
                        } else {
                            // 点击未选中的区域，选中该区域
                            selectRegion(potentialDragRegionId)
                        }
                    }

                    // 重置所有状态
                    isRotatingRegion = false
                    isDraggingRegion = false
                    isDraggingCanvas = false
                    isPotentialDrag = false
                    potentialDragRegionId = -1
                    invalidate()
                }

                else -> {}
            }
        }

        return super.onTouchEvent(event)
    }

    /**
     * 移动
     *
     * @param lessX
     * @param lessY
     */
    private fun setSelfPivot(lessX: Float, lessY: Float) {
        var setPivotX: Float
        var setPivotY: Float
        setPivotX = pivotX + lessX
        setPivotY = pivotY + lessY
        if (setPivotX < 0 && setPivotY < 0) {
            setPivotX = 0f
            setPivotY = 0f
        } else if (setPivotX > 0 && setPivotY < 0) {
            setPivotY = 0f
            if (setPivotX > width) {
                setPivotX = width.toFloat()
            }
        } else if (setPivotX < 0 && setPivotY > 0) {
            setPivotX = 0f
            if (setPivotY > height) {
                setPivotY = height.toFloat()
            }
        } else {
            if (setPivotX > width) {
                setPivotX = width.toFloat()
            }
            if (setPivotY > height) {
                setPivotY = height.toFloat()
            }
        }
        setPivot(setPivotX, setPivotY)
    }

    /**
     * 平移画面，当画面的宽或高大于屏幕宽高时，调用此方法进行平移
     */
    private fun setPivot(x: Float, y: Float) {
        pivotX = x
        pivotY = y
    }

    /**
     * 计算两个点的距离
     *
     * @param event
     * @return
     */
    private fun spacing(event: MotionEvent): Float {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            sqrt((x * x + y * y))
        } else {
            0f
        }
    }

    /**
     * 设置放大缩小
     */
    private fun setScale(scale: Float) {
        val validScale = if (scale > SCALE_MAX) {
            SCALE_MAX
        } else if (scale < SCALE_MIN) {
            SCALE_MIN
        } else {
            scale
        }
        showLog(TAG, "setScale() scale = $scale , validScale = $validScale")
        scaleX = validScale
        scaleY = validScale
    }

    /**
     * 双指移动的距离
     */
    private fun moveDistance(event: MotionEvent): Float {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            sqrt((x * x + y * y))
        } else 0f
    }

    /**
     * 刷新电源图标位置，确保电源图标跟随 regionId=0 的 shape
     * 通过临时调整 shapeViews 顺序实现，让 regionId=0 的 shape 排在首位
     */
    private fun updatePowerItemForMainRegion() {
        // 找到 regionId=0 的 shape
        val mainIndex = regionIds.indexOfFirst { it == 0 }
        if (mainIndex == -1 || mainIndex >= canvasLayoutProxyV1.shapeViews.size || mainIndex == 0) {
            // 如果没有找到 regionId=0 或它已经在首位，直接调用父类方法
            canvasLayoutProxyV1.updatePowerItemView()
            return
        }

        // 临时交换 shapeViews[0] 和 shapeViews[mainIndex]
        val originalFirst = canvasLayoutProxyV1.shapeViews[0]
        val mainShape = canvasLayoutProxyV1.shapeViews[mainIndex]

        // 保存原始的 offsetRotation 值
        val originalFirstRotation = originalFirst.offsetRotation
        val mainShapeRotation = mainShape.offsetRotation

        canvasLayoutProxyV1.shapeViews[0] = mainShape
        canvasLayoutProxyV1.shapeViews[mainIndex] = originalFirst

        // 临时设置 mainShape 的 offsetRotation 为 originalFirst 的值，保持电源图标位置连续性
        mainShape.offsetRotation = originalFirstRotation

        // 调用父类的电源图标更新方法
        canvasLayoutProxyV1.updatePowerItemView()

        // 恢复 shapeViews 原始顺序和 offsetRotation 值
        canvasLayoutProxyV1.shapeViews[0] = originalFirst
        canvasLayoutProxyV1.shapeViews[mainIndex] = mainShape
        mainShape.offsetRotation = mainShapeRotation
    }

}