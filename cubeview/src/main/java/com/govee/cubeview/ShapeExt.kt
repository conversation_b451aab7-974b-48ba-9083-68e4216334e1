package com.govee.cubeview

import android.content.Context
import android.graphics.Color
import android.os.Build
import android.util.Log
import android.view.View
import androidx.annotation.ColorRes

/**
 *     author  : sinrow
 *     time    : 2021/12/17
 *     version : 1.0.0
 *     desc    :
 */


/**
 * 防止重复点击事件 默认0.5秒内不可重复点击
 * @param interval 时间间隔 默认0.5秒
 * @param action 执行方法
 */
var lastClickTime = 0L
inline fun View.clickNoRepeat(interval: Long = 500, crossinline action: (view: View) -> Unit) {
    setOnClickListener {
        try {
            val currentTime = System.currentTimeMillis()
            if (lastClickTime != 0L && (currentTime - lastClickTime < interval)) {
                return@setOnClickListener
            }
            lastClickTime = currentTime
            action(it)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

inline fun Boolean.yes(block: () -> Unit): Boolean {
    return this.apply { if (this) block() }
}

inline fun Boolean.no(block: () -> Unit): Boolean {
    return this.apply { if (!this) block() }
}

fun log(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.d(tag, content)
    }
}

fun logw(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.w(tag, content)
    }
}

fun loge(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.e(tag, content)
    }
}

fun Int.checkAngle() = Utils.rangeRotation360(this)
fun Float.checkAngle() = Utils.rangeRotation360(this)

/**
 * 是否接近浅蓝色
 *
 * @param rgb
 * @return
 */
internal fun Int.isNearLowBlueColor(): Boolean = run {
    val hsv = FloatArray(3)
    Color.colorToHSV(this, hsv)
    val h = hsv[0].toInt()
    val s = (hsv[1] * 100).toInt()
    val v = (hsv[2] * 100).toInt()
    //log("TAG", "isNearLowBlueColor: h = $h ; s = $s ; v = $v")
    val nearH = 195 - 5 <= h && 195 + 5 >= h
    val nearS = 95 - 2 <= s && s <= 100
    val nearV = 82 - 2 <= v && v <= 100
    //log("TAG", "isNearLowBlueColor: nearH = $nearH ; nearS = $nearS ; nearV = $nearV")
    return nearH && nearS && nearV
}

 fun View.toColor(@ColorRes resId: Int): Int {
     return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
         resources.getColor(resId, null)
     } else {
         resources.getColor(resId)
     }
 }

internal fun Context.toColor(@ColorRes resId: Int): Int {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        resources.getColor(resId, null)
    } else {
        resources.getColor(resId)
    }
}

fun showLog(content: String) {
    log("CubeLib -- ", content)
}

fun showLog(tag: String, content: String) {
    log("CubeLib -- $tag", content)
}

fun showLog(tag: String, content: () -> String) {
    log("CubeLib -- $tag", content.invoke())
}


fun setViewsVisible(isVisible: Boolean, vararg views: View?) {
    for (it in views) {
        it?.visibleByBoolean(isVisible)
    }
}

fun View.visibleByBoolean(value: Boolean) {
    visibility = if (value) View.VISIBLE else View.GONE
}


fun View.visible() {
    this.visibility = View.VISIBLE
}

fun View.gone() {
    this.visibility = View.GONE
}

fun Context.isLightMode(): Boolean {
    return Utils.curLightMode(this)
}
