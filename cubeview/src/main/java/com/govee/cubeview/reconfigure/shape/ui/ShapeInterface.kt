package com.govee.cubeview.reconfigure.shape.ui

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.configuration.IConfig
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.data.CubeMoveFeastData
import com.govee.cubeview.shape.ShapePosition

/**
 *     author  : sinrow
 *     time    : 2022/9/26
 *     version : 1.0.0
 *     desc    :
 */

interface IBasicBusiness {

    /**
     * 根据翻转点 x 轴坐标进行翻转
     * <p>
     *     翻转动画步骤：1、整个画布从 0 到 90 度旋转以 Y 轴，取第一块的中心点的 X 坐标。
     *                2、旋转到 90 度时，计算翻转后的图形中心点以及角度，同时移动到翻转后的图形的中心点。
     *                3、整个画布从 270 翻转到 360 以防坐标系发生变化。
     *     注意点：1、翻转的x 需要改变 cameraDistance ，详见：FlipAnimationUtil.startAnimation
     *           2、不同图形翻转的角度算法不一致，可直接看代码逻辑
     *           3、需要重新计算每一块图形的方向
     *           4、翻转后的画布旋转角度以及图形旋转角度需要重新计算
     *
     * </p>
     */
    fun flipShapeViewByX()

    /**
     * 设置图形旋转角度
     * <p>
     *  遍历图形，offsetRotation = offsetRotation + onceRotation
     * </p>
     */
    fun setShapeRotation(onceRotation: Float)

    /**
     * 输出当前图形的数据
     */
    fun getShapeRotations(): ArrayList<ShapePosition>

    /**
     * 安装模式-下一块
     * @param changeLastShapeState
     * @return Pair<BaseShapeView, Int> first 当前块图形， second 当前图形 index
     */
    fun installNextShape(changeLastShapeState: Boolean = false): Pair<BaseShapeView, Int>?

    /**
     * 安装模式-上一块
     *
     * @return Pair<BaseShapeView, Int> first 当前块图形， second 当前图形 index
     */
    fun installLastShape(): Pair<BaseShapeView, Int>?

    /**
     * 安装模式-选中所有块
     */
    fun installAllShape()

    /**
     * 校验模式-下一步
     * @param nextPos 下一块 index
     */
    fun checkNextShape(nextPos: Int): PointF?

    /**
     * 校验模式-全部
     */
    fun checkAllShape(): Boolean

    /**
     * 颜色模式-选择全部图形块
     * @param isSelectedAll 是否选中全部图形
     */
    fun setSelectAll(isSelectedAll: Boolean): Boolean

    /**
     * 聚焦模式-更新聚点信息
     * @param pointF 聚点坐标
     * @param isIn 是否聚拢
     */
    fun updateFocusPoint(pointF: PointF, isIn: Boolean)


    /**
     * 颜色模式-设置颜色参数
     * @param colorList 颜色列表
     * @param brightnessList 亮度列表
     * @param selectedList 是否选中
     */
    fun setColorParams(
        colorList: ArrayList<Int>, brightnessList: ArrayList<Int>, selectedList: ArrayList<Boolean>
    )

    /**
     * 重置图形(最少保留一个)
     */
    fun resetShape(reserve: Boolean)
}

interface IShapeM {

    /**
     * 解析图形数据
     */
    fun parseShapeData(shapePosition: ArrayList<ShapePosition>)

    /**
     *  该方法用于刷新当前图形状态（不包括基本图形）
     */
    fun notifyRefreshShapeUI(modeType: CanvasLayoutModeType)

    /**
     * 重置图形(最少保留一个)
     */
    fun resetShape(reserve: Boolean)

    /**
     * 注册监听回调方法
     */
    fun registerListener(listener: OnShapeListener)

    /**
     * 移动到图形中心
     */
    fun move2ShapeCenter(animation: Boolean = false)

    /**
     * 销毁
     */
    fun onDestroy()
}

interface IShapeBusiness {

    /**
     * 图形根据坐标点 x 对应的 Y 轴翻转
     * @origin 原点坐标
     */
    fun flipByX(origin: PointF)

    /**
     * 图形水平旋转
     * @param onceRotation 旋转角度
     */
    fun horizontalRotation(onceRotation: Float, origin: PointF)

    /**
     * 根据当前状态是否显示中间文本
     */
    fun showSerialNumber(): Boolean

    /**
     * 当前图形是否可点击
     * <p>
     *     默认颜色模式可点击
     * </p>
     * @param canvasLayoutModeType 画布UI类型
     */
    fun enableClick(canvasLayoutModeType: CanvasLayoutModeType): Boolean

    /**
     * 颜色模式-图形处理
     */
    fun doSomething4ColorMode(block: () -> Unit)

    /**
     * 观影盛宴模式-图形处理
     */
    fun doSomething4MoveFeast(block: () -> Unit)

}

interface IShapeView : IShapeCalculate, IShapeDraw {

    /**
     * 配置图形基础参数(优先注入 config )
     */
    fun setConfig(config: IConfig)

    /**
     * 根据图形数据初始图形
     */
    fun initShapeData(shapeData: ShapeData)

    /**
     *  添加相对画布位置
     *  <p>
     *      内部实现图形偏移位置
     *  </P>
     */
    fun setShapeLayoutParams(centerPointX: Float, centerPointY: Float, shapeRectWith: Float)

    /**
     * 刷新 Shape 状态
     *
     */
    fun updateUIState(type: UIType)

    /**
     * 设置 shape 颜色
     *<p>
     * 根据图形规则设置背景颜色
     *</p>
     */
    fun setShapeColor(color: Int)

    /**
     * 设置图形中心文本内容
     */
    fun setShapeCenterText(content: String)
}


interface IShapeCalculate {

    /**
     * 根据角度获取下一个shape的中点point，多边形的坐标不一样，内部自己实现
     * @param pointF 当前图形中心点
     * @param length 当前图形中心点与下一个图形中心点的距离
     * @param rotation 旋转角度
     */
    fun getNextShapeCenterPointByRotation(pointF: PointF, length: Float, rotation: Float): PointF

    /**
     * 获取多边形旋转后的坐标
     * @param centerPointF 当前图形中心点
     * @param length 当前图形中心点与下一个图形中心点的距离
     * @param rotation 旋转角度
     * @param forSelf 是否用于画图形（默认是用于画图形，Y 形灯计算辅助图形时需要使用 false）
     */
    fun getShapePoint(
        centerPointF: PointF, length: Float, rotation: Float, forSelf: Boolean = true
    ): Array<PointF>

    /**
     * 根据协议，获取旋转角度
     */
    fun getRotationValue(): Int

    /**
     * 根据方向获取方向角度 Tag 0-5
     * <p>
     *     该方法用于添加图形时，需要确认下一个图形的旋转角度。
     *     又因根据方向可知道对应的图形旋转角度，故采用 index 形式标识为方向
     * </p>
     * @param directionTag 下一个图形的方向
     */
    fun getRotationByDirectionTag(directionTag: Int): Float

    /**
     * 添加图形的中心点
     * <p>
     *     该方法用于计算该方块的添加辅助图形的中心点距离。一般是内切圆/外切圆(具体看设备形态)+辅助图形大小的一半
     * </p>
     */
    fun getAddAssistShapeCenterLength(): Float
}

interface IShapeDraw {

    /**
     * 绘制背景区域
     * <p>
     *     根据 canvas.drawPath(path, paint) 形成闭环区间，绘制区域内的颜色
     * </p>
     * @param canvas 画布
     * @param paint 画笔
     */
    fun drawBackground(canvas: Canvas, paint: Paint)

    /**
     * 绘制基础图形
     * <p>
     *     把点连成线，形成图形
     * </p>
     * @param canvas 画布
     * @param paint 画笔
     */
    fun drawBaseShape(canvas: Canvas, paint: Paint)

    /**
     * 绘制选中边框
     * <p>
     *     Y 形灯的选中框特殊处理
     * </p>
     * @param canvas 画布
     * @param paint 画笔
     */
    fun drawSelectedBorder(canvas: Canvas, paint: Paint)

    /**
     * 绘制其他图形属性
     * <p>
     *     立体六边形有特殊属性
     * </p>
     * @param canvas 画布
     * @param paint 画笔
     */
    fun drawShapeOtherAttributes(canvas: Canvas, paint: Paint)

    /**
     * 图形中心绘制文本(多数用于序号、亮度等)
     * <P>
     *  需求：文本+背景(可定制化)
     * <p>
     * @param canvas 画布
     * @param paint 画笔
     */
    fun drawCenterText(canvas: Canvas, paint: Paint)

    /**
     * 图形中心绘制图片(用于盛宴)
     */
    fun drawCenterDrawBitmap(canvas: Canvas, paint: Paint)

}

interface IGraffiti {
    /**
     * 设置默认背景色
     */
    fun setDefaultColor(color: Int)

    /**
     * 涂鸦
     */
    fun setGraffitiColor(color: Int)

    fun getGraffitiColors(): LinkedHashMap<Int, MutableList<Int>>

    /**
     * 橡皮擦
     */
    fun eraser()

    /**
     * 撤销
     */
    fun onRevoke()

    /**
     * 还原
     */
    fun onRestore()

    fun setColors(color: MutableList<Int>)

    /**
     * 清除
     */
    fun resetGraffiti()
}

/**
 * 统一对外监听器
 */
open class OnShapeListener : OnSelectShapeChange, OnAddDeleteChange, OnFocusDragChange,
    OnScaleAndTransitionChange, OnMoveFeastListener, OnSelectedMultiPower, OnIGraffitiListener {

    override fun selectedShape(selectedShapes: ArrayList<Boolean>) {}
    override fun graphQuantityChanges(size: Int) {}
    override fun focusDragPointChange(point: PointF) {}
    override fun selectedFocusPoint(params: ByteArray) {}
    override fun scaleAndTransition(scale: Float, point: PointF, animation: Boolean) {}
    override fun getMoveFeastSelectedAreaMsg(): CubeMoveFeastData {
        return CubeMoveFeastData()
    }

    override fun notifyFeastChange(feastDataHashMap: HashMap<Int, MutableList<CubeMoveFeastData>>) {}
    override fun selectedMultiPower(powerNum: Int) {}
    override fun notifyGraffitiChangeListener() {

    }
}

interface OnScaleAndTransitionChange {
    fun scaleAndTransition(scale: Float, point: PointF, animation: Boolean = false)
}

/**
 * 颜色模式下选择图形回调
 */
interface OnSelectShapeChange {
    fun selectedShape(selectedShapes: ArrayList<Boolean>)
}

/**
 * 添加删除方块
 */
interface OnAddDeleteChange {
    fun graphQuantityChanges(size: Int)
}

/**
 * 聚焦模式下选择的点
 */
interface OnFocusDragChange {
    fun focusDragPointChange(point: PointF)
    fun selectedFocusPoint(params: ByteArray)
}

interface OnMoveFeastListener {
    fun getMoveFeastSelectedAreaMsg(): CubeMoveFeastData

    fun notifyFeastChange(feastDataHashMap: HashMap<Int, MutableList<CubeMoveFeastData>>)
}

interface OnSelectedMultiPower {
    fun selectedMultiPower(powerNum: Int)
}

interface OnIGraffitiListener {
    fun notifyGraffitiChangeListener()

}