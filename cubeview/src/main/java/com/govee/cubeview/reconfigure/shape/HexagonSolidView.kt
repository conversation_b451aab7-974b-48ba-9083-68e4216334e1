package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.*
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.reconfigure.shape.assist.ImgPowerShapeView
import com.govee.cubeview.reconfigure.shape.ui.powerAssistShapeCenterPoints
import com.govee.cubeview.reconfigure.shape.ui.toSetFloatArray
import com.govee.cubeview.shape.ShapePosition
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2022/9/15
 *     version : 1.0.0
 *     desc    :
 */
class HexagonSolidView(context: Context) : BusinessShapeView(context) {


    override fun getNextShapeCenterPointByRotation(
        pointF: PointF,
        length: Float,
        rotation: Float
    ): PointF {
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(
        centerPointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<PointF> {
        return Array(6) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(rotation, centerPointF.x, centerPointF.y)

            /*六边形，最左边的点为起始点 0 ，默认角度为 60，经过旋转后，变成了左上角顶点*/
            /*获取辅助图形的旋转点，需要从左上角开始，这样就可以达到了要求了*/

            val shapePoint = Array(6) { FloatArray(2) }
            shapePoint[0] =
                floatArrayOf(
                    (length * cos(2 * PI * 3 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 3 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[1] =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[2] =
                floatArrayOf(
                    (length * cos(2 * PI * 5 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 5 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[3] =
                floatArrayOf(
                    (length * cos(0.0) + centerPointF.x).toFloat(),
                    (length * sin(0.0) + centerPointF.y).toFloat()
                )
            shapePoint[4] =
                floatArrayOf(
                    (length * cos(2 * PI * 1 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 1 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[5] =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + centerPointF.y).toFloat()
                )

            matrix.mapPoints(shapePoint[0])
            matrix.mapPoints(shapePoint[1])
            matrix.mapPoints(shapePoint[2])
            matrix.mapPoints(shapePoint[3])
            matrix.mapPoints(shapePoint[4])
            matrix.mapPoints(shapePoint[5])

            toSetFloatArray(shapePoint)
        }
    }

    /**
     * directionTag  0-5
     *   输出边 - 输入边
     *   1 -- none
     *   2 -- 1
     *   3 -- 2, 6
     *   4 -- 1
     *   5 -- 2, 6
     *   6 -- 1
     * */
    override fun getRotationByDirectionTag(directionTag: Int): Float {
        /*directionTag： 建议以 index 作为序号*/
        /* 0-> 90 */
        /* 1-> 90 */
        /* 2->120 */
        /* 3->210 */
        /* 4->210 */
        /* 5->300 */
        return directionTag * 60 - 60f
    }

    override fun drawBackground(canvas: Canvas, paint: Paint) {
        val showPartitionBackground = showPartitionBackground()
        showLog(TAG, "drawBackground: showPartitionBackground = $showPartitionBackground ")
        if (showPartitionBackground) {
            drawCustomPreViewUI(canvas)
            return
        }

        super.drawBackground(canvas, paint)

        val onlyDisplayTheTopArea = onlyDisplayTheTopArea()
        showLog(TAG, "drawBackground: onlyDisplayTheTopArea() = ${onlyDisplayTheTopArea()} ")
        if (onlyDisplayTheTopArea) {
            drawCustomCheckingUI(canvas)
            return
        }
    }

    /**
     * 处理预览模式下的背景颜色
     */
    private fun drawCustomPreViewUI(canvas: Canvas) {
        val allShapePoint =
            getShapeDefaultPoint(params.outsideLineLength)
        val shapePosition1 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[3].x, allShapePoint[3].y),
                PointF(allShapePoint[4].x, allShapePoint[4].y),
                PointF(allShapePoint[5].x, allShapePoint[5].y)
            )

        val shapePosition2 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[5].x, allShapePoint[5].y),
                PointF(allShapePoint[0].x, allShapePoint[0].y),
                PointF(allShapePoint[1].x, allShapePoint[1].y)
            )

        val shapePoint3 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[1].x, allShapePoint[1].y),
                PointF(allShapePoint[2].x, allShapePoint[2].y),
                PointF(allShapePoint[3].x, allShapePoint[3].y)
            )

        drawView(canvas, shapePosition1, params.preViewColors[0])
        drawView(canvas, shapePosition2, params.preViewColors[1])
        drawView(canvas, shapePoint3, params.preViewColors[2])
    }


    private fun drawCustomCheckingUI(canvas: Canvas) {
        // 显示图形 y 轴最小，x 最大的
        // 校准的 ic 数，是怎么处理的？
        // 安装、校准的时候，发送的 ic 数量的逻辑：
        // ----  校准的个数*6 +当前块插入的序号对应的数量，如果是 1、2 编号，则发送0，3、4则发2，0、5则发 4 -----
        //            1, 2 -> icIndex = 0
        //            3, 4 -> icIndex = 2
        //            0, 5 -> icIndex = 4

        // 分区，然后确认顶部的是哪个边序号出去的。
        val second = getCheckStateAreaInfo().second
        drawView(canvas, second, params.checkingColor)
    }

    fun getCheckStateIcIndex(): Pair<Int, Array<PointF>> {
        val pair = getCheckStateAreaInfo()
        val topPoint = pair.first
        val pointFs = pair.second
        var icIndex = 0
        when (topPoint) {
            1, 2 -> icIndex = 0
            3, 4 -> icIndex = 2
            0, 5 -> icIndex = 4
        }
        return Pair(icIndex, pointFs)
    }

    //获取安装时的显示区域信息
    fun getCheckStateAreaInfo(): Pair<Int, Array<PointF>> {

        val allShapePoint = getShapeDefaultPoint()

        var index = 0
        //x最大，y最小
        val minPoint = intArrayOf(Int.MIN_VALUE, Int.MAX_VALUE, 0)

        //转成int，防止float判断误差
        val allShapePoint4Int = arrayOfNulls<IntArray>(6)
        allShapePoint.forEachIndexed { i, floats ->
            allShapePoint4Int[i] = intArrayOf(floats.x.toInt(), floats.y.toInt())
        }

        allShapePoint4Int.forEachIndexed { i, curPoint ->
            if (curPoint!![1] == minPoint[1]) {
                if (curPoint[0] > minPoint[0]) {
                    index = i
                    minPoint[0] = curPoint[0]
                    minPoint[1] = curPoint[1]
                    minPoint[2] = 1
                }
            } else if (curPoint[1] < minPoint[1]) {
                index = i
                minPoint[0] = curPoint[0]
                minPoint[1] = curPoint[1]
            }
        }

        //奇数边+1
        if (index % 2 == 1) {
            if (minPoint[2] == 1) {
                index--
            } else {
                index++
            }
        }
        index = Utils.rangeInt(index, 6)

        showLog(TAG, "getCheckStateAreaInfo: offsetR=$offsetRotation , index=$index ")

        return Pair(
            index,
            Array(4) { PointF() }.apply {
                this[0] = allShapePoint[Utils.rangeInt(index - 1, 6)]
                this[1] = allShapePoint[index]
                this[2] = allShapePoint[Utils.rangeInt(index + 1, 6)]
                this[3] = selfCenterPointF
            }
        )
    }

    /**
     * 处于校验模式下，当前正在校验的方块只显示顶层视图
     */
    private fun onlyDisplayTheTopArea(): Boolean {
        return shapeState.let {
            it.type.isCheckType() || it.type.isInstallType() && (it.state == 1 || it.state == 2)
        }
    }

    /**
     * 显示分区背景颜色
     * <p>
     *     作用于预览模式、校验模式
     * </p>
     */
    private fun showPartitionBackground(): Boolean {
        return shapeState.type.isPreViewType()
    }

    /**
     * 图形分区
     * 使用 canvas.drawPath(it, checkingColorPaint)，根据路径 + Paint.Style.FILL 实现实心图形背景
     */
    private fun drawView(canvas: Canvas, shapePoint3: Array<PointF>, bgColor: Int) {
        baseShapePath.let {
            it.reset()
            shapePoint3.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }
            it.close()
            val checkingColorPaint = Paint()
            checkingColorPaint.color = bgColor
            checkingColorPaint.style = Paint.Style.FILL
            canvas.drawPath(it, checkingColorPaint)
            it.reset()
        }
    }


    override fun drawShapeOtherAttributes(canvas: Canvas, paint: Paint) {
        // 有两个业务。1、基础的中间属性 2、预览模式下，不同背景色

        val length = params.outsideLineLength
        val shapePoint =
            arrayOf(
                PointF(
                    (length * cos(2 * PI * 4 / 6) + selfCenterPointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + selfCenterPointF.y).toFloat()
                ),
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(
                    (length * cos(0.0) + selfCenterPointF.x).toFloat(),
                    (length * sin(0.0) + selfCenterPointF.y).toFloat()
                ),
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(
                    (length * cos(2 * PI * 2 / 6) + selfCenterPointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + selfCenterPointF.y).toFloat()
                ),
            )

        baseShapePath.let {
            it.reset()
            shapePoint.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }
            val matrix = Matrix()//旋转
            matrix.setRotate(offsetRotation, selfCenterPointF.x, selfCenterPointF.y)

            /*画路径*/
            it.transform(matrix)
            /*画路径*/
            canvas.drawPath(it, paint)

            it.reset()
        }
    }

    override fun drawCenterText(canvas: Canvas, paint: Paint) {
        val textBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        var color = if (shapeState.type.isInstallType()) params.shapeColor else params.shapeColor

        if (modeType == CanvasLayoutModeType.MoveFeast) {
            color = toColor(moveFeastData.color)
        }
        textBgPaint.color = color

        textBgPaint.style = Paint.Style.FILL_AND_STROKE
        canvas.drawCircle(width / 2f, height / 2f, params.textBgRadius, textBgPaint)
        super.drawCenterText(canvas, paint)
    }

    override fun showSerialNumber(): Boolean {
        if (modeType.isInstallMode()) {
            return true
        }
        return super.showSerialNumber()
    }

    override fun getPathShapeCenterPoints(): PointF {
        showLog(TAG, "getPathShapeCenterPoints: offsetRotation = $offsetRotation ")
        return getNextShapeCenterPoints(offsetRotation + 180, params.innerLineLength)
    }

    override fun getPathEntity(lastShapeView: BaseShapeView?): PathEntity {
        lastShapeView?.let {
            /*1、序号*/
            val pathNum = Constants.getInstallNumberWithDirectionTag2(
                params.shapeType, getPathShapePathNum(offsetRotation)
            )

            /*2、角度*/
            /*计算出的绝对角度，相对于画布*/
            val calAngle = Utils.calAngle(it.centerPos, centerPos)
            val roundAngle = (calAngle / 30f).roundToInt() * 30 + 60
            val angleOffSet = Utils.rangeRotation360(roundAngle - it.offsetRotation)
            val lastShapeDirectionTag = (angleOffSet / 60f).toInt()

            /*赋值 -- 上一个图形是从哪条边出的*/
            val rotation = getRotationByDirectionTag(lastShapeDirectionTag) + it.offsetRotation

            /*3、中心点坐标*/
            val centerPoint =
                PointF((it.centerPos.x + centerPos.x) / 2, (it.centerPos.y + centerPos.y) / 2)

            return PathEntity().apply {
                this.pathNum = pathNum
                this.rotation = rotation
                this.centerPointF = centerPoint
            }
        } ?: run {
            val pointF = powerAssistShapeCenterPoints(ImgPowerShapeView.SIZE_WIDTH)
                .let {
                    Utils.centerPoint(it, centerPos)
                }
            return PathEntity().apply {
                this.pathNum = -1
                this.rotation = (offsetRotation - params.defaultAngle)
                this.centerPointF = pointF
            }
        }
    }

    /**
     * 获取下一个图形的 shapePosition 信息
     */
    override fun getNextShapePosition(edgeTag: Int): ShapePosition {
        val type = params.shapeType
        val rotationByDirectionTag = getRotationByDirectionTag(edgeTag) // directionTag * 60f
        Constants.getShapeDefaultAngle()
        val nextRotation =
            rotationByDirectionTag + offsetRotation // directionTag * 60f + offsetRotation = next.offsetRotation.checkAngle().roundToInt()
        val nextCenterPoint =
            getNextShapeCenterPoints(nextRotation)
        /*设置自身offsetTag -- 需要计算输入位置冲突*/
        val offsetTag = Constants.getNextShapeSupportOffset(edgeTag, type).first

        // 下一个图形的旋转角度
        val finalRotation = (offsetRotation + rotationByDirectionTag + offsetTag.first) % 360

        // 若出边为电源边，则需要矫正当前图形的旋转角度
        if (edgeTag == Constants.getShapeInputTag(type)) {
            val (firOffSetTag, secOffsetTag) = Constants.getNextShapeSupportOffset(edgeTag, type)
            if (secOffsetTag != null) {
                if (secOffsetTag.first == offsetTag.first) {
                    /*旋转上一个图形*/
                    this.offsetRotation -= secOffsetTag.first - firOffSetTag.first
                    this.offsetRotation = this.offsetRotation % 360
                } else {
                    /*旋转上一个图形*/
                    this.offsetRotation += secOffsetTag.first - firOffSetTag.first
                    this.offsetRotation = this.offsetRotation % 360
                }
            }
        }

        return ShapePosition(
            type, nextCenterPoint.x, nextCenterPoint.y,
            finalRotation.checkAngle().roundToInt()
        )
    }

    /**
     * @param subShapeView 下一块图形的信息
     */
    override fun updateStateBySub(subShapeView: BaseShapeView?) {
        super.updateStateBySub(subShapeView)
        subShapeView?.let {
            // 通过当前块和下一块的信息，推断出偏移量和哪条边出的

            /*计算出的绝对角度，相对于画布*/
            val calAngle = Utils.calAngle(centerPos, it.centerPos)
            val roundAngle = (calAngle / 30f).roundToInt() * 30 + 60
            var angleOffSet = (roundAngle - offsetRotation)
            angleOffSet = Utils.rangeRotation360(angleOffSet)

            nextDirectionTag = (angleOffSet / 60f).toInt()
            /*赋值 -- 上一个图形是从哪条边出的*/
            showLog(
                TAG,
                "updateStateBySub: calAngle = $calAngle roundAngle = $roundAngle  angleOffSet = $angleOffSet  + nextDirectionTag = $nextDirectionTag "
            )

            /*设置自身的Tag偏移量*/
            Constants.getNextShapeSupportOffset(
                nextDirectionTag,
                params.shapeType
            ).run {
                val angVal =
                    360 - Utils.rangeRotation360(roundAngle - 60 - it.offsetRotation)
                it.offsetTag = if (second?.first == angVal) {
                    second
                } else {
                    first
                }
            }
        }
    }
}