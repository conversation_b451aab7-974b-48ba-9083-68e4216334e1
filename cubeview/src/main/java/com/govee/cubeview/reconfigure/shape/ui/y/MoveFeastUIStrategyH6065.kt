package com.govee.cubeview.reconfigure.shape.ui.y

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.MoveFeastUIStrategy
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/12/9
 *     version : 1.0.0
 *     desc    :
 */
class MoveFeastUIStrategyH6065(shapeView: BaseShapeView) : MoveFeastUIStrategy(shapeView) {

    override fun updateState(params: BaseShapeParams, state: Int) {

        params.let {

            params.paintShapeBackground.color = shapeView.toColor(shapeView.moveFeastData.color)
            shapeView.moveFeastData.also { moveFeast ->
                if (moveFeast.isDrawCenterText()) {
                    it.paintCenterText.color =
                        shapeView.toColor(com.govee.ui.R.color.font_style_20_textColor)
                    it.centerText = shapeView.moveFeastData.centerText

                    it.yGuidelineColor[0] = com.govee.ui.R.color.ui_line_style_6_2_stroke_color
                    it.yGuidelineColor[1] = com.govee.ui.R.color.ui_line_style_6_2_stroke_color
                    it.yGuidelineColor[2] = com.govee.ui.R.color.ui_line_style_6_2_stroke_color
                    it.colorSheetMetal = com.govee.ui.R.color.ui_line_style_6_2_stroke_color

                } else if (moveFeast.isDrawCenterDrawable()) {
                    it.centerText = ""

                    it.yGuidelineColor[0] = com.govee.ui.R.color.ui_line_style_6_3_stroke_color
                    it.yGuidelineColor[1] = com.govee.ui.R.color.ui_line_style_6_3_stroke_color
                    it.yGuidelineColor[2] = com.govee.ui.R.color.ui_line_style_6_3_stroke_color
                    it.colorSheetMetal = com.govee.ui.R.color.ui_line_style_6_3_stroke_color
                }
            }

        }
    }
}