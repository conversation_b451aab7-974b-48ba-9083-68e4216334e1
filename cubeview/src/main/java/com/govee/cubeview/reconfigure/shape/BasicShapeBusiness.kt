package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.PointF
import com.govee.cubeview.CanvasLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.no
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout
import com.govee.cubeview.reconfigure.configuration.IConfig
import com.govee.cubeview.reconfigure.proxy.CanvasLayoutProxyV1
import com.govee.cubeview.reconfigure.shape.assist.BaseFocusView
import com.govee.cubeview.reconfigure.shape.assist.FocusDragView
import com.govee.cubeview.reconfigure.shape.assist.ImgAddShapeView
import com.govee.cubeview.reconfigure.shape.assist.ImgDelShapeView
import com.govee.cubeview.reconfigure.shape.assist.ImgPathShapeView
import com.govee.cubeview.reconfigure.shape.assist.ImgPowerShapeView
import com.govee.cubeview.reconfigure.shape.ui.IShapeM
import com.govee.cubeview.reconfigure.shape.ui.OnShapeListener
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.reconfigure.shape.ui.addAssistShapeCenterPoints
import com.govee.cubeview.reconfigure.shape.ui.multiPowerAssistShapeCenterPoints
import com.govee.cubeview.reconfigure.shape.ui.multiPowerPathRotation
import com.govee.cubeview.reconfigure.shape.ui.powerAssistShapeCenterPoints
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.view.PhysicalSizeView
import com.govee.cubeview.view.SelectBoxView
import com.govee.cubeview.yes
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2022/9/16
 *     version : 1.0.0
 *     desc    :
 *    图形基础业务类，处理：图形解析（基础图形），UI 刷新(更新辅助图形)
 */
abstract class BasicShapeBusiness(private val canvasLayout: BaseCanvasLayout) : IShapeM {

    protected val TAG = javaClass.simpleName.toString()

    protected val config: IConfig
        get() {
            return canvasLayout.cubeType.config
        }

    protected val context: Context = canvasLayout.context

    protected val mModeType: CanvasLayoutModeType
        get() {
            return canvasLayout.mModeType
        }
    protected val cubeType: PanelLampType
        get() {
            return canvasLayout.cubeType
        }

    private val canvasSize: Int
        get() {
            return canvasLayout.canvasSize
        }

    private var maxShapeCountHint = 0

    val shapeViews by lazy { mutableListOf<BaseShapeView>() }
    private val addViews by lazy { mutableListOf<ImgAddShapeView>() }
    private val deleteViews by lazy { mutableListOf<ImgDelShapeView>() }
    protected val powerViews by lazy { mutableListOf<ImgPowerShapeView>() }
    val pathViews by lazy { HashMap<BaseShapeView, ImgPathShapeView>() }


    protected val focusViews by lazy { mutableListOf<BaseFocusView>() }

    protected val powerViewPoints by lazy { mutableListOf<BaseShapeView>() }
    private var multiPowerView: ImgPowerShapeView? = null
    protected var multiPowerPathView: ImgPathShapeView? = null
    protected val pairMultiPowerList = mutableListOf<Pair<BaseShapeView, ImgPowerShapeView>>()

    protected var roundColor4PreViewBg: IntArray? = null

    protected val mSelectBoxView by lazy {
        SelectBoxView(context)
    }

    protected val mPhysicalSizeView by lazy {
        PhysicalSizeView(context)
    }

    open val shapeCenterPointF: PointF
        get() {
            return config.checkShapeCenterPointF(shapeViews)
        }

    /**
     * 用于计算图形效果中心点
     */
    open fun getShapeEffectCenterPoint(): PointF {
        return config.getShapeEffectCenterPoint(shapeViews)
    }

    protected val shapeViewsRange: FloatArray
        get() {
            return config.getShapeRange(shapeViews)
        }

    protected val simpleShapeViewsRange: FloatArray
        get() {
            var minX = 0f
            var maxX = 0f
            var minY = 0f
            var maxY = 0f
            shapeViews.apply {
                first().let {

                    it.getShapePoint4Canvas().onEach { shapePoint4Canvas ->
                        minX = shapePoint4Canvas.x
                        maxX = shapePoint4Canvas.x
                        minY = shapePoint4Canvas.y
                        maxY = shapePoint4Canvas.y
                    }
                }
            }.forEach {
                val self = true
                it.getShapePoint4Canvas(self).onEach { shapePoint ->
                    minX = min(shapePoint.x, minX)
                    maxX = max(shapePoint.x, maxX)
                    minY = min(shapePoint.y, minY)
                    maxY = max(shapePoint.y, maxY)
                }
            }
            return FloatArray(4).apply {
                this[0] = minX
                this[1] = maxX
                this[2] = minY
                this[3] = maxY
            }
        }

    /*图形初始缩放大小*/
    val mDefaultScale: Float
        get() {
            return shapeViewsRange.let {
                val shapeWidth = it[1] - it[0]
                val shapeHeight = it[3] - it[2]
                max(shapeWidth, shapeHeight) / canvasSize
            }
        }

    val mDefaultOffsetPoint: PointF
        get() {
            return shapeCenterPointF.let {
                PointF(-(it.x - canvasSize / 2), -(it.y - canvasSize / 2))
            }
        }


    private var mFocusDragViewScale: Float = 0f

    /*更新拖动布局的缩放比例*/
    fun updateDragViewScale(scale: Float) {
        if (scale <= 0) return
        focusViews
            .asSequence()
            .filterIsInstance<FocusDragView>()
            .firstNotNullOfOrNull {
                mFocusDragViewScale = scale
                it.scaleX = scale
                it.scaleY = scale
            }
    }

    private var lastShapeCenterPointF: PointF? = null

    private var shapeSerialNumber = 0/*图形序号*/

    protected var listener: OnShapeListener? = null


    override fun parseShapeData(shapePosition: ArrayList<ShapePosition>) {
        // 解析后，创建图形
        // 清空数据
        resetShape(false)
        config.inputBeforeConvertShapeData(shapePosition).onEachIndexed { index, position ->

            addShapeView(position) { baseShapeView ->
                // 确认上一块图形
                if (index != 0) {
                    position.lastPosition.takeIf { it < shapeViews.size }?.let {
                        shapeViews[it].updateStateBySub(baseShapeView)
                    }
                }
            }
        }
        //config.updateOperatedShapeViews(mModeType, shapeViews)
    }


    private fun addShapeView(
        shapePosition: ShapePosition, block: ((BaseShapeView) -> Unit)? = null
    ) {
        val type = shapePosition.type
        maxShapeCountHint = Constants.getShapeSupportMaxCount(type)
        /*根据类型创建不同的形状 view */
        config.createShapeView(context, type).apply {
            /*所有的图形旋转都是跟随其实点进行旋转的，所以在计算的时候,只需要考虑中心点和方向*/

            if (shapePosition.editable) {
                shapeSerialNumber++
            }
            initShapeData(ShapeData(shapePosition, mModeType, shapeSerialNumber))

            if (this is BusinessShapeView) {
                doSomething4ColorMode {
                    showLog(TAG, "addShapeView: onclick = $ shape")
                    isSelected = !isSelected
                    updateUIState()
                    // 通知 canvasLayout 当前图形的选择状态
                    val list = ArrayList<Boolean>().apply {
                        shapeViews.onEach {
                            add(it.isSelected)
                        }
                    }
                    listener?.selectedShape(list)
                }

                doSomething4MoveFeast {
                    doSomethingMoveFeast(this)
                }
            }

        }.also {
            canvasLayout.addView(it)
            shapeViews.add(it)
            block?.invoke(it)
        }
    }

    open fun doSomethingMoveFeast(baseShapeView: BusinessShapeView) {
    }

    override fun registerListener(listener: OnShapeListener) {
        this.listener = listener
    }

    fun notifyRefreshShapeUI() {
        notifyRefreshShapeUI(mModeType)
    }

    override fun notifyRefreshShapeUI(modeType: CanvasLayoutModeType) {
        /*清空当前辅助图形状态，再根据当前模式，重新生成新的辅助图形*/
        clearAssistViewState()
        showLog(TAG, "notifyRefreshShapeUI: modeType = $modeType ")
        if (modeType.checkPathView4Refresh()) {
            /*更新图形的拼接路径*/
            updatePathView()
        }

        if (modeType.checkPowerView4Refresh()) {
            /*更新电源键*/
            updatePowerView()
        }
        if (modeType.checkOperateView4Refresh()) {
            /*更新添加按钮*/
            updateDeleteView()
            /*更新删除按钮*/
            updateAddImgView()
        }


        if (modeType.checkFocusView4Refresh()) {
            /*更新拖拽聚焦item*/
            updateFocusView()

        }

        if (modeType.checkMultiPowerView4Refresh()) {
            updateMultiPowerView()
        }
        if (modeType.isGraffiti()) {
            // 是否是涂鸦功能
            updateGraffitiView()
        }
    }

    open fun updateGraffitiView() {

    }

    /**
     *  更新辅助电源视图
     *  <p>
     *  应用场景:多电源选择模式，安装模式，校验模式
     *  流程：1、判断当前是否可执行辅助电源流程
     *      2、判断是否显示可编辑区域,并且获取到默认的辅助电源图形
     *          a.超过规定数量之后的图形才考虑是否需要接电源，接电源的起始点在总数量的一半
     *          b.先确定哪个多边形可接入
     *          c.点击时判断那条边的 Y 轴最大，
     *          d.根据多边形的边确认新的电源方向在哪里
     *      3、获取当前是否已经选择了辅助电源(如：设备读取到的信息)，并且确认该电源信息是否符合当前图形(防止图形经过拼接、旋转、翻转等)
     *  </p>
     */
    open fun updateMultiPowerView() {
        val size = shapeViews.size
        if (size <= 0) return
        val shapeType = shapeViews.first().params.shapeType
        val multiPower = Constants.isMultiPower(size, shapeType)
        if (!multiPower) return

        val showMultiPowerEditSelected =
            mModeType == CanvasLayoutModeType.MultiPower/*只有辅助电源模式下，才需要显示可选择区域*/

        val showDefaultMultiPower = shapeViews.first().params.showDefaultMultiPower

        var currentSelectShapeView: BaseShapeView? = null/*当前已经有的辅助电源图形*/
        var defaultSelectShapeView: BaseShapeView? = null/*默认选择*/

        // 添加
        shapeViews.asSequence().filter { !it.mMultiPower.isDefault() }/*过滤没有选择辅助电源*/
            .firstNotNullOfOrNull { it.mMultiPower }/*在列表中选择第一个不为空的，若没有则返回 null*/?.let {
                val shapeView = shapeViews[(it.powerNumber - 1).coerceAtLeast(0).coerceAtMost(shapeViews.size - 1)]
                val nextShapeCenterPoints =
                    shapeView.multiPowerAssistShapeCenterPoints(it.powerEdgeNUmber)
                /*通过已知的辅助电源信息计算出坐标后，判断是否满足当前的可用区域范围*/
                if (checkAreaValid(nextShapeCenterPoints)) {
                    shapeView.params.isMultiPowerSelected = true
                    shapeView.mMultiPower.powerPointF.set(
                        nextShapeCenterPoints.x, nextShapeCenterPoints.y
                    )
                    currentSelectShapeView = shapeView
                }
            }

        if (showMultiPowerEditSelected) {
            val powerSize = config.fetchMultiPowerLimitIndex(size)
            shapeViews.onEach { it.updateUIState(state = 2) }.subList(powerSize, size)//获取后面可拼接的图形
                .forEachIndexed { shapeIndex, shapeView ->
                    var areaValid = false
                    var maxY = 0f
                    // 目前已经得到选择图形以及辅助电源的信息
                    shapeView.addAssistShapeCenterPoints().onEachIndexed { index, pointF ->
                        /* 确认哪个点有空余地方 */
                        if (checkAreaValid(pointF)) {
                            val directionTag = Constants.getInstallNumberWithDirectionTag2(
                                shapeView.params.shapeType, index
                            )

                            val optionalEdgeNumbers = shapeView.mMultiPower.optionalEdgeNumbers
                            optionalEdgeNumbers.add(directionTag)

                            // 多个辅助点可画，优先 Y 轴最大（存在相同 Y 轴的情况下，与 iOS 保持一致）
                            if (pointF.y > maxY) {
                                maxY = pointF.y
                                // 记录辅助坐标（这个地方得确认下是什么关系,跟方向有关系）
                                val powerNumber = powerSize + shapeIndex + 1

                                shapeView.mMultiPower.powerEdgeNUmber = directionTag
                                shapeView.mMultiPower.powerNumber = powerNumber
                                shapeView.mMultiPower.powerPointF.set(pointF.x, pointF.y)
                            }
                            areaValid = true
                        }
                    }

                    // 处理点击事件
                    areaValid.yes {
                        /*为了防止*/
                        shapeView.setOnClickListener {
                            updateSelectedMultiPowerView(shapeView)
                        }
                        shapeView.updateUIState(state = 0)
                        powerViewPoints.add(shapeView)
                    }.no {
                        // 不可用
                        shapeView.updateUIState(state = 2)
                    }
                }
            /* 根据 y 轴排序 从小到大*/
            powerViewPoints.sortBy { ceil(it.mMultiPower.powerPointF.y) }
            /*若最后一个图形可选，则默认选中 Y 轴最大的*/
            powerViewPoints.lastOrNull()?.let {
                defaultSelectShapeView = it
            }
        }

        /*若当前没有选择的辅助电源，则使用默认的*/
        currentSelectShapeView?.let { updateSelectedMultiPowerView(it, showMultiPowerEditSelected) }
            ?: also {
                if (showDefaultMultiPower) {
                    defaultSelectShapeView?.let {
                        updateSelectedMultiPowerView(it, true)
                    } ?: canvasLayout.removeView(multiPowerView)
                }
            }
    }

    /**
     * 更新选中辅助电源图形
     */
    private fun updateSelectedMultiPowerView(
        baseShapeView: BaseShapeView, hadResetUIState: Boolean = true
    ) {
        if (hadResetUIState) {
            shapeViews.forEach {
                /*把原来选中的切换到未选中状态*/
                if (it.shapeState.state == 1) {
                    it.updateUIState(0)
                }
            }
            baseShapeView.updateUIState(state = 1)
        }
        multiPowerView?.let { canvasLayout.removeView(it) }

        multiPowerView = config.createPowerView(context)
            .apply { setLayoutParams(baseShapeView.mMultiPower.powerPointF) }

        val showMultiPowerPath = mModeType.isInstallMode()
        if (showMultiPowerPath) {
            multiPowerPathView?.let { canvasLayout.removeView(it) }.run { multiPowerView }?.let {
                config.createPathView(context).apply {

                    val centerPoint = Utils.centerPoint(
                        baseShapeView.multiPowerPathRotation(), baseShapeView.centerPos
                    )
                    val calAngle = Utils.calAngle(centerPoint, baseShapeView.centerPos)

                    setLayoutParams(centerPoint)

                    setData(calAngle.toFloat(), -1)

                }.let {
                    canvasLayout.addView(it)
                    multiPowerPathView = it
                }
            }
        }

        /*最后才添加是为了防止被路径辅助线覆盖*/
        multiPowerView.let { canvasLayout.addView(it) }

        if (mModeType.isMultiPower()) {
            multiPowerView?.let {
                listener?.selectedMultiPower(baseShapeView.mMultiPower.powerNumber)
            }
        }
    }

    open fun updateFocusView() {
        if (shapeViews.isEmpty()) return
        config.createFocusView(context).apply {
            showLog(TAG, "updateFocusView: shapeCenterPointF = $shapeCenterPointF ")
            /*拖拽回调*/
            doSomething {
                // 普通图形
                focusDragCallback = { pointF: PointF ->
                    listener?.focusDragPointChange(pointF)
                }
            }
            setLayoutParams(shapeCenterPointF)
        }.let {
            focusViews.add(it)
            canvasLayout.addView(it)
        }
        updateDragViewScale(mFocusDragViewScale)
    }

    open fun updatePowerView() {
        if (shapeViews.isEmpty()) return
        if (config.ignorePowerView()) return
        config.createPowerView(context).apply {
            val first = shapeViews.first()
            val nextShapeCenterPoints = first.powerAssistShapeCenterPoints()
            setLayoutParams(nextShapeCenterPoints)
        }.let {
            powerViews.add(it)
            canvasLayout.addView(it)
        }
    }

    /**
     * 更新电源按钮，带清除
     */
    open fun updatePowerItemView() {
        if (shapeViews.isEmpty()) return
        if (config.ignorePowerView()) return
        config.createPowerView(context).apply {
            val first = shapeViews.first()
            val nextShapeCenterPoints = first.powerAssistShapeCenterPoints()
            setLayoutParams(nextShapeCenterPoints)
        }.let {
            powerViews.onEach { canvasLayout.removeView(it) }.clear()
            powerViews.add(it)
            canvasLayout.addView(it)
        }
    }

    protected fun updateDeleteView() {
        shapeViews.asSequence().filterIndexed { index, baseShapeView ->
            val result =
                if (index == 0) {//排除第一个
                    if (baseShapeView.params.shapeType == Shape.TYPE_SPACE_HEXAGON) {
                        if (baseShapeView.params.editable) {
                            //可编辑
                            baseShapeView.params.canDelState()
                        } else {
                            // 不可编辑
                            false
                        }
                    } else {
                        false
                    }
                } else {
                    if (!baseShapeView.params.editable) {/*排除不可编辑的*/
                        false
                    } else if (baseShapeView.params.isAllEdited) {/*若是图形定义为可全部编辑，需要看当前块的连接状态*/
                        baseShapeView.params.canDelState()
                    } else {
//                            index == shapeViews.lastIndex/*若图形定义为单操作，则判断是否最后一块*/
                        baseShapeView.params.canDelState()
                    }
                }
            result
        }.toList().onEach { baseShapeView ->
            showLog(TAG, "updateDeleteView ")
            val centerPos = baseShapeView.centerPos
            config.createDeleteView(context).apply {
                // 添加视图
                setLayoutParams(centerPos)

                doSomething {
                    // 点击事件,删除当前 view
                    showLog(TAG, "onclick delete view")
                    handleClickDelShapeView(baseShapeView)
                    notifyRefreshShapeUI()
                    listener?.graphQuantityChanges(shapeSerialNumber)
                }

            }.let {
                deleteViews.add(it)
                canvasLayout.addView(it)
            }
        }
    }

    protected fun handleClickDelShapeView(baseShapeView: BaseShapeView) {
        shapeViews.remove(baseShapeView)
        canvasLayout.removeView(baseShapeView)

        config.updateOperatedShapeViews(mModeType, shapeViews)
        baseShapeView.notifyParentConnectState()
        shapeSerialNumber--
    }

    protected fun updateAddImgView() {
        if (shapeViews.size >= maxShapeCountHint) return
        shapeViews.asSequence().filterIndexed { _, baseShapeView ->
            // true 就是符合条件
            // 根据图形不同的需求过滤
            val result = if (baseShapeView.params.isAllEdited) {//确认图形是否可全部编辑
                baseShapeView.params.canAddState() /*确认当前连接状态是否可编辑*/
            } else {
                baseShapeView.params.canAddState() /*确认当前连接状态是否可编辑*/
            }
            result
        }.toList().onEach { baseShapeView ->
            /*得到图形的顶点*/
            val shapePoint = baseShapeView.addAssistShapeCenterPoints()

            shapePoint.onEachIndexed { index, pointF ->
                // 也就是需要抽离方法，单独判断某个图形是否在可添加范围内,辅助图形使用 width，图形使用外切圆半径*2
                // 需要判断下一个图形的中心点是否在范围内
                if (!checkAreaValid(pointF)) {
                    return@onEachIndexed
                }

                handleAddView(baseShapeView, pointF, index)
            }
        }
    }

    private fun handleAddView(baseShapeView: BaseShapeView, pointF: PointF, index: Int) {

        config.createAddView(context).apply {

            setLayoutParams(pointF)

            doSomething {
                showLog(TAG, "onclick add index = $index")

                val nextShapePosition = baseShapeView.getNextShapePosition(index)

                showLog(TAG, "updateAddImgView: shapePosition = $nextShapePosition")

                addShapeView(nextShapePosition) {
                    // 表示当前已经添加完毕了
                    config.updateOperatedShapeViews(mModeType, shapeViews)
                    baseShapeView.updateStateBySub(it)
                }
                notifyRefreshShapeUI()
                listener?.graphQuantityChanges(shapeSerialNumber)
            }

        }.let {
            addViews.add(it)
            canvasLayout.addView(it)
        }
    }

    protected fun updatePathView() {
        var lastShapeView: BaseShapeView? = null
//        val hasDisAbleShape = shapeViews.any { !it.params.editable }
        shapeViews.forEachIndexed { _, baseShapeView ->
            /*创建 PathView*/
            config.createPathView(context).apply {
                doSomething {
                    lastShapeView.let {
                        baseShapeView.getPathEntity(lastShapeView)?.run {
//                            if (baseShapeView.params.editable) {
                            setLayoutParams(centerPointF)
                            setData(rotation, pathNum)
                            setData(this)
                            setAnimationPoint(startShapePoint, endShapePoint)
                            canvasLayout.addView(this@apply)
                            pathViews.put(baseShapeView, this@apply)
//                            pathViews.add(this@apply)
//                            showLog(
//                                TAG,
//                                "updatePathView: baseShapeView index = $index , pathViews = ${pathViews.size}"
//                            )
//                            }
                        }
                    }
//                        ?: let {
                    /*这个是为了与之前业务逻辑保持一致*/
                    /*Y形灯特殊逻辑处理，默认需要添加两个占位 pathView*/
                    /*若是加装逻辑，则只需要一个占位 pathView */
//                        pathViews.add(this)
//                        if (hasDisAbleShape) {
//                            pathViews.add(this)
//                        }
//                    }
                    if (pathViews.size >= 2) {
                        alpha = 0.4f
                    }
                }
                pathViews[baseShapeView] = this@apply
            }
            lastShapeView = baseShapeView
        }
    }


    /*获取到已占用的区域*/
    private fun getSelectedArea(): Array<FloatArray> {
        val arrayArea =
            Array(shapeViews.size + if (powerViews.isNotEmpty()) 1 else 0) { FloatArray(4) }
        for (i in shapeViews.size - 1 downTo 0) {
            arrayArea[i] = shapeViews[i].shapeArea4Parent()
        }
        val len = shapeViews.first().params.powerAreaLen
        /*加上电源位置*/
        powerViews.isNotEmpty().yes {
            powerViews.first().pos.apply {
                arrayArea[shapeViews.size] = floatArrayOf(
                    x - len, x + len, y - len, y + len
                )
            }
        }
        return arrayArea
    }

    /*检查点是否在有效区域内*/
    protected fun checkAreaValid(point: PointF): Boolean {
        val area = getSelectedArea()
        area.forEach {
            if (point.x >= it[0] && point.x <= it[1] && point.y >= it[2] && point.y <= it[3]) {
                return false
            }
        }
        /*是否在在画布范围内*/

        if (point.x - CanvasLayout.CANVAS_PADDING <= 0 || point.x + CanvasLayout.CANVAS_PADDING >= canvasSize || point.y - CanvasLayout.CANVAS_PADDING <= 0 || point.y + CanvasLayout.CANVAS_PADDING >= canvasSize) {
            return false
        }

        //点击位置x坐标与圆心的x坐标的距离
        val distanceX: Float = abs(canvasSize / 2 - point.x)
        //点击位置y坐标与圆心的y坐标的距离
        val distanceY: Float = abs(canvasSize / 2 - point.y)
        //点击位置与圆心的直线距离
        val distanceZ = sqrt(distanceX.toDouble().pow(2.0) + distanceY.toDouble().pow(2.0))

        //如果点击位置与圆心的距离大于圆的半径，证明点击位置没有在圆内
        if (distanceZ > canvasSize / 2 - CanvasLayout.CANVAS_PADDING) {
            return false
        }
        return true
    }

    override fun resetShape(reserve: Boolean) {
        lastShapeCenterPointF = null
        shapeSerialNumber = 0
        shapeViews.clear()
        clearAssistViewState()
        canvasLayout.removeAllViewsInLayout()
        if (reserve) {
            val generaDefaultShape = canvasLayout.cubeType.run {
                CanvasLayoutProxyV1.generaDefaultShape(shapeType)
            }
            addShapeView(generaDefaultShape) {
                config.updateOperatedShapeViews(mModeType, shapeViews)
            }
            listener?.graphQuantityChanges(shapeSerialNumber)
            notifyRefreshShapeUI()
            move2ShapeCenter()
        }
    }

    override fun move2ShapeCenter(animation: Boolean) {
        listener?.let {
            val viewsRange = shapeViewsRange
            val shapeWidth = abs(viewsRange[1] - viewsRange[0])
            val shapeHeight = abs(viewsRange[3] - viewsRange[2])

            val canvasCenterPoint = canvasSize / 2f
            /*整体相对于画布的拉伸*/
            val scale = max(shapeWidth, shapeHeight) / canvasSize
            val point = PointF(
                canvasCenterPoint - shapeCenterPointF.x, canvasCenterPoint - shapeCenterPointF.y
            )
            it.scaleAndTransition(scale, point, animation)
        }
    }


    /**
     * 清空当前辅助视图
     */
    protected fun clearAssistViewState() {
        addViews.onEach { canvasLayout.removeView(it) }.clear()
        deleteViews.onEach { canvasLayout.removeView(it) }.clear()
        powerViews.onEach { canvasLayout.removeView(it) }.clear()
        pathViews.values.onEach {
            it.clearAnimation()
            canvasLayout.removeView(it)
        }.clear()
        focusViews.onEach { canvasLayout.removeView(it) }.clear()
        multiPowerView?.let { canvasLayout.removeView(it) }
        multiPowerPathView?.let { canvasLayout.removeView(it) }
        powerViewPoints.clear()
        pairMultiPowerList.forEach { canvasLayout.removeView(it.second) }
    }

    override fun onDestroy() {
        listener = null
    }

}