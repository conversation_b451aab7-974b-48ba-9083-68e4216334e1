package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.CanvasLayout.Companion.TAG
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/11/7
 *     version : 1.0.0
 *     desc    : 辅助电源 UI 处理类
 */
class MultiPowerStrategy(val shapeView: BaseShapeView) : ShapeUIStrategy {
    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {

        /*该方法为了让不同状态下重新绘制 layoutParams，防止点击区域出现问题*/
        shapeView.let { it.setShapeLayoutParams(it.centerPos.x, it.centerPos.y) }

        with(params) {
            showLog(TAG, "updateState: state = $state ")
            /* 0:可选择；1：当前选中；2：不可选择 */
            when (state) {
                0 -> {
                    shapeView.alpha = 1f
                    shapeView.z = 0f
                    paintShapeBackground.color =
                        shapeView.toColor(multiPowerCanSelectColor)
                    paintBaseShape.color = shapeView.toColor(multiPowerCanSelectColorStroke)
                    isMultiPowerSelected = false
                }
                1 -> {
                    shapeView.alpha = 1f
                    /*
                    为了让 view 层置于其他 view 的上方，防止边框被吃掉
                    (view 绘制根据 paint 的 stroke 一半计算，所以有一半的边框宽度会被重合)
                    */
                    shapeView.z = 2f
                    paintShapeBackground.color =
                        shapeView.toColor(params.multiPowerSelectingColor)
                    paintBaseShape.color = shapeView.toColor(multiPowerSelectingColorStroke)
                    isMultiPowerSelected = true
                }
                2 -> {
                    shapeView.alpha = 0.4f
                    shapeView.z = 0f
                    paintShapeBackground.color =
                        shapeView.toColor(params.multiPowerUnSelectColor)
                    isMultiPowerSelected = false
                }
            }
        }
    }

    override fun getType(): Type = Type.MultiPower

}