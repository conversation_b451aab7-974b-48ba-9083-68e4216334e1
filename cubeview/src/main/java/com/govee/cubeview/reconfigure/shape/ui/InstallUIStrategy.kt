package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/9/30
 *     version : 1.0.0
 *     desc    :
 */
class InstallUIStrategy(private val shapeView: BaseShapeView) : ShapeUIStrategy {

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
//        showLog("", "updateState: state = $state ")
        params.let {
            when (state) {
                0 -> {
                    shapeView.alpha = 0.4f
                    it.paintBaseShape.color = shapeView.toColor(it.unInstalledStrokeColor)
                    it.paintShapeBackground.color = shapeView.toColor(it.unInstalledColor)
                }
                1 -> {
                    shapeView.alpha = 1f
                    it.paintBaseShape.color = shapeView.toColor(it.installingStrokeColor)
                    it.paintShapeBackground.color = shapeView.toColor(it.installingColor)
                }
                2 -> {
                    shapeView.alpha = 1f
                    it.paintBaseShape.color = shapeView.toColor(it.installedStrokeColor)
                    it.paintShapeBackground.color = shapeView.toColor(it.installedColor)
                }
            }
        }
    }


    override fun getType(): Type = Type.Install

}