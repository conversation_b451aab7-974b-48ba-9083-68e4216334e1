package com.govee.cubeview.reconfigure.shape.data

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.yes
import java.util.Stack

/**
 *     author  : sinrow
 *     time    : 2022/11/17
 *     version : 1.0.0
 *     desc    : Y 形灯数据整合，数据来源分为两部分：数据倒推图形、图形数据输出
 *               数据倒推图形：核心参数为 ShapePosition: index x y rotation
 *               图形数据输出：上一块的核心参数，标记在上一块的左右
 */

class YShapeTree {

    companion object {
        private const val TAG = "YShapeTree"
        private const val isDebug = false/*是否打印日志*/
    }

    private var root: YShapeNote? = null
    private var root4Ic: YShapeNote? = null

    data class YShapeNote(val shapePosition: ShapePosition) {
        var lastPosition: Int = 0
        var leftNode: YShapeNote? = null
        var rightNode: YShapeNote? = null
        var isLeft = false
        var id: Int = 0
        var isSub = true
        var nowShapeView: BaseShapeView? = null

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as YShapeNote

            if (shapePosition != other.shapePosition) return false
            if (lastPosition != other.lastPosition) return false
            if (leftNode != other.leftNode) return false
            if (rightNode != other.rightNode) return false
            if (isLeft != other.isLeft) return false
            if (id != other.id) return false
            if (isSub != other.isSub) return false
            if (nowShapeView != other.nowShapeView) return false

            return true
        }

        override fun hashCode(): Int {
            var result = shapePosition.hashCode()
            result = 31 * result + lastPosition
            result = 31 * result + (leftNode?.hashCode() ?: 0)
            result = 31 * result + (rightNode?.hashCode() ?: 0)
            result = 31 * result + isLeft.hashCode()
            result = 31 * result + id
            result = 31 * result + isSub.hashCode()
            result = 31 * result + (nowShapeView?.hashCode() ?: 0)
            return result
        }

    }

    fun reset() {
        root = null
        root4Ic = null
    }

    private val visible = mutableListOf<YShapeNote>()

    /**
     * 作用于倒推图形时，形成的树
     */
    fun insert(id: Int, shapePosition: ShapePosition) {
        insert4Ic(id, shapePosition)
        showLog("开始插入--- id = $id")
        val newNode = YShapeNote(shapePosition).apply { this.id = id }
        if (root == null) {
            root = newNode
            showLog("做为跟节点 newNode id = ${newNode.id} 上一个的 id = ${newNode.lastPosition}")
            return
        }
        val stack = Stack<YShapeNote>()
        stack.push(root)
        visible.clear()

        while (stack.isNotEmpty()) {

            val pop = stack.pop()

            when (pop.shapePosition.state) {
                0 -> {
                    // 走到尽头了
                    showLog("当前块没有 ${pop.id} ")
                }
                1 -> {
                    // 表示左边
                    if (pop.leftNode != null) {
                        stack.push(pop.leftNode)
                        showLog("只有左边 - 压栈 --- 左边")
                    } else {
                        showLog("只有左边 - newNode id = ${newNode.id}  插在 ${pop.id} 左边")
                        pop.leftNode = newNode
                        newNode.lastPosition = pop.id
                        pop.isLeft = true
                        stack.clear()
                    }
                }
                2 -> {
                    // 表示只有右边
                    if (pop.rightNode != null) {
                        showLog("只有右边 - 压栈 --- 右边")
                        stack.push(pop.rightNode)
                    } else {
                        showLog("只有右边 - newNode id = ${newNode.id} 插在 ${pop.id} 右边")
                        pop.rightNode = newNode
                        newNode.lastPosition = pop.id
                        pop.isLeft = false
                        stack.clear()
                    }
                }
                3 -> {
                    // 表示两条边都有
                    // 这里出问题了，左边插满，如何回右边呢

                    if (visible.contains(pop)) {
                        // 左边走完了，开始右边
                        if (pop.rightNode != null) {
                            stack.push(pop.rightNode)
                            showLog("两边都有 - 左边走完，走右边")
                        } else {
                            newNode.lastPosition = pop.id
                            pop.rightNode = newNode
                            showLog("两边都有 - 左边走完，右边没有插右边 id = ${newNode.id} 插在 ${pop.id} 右边")
                            pop.isLeft = false
                            stack.clear()
                        }
                    } else
                        if (pop.leftNode != null) {
                            stack.push(pop)
                            stack.push(pop.leftNode)
                            showLog("两边都有 - 先压当前 id = ${pop.id}，在压左边")
                            visible.add(pop)
                        } else {
                            newNode.lastPosition = pop.id
                            pop.leftNode = newNode
                            showLog("两边都有 - newNode id = ${newNode.id} 插在 ${pop.id} 左边")
                            pop.isLeft = true
                            stack.clear()
                        }
                }
            }
        }

    }

    private val visible4Ic = mutableListOf<YShapeNote>()

    /**
     * 根据倒推图形，提前形成作用于计算图形的IC序号的树
     */
    private fun insert4Ic(index: Int, shapePosition: ShapePosition) {
        //showLog(TAG, "insert4Ic: 进来 index = $index ")
        val parent = YShapeNote(shapePosition).apply {
            this.id = index
            this.isSub = false
        }
        val left = YShapeNote(shapePosition).apply { this.id = index }
        val right = YShapeNote(shapePosition).apply { this.id = index }

        parent.leftNode = left
        parent.rightNode = right

        if (root4Ic == null) {
            root4Ic = parent
            showLog(TAG, "insert4Ic:  跟节点--- ")
            return
        }
        visible4Ic.clear()
        val stack = Stack<YShapeNote>()
        stack.push(root4Ic)

        while (stack.isNotEmpty()) {
            val pop = stack.pop()
            val leftNode = pop.leftNode
            val rightNode = pop.rightNode

            when (pop.shapePosition.state) {
                0 -> {
                    // 走到尽头了
                    showLog("insert4Ic:  当前块没有 ${pop.id} ")
                }
                1 -> {
                    // 表示左边
                    if (leftNode != null) {
                        if (leftNode.leftNode != null) {
                            stack.push(leftNode.leftNode)
                            showLog("insert4Ic:  只有左边 - 压栈 --- 左边")
                        } else {
                            leftNode.leftNode = parent
                            showLog("insert4Ic:  只有左边 - newNode id = ${parent.id}  插在 ${pop.id} 左边")
                            pop.isLeft = true
                            stack.clear()
                        }
                    } else {
                        // 走到尽头了
                        showLog("insert4Ic:  当前块没有 ${pop.id} ")
                    }
                }
                2 -> {
                    // 表示只有右边
                    if (rightNode != null) {
                        if (rightNode.rightNode != null) {
                            showLog("insert4Ic:  只有右边 - 压栈 --- 右边")
                            stack.push(rightNode.rightNode)
                        } else {
                            rightNode.rightNode = parent
                            showLog("insert4Ic:  只有右边 - newNode id = ${parent.id} 插在 ${pop.id} 右边")
                            pop.isLeft = false
                            stack.clear()
                        }
                    } else {
                        // 走到尽头了
                        showLog("insert4Ic:  当前块没有 ${pop.id} ")
                    }
                }
                3 -> {
                    // 表示两条边都有
                    // 这里出问题了，左边插满，如何回右边呢

                    if (visible4Ic.contains(pop)) {
                        // 左边走完了，开始右边
                        if (rightNode?.rightNode != null) {
                            stack.push(rightNode.rightNode)
                            showLog("insert4Ic:  两边都有 - 左边走完，走右边")
                        } else {
                            if (rightNode == null) {
                                pop.rightNode = parent
                            } else {
                                rightNode.rightNode = parent
                            }
                            showLog("insert4Ic:  两边都有 - 左边走完，右边没有插右边 id = ${parent.id} 插在 ${pop.id} 右边")
                            pop.isLeft = false
                            stack.clear()
                        }
                    } else
                        if (leftNode?.leftNode == null) {
                            if (leftNode == null) {
                                pop.leftNode = parent
                            } else {
                                leftNode.leftNode = parent
                            }
                            showLog("insert4Ic:  两边都有 - newNode id = ${parent.id} 插在 ${pop.id} 左边")
                            stack.clear()
                        } else {
                            stack.push(pop)
                            stack.push(leftNode.leftNode)
                            visible4Ic.add(pop)
                            showLog("insert4Ic:  两边都有 - 先压当前 id = ${pop.id}，再压左边 id = ${leftNode.leftNode?.id}")
                        }
                }
            }
        }
    }

    /**
     * 插入方法。
     * <p>
     *     作用于拼接后，形成的新数据，需要根据树的中左右排列输出
     * </p>
     */
    fun insert(
        parentShapeView: BaseShapeView?,
        nowShapeView: BaseShapeView
    ) {
        val nowShapePosition = nowShapeView.toShapePosition()
        if (root == null) {
            root = YShapeNote(nowShapePosition).apply { this.nowShapeView = nowShapeView }
            showLog("insert:  = ")
        } else {
            if (parentShapeView == null) {
                showLog("insert: parentShapeView = null")
                return
            }
            val isLeft = parentShapeView.let {
                val yNote = it.note as YNote
                yNote.leftNote == nowShapeView
            }

            val stack = Stack<YShapeNote>()
            stack.push(root)// 压栈

            while (stack.isNotEmpty()) {
                val pop = stack.pop()// 抛第一个
                if (parentShapeView == pop.nowShapeView) {
                    showLog("insert: 找到了 isLeft = $isLeft")
                    YShapeNote(nowShapePosition)
                        .apply {
                            this.nowShapeView = nowShapeView
                            this.isLeft = isLeft
                        }.let {
                            if (isLeft) {
                                pop.leftNode = it
                            } else {
                                pop.rightNode = it
                            }
                        }
                } else {
                    showLog("insert: 不是继续找，压左右 ")
                    if (pop.leftNode != null) {
                        stack.push(pop.leftNode)
                    }
                    if (pop.rightNode != null) {
                        stack.push(pop.rightNode)
                    }
                }
            }
        }

    }

    /**
     * 根据 id 寻找上一块图形的 index
     * <p>
     *     作用于倒推图形时，输出上下图形的关联关系
     * </p>
     */
    fun findLastShape(id: Int): Int {
        if (root == null) {
            showLog("findLastShape --  空树")
            return id
        }
        val stack = Stack<YShapeNote>()
        stack.push(root)
        while (stack.isNotEmpty()) {
            val pop = stack.pop()
            if (pop.id == id) {
                showLog("findLastShape --  id = $id , 上一个的 id = ${pop.lastPosition}")
                return pop.lastPosition
            } else {
                if (pop.rightNode != null) {
                    stack.push(pop.rightNode)
                }
                if (pop.leftNode != null) {
                    stack.push(pop.leftNode)
                }
            }
        }
        showLog("findLastShape --  没找到")
        return id
    }

    /**
     * 遍历，输出对应块的 ic 序号
     */
    fun ergodic(shapeIndex: Int): MutableList<Int> {// index 查找
        // 目的，计算出每一块图形的边的 ic 序号，电源入口左边起 ，开始：0 4，8

        // 构建了下标为 id 的图形的二叉树，
        // 从 root 的左边遍历，然后开始计算右边的。
        // id 可以定位到在哪个节点中，这时，就可以遍历得知

        val mapShapeIndex = mapShapeIndex()
        return mapShapeIndex[shapeIndex] ?: mutableListOf()
    }


    /**
     * 计算当前树的 ic 排列顺序
     * <p>
     *     核心思路：记录每一条边的序号，按照左中右排序顺序。ic 从0开始，间隔 4。
     *     输出以图形序号为 key ，每一条边对应的 ic 形成Int 集合作为 value 的 HashMap 集合
     * </p>
     */
    private fun mapShapeIndex(): HashMap<Int, MutableList<Int>> {

        if (root4Ic == null) return HashMap()
        val list = mutableListOf<Int>()
        val stack = Stack<YShapeNote>()

        val visible = mutableListOf<YShapeNote>()
        stack.push(root4Ic)
        while (stack.isNotEmpty()) {
            val pop = stack.pop()
            val leftNode = pop.leftNode
            val rightNode = pop.rightNode
            if (!pop.isSub) {
                // 电源节点，左右必然不可能为空
                // 出现为空的情况只有，在两个灯板连接处的虚拟节点，该节点不作为排序
                // 记录电源节点的 id，记录当前电源节点已访问
                list.add(pop.id)
                visible.add(pop)
                showLog("ergodic -- 电源节点 ${pop.id}")

                if (rightNode != null) {
                    showLog("ergodic -- rightNode 重新压栈 ${rightNode.id}")
                    stack.push(rightNode)
                }

                // 将子节点放入栈中
                if (leftNode != null) {
                    showLog("ergodic -- leftNode 重新压栈 ${leftNode.id}")
                    stack.push(leftNode)
                }
            } else {
                // 不会存在左右两边都有值的情况，因为永远都是单边的
                if (visible.contains(pop)) {
                    list.add(pop.id)
                    showLog("ergodic -- 重新压栈 ${pop.id}")
                } else if ((pop.leftNode == null && pop.rightNode != null)) {
                    //访问此节点
                    showLog("ergodic --- 左边为空，右边不为空")
                    stack.push(pop)// 重新压栈，通过是否访问来往外抛出
                    visible.add(pop)
                    stack.push(pop.rightNode)
                } else if (pop.rightNode == null && pop.leftNode != null) {
                    // 左边为空，右边不为空
                    showLog("ergodic --- 左边为不空，右边为空")
                    stack.push(pop)
                    visible.add(pop)
                    stack.push(pop.leftNode)
                } else {
                    list.add(pop.id)
                    visible.add(pop)
                    showLog("ergodic --- 两者都为空  id = ${pop.id}, left = ${pop.leftNode} , right = ${pop.rightNode}")
                }
            }
        }

        val map = java.util.HashMap<Int, MutableList<Int>>()
        var icPosition = 0
        list.forEach {
            map[it]?.apply {
                add(icPosition)
            } ?: run {
                map.put(it, mutableListOf<Int>().apply {
                    add(icPosition)
                })
            }
            icPosition += 4
        }
        return map
    }

    private val resultList = arrayListOf<ShapePosition>()

    /**
     * 中左右遍历
     * <p>
     *     作用于添加图形后，按照需求输出的数据结构顺序
     * </p>
     */
    fun ergodicMid(): ArrayList<ShapePosition> {
        resultList.clear()
        if (root == null) return resultList

        val stack = Stack<YShapeNote>()
        stack.push(root)
        while (stack.isNotEmpty()) {
            val pop = stack.pop()// 弹出
            showLog("ergodicMid() 装进集合中---")
            resultList.add(pop.shapePosition)
            if (pop.rightNode != null) {
                showLog("ergodicMid() 压右边--")
                stack.push(pop.rightNode)
            }
            if (pop.leftNode != null) {
                showLog("ergodicMid() 压左边--")
                stack.push(pop.leftNode)
            }
        }
        return resultList
    }

    private fun showLog(content: String) {
        isDebug.yes {
            showLog(TAG, "showLog: content = $content ")
        }
    }
}