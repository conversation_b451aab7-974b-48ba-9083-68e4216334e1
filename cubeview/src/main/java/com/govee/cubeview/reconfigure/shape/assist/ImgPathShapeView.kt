package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.TypedValue
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.govee.cubeview.reconfigure.PathEntity

/**
 * Create by lvwen<PERSON><PERSON> on 2021/7/5 11:49
 * 路径图形
 */
open class ImgPathShapeView(context: Context) : BaseAssistView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 19//边长
        const val SIZE_HEIGHT = 46//边长

    }

    //中心点相对于父布局位置，不是真的图形的中心点，是映射成六边形的中心点
    protected var mRotation = 0f//方向，绝对角度
    var numText = 0//标号

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    open fun init() {
        inputImgView.maxWidth = sizeWith
        inputImgView.maxHeight = sizeHeight
        inputImgView.scaleType = ImageView.ScaleType.CENTER_INSIDE
        rotation = mRotation
        setInputImageResource(com.govee.ui.R.mipmap.new_light_6061_pics_buxian)
    }


    open fun setData(angle: Float, numText: Int) {
        this.mRotation = angle
        this.numText = numText
        rotation = mRotation

        if (numText == -1) {
            setInputImageResource(com.govee.ui.R.mipmap.new_light_6061_pics_buxian_start)
        }
//        invalidate()
    }

    open fun setData(pathEntity: PathEntity) {
        pathEntity.let {
            this.mRotation = it.rotation
            this.numText = it.pathNum
            rotation = mRotation
            if (numText == -1) {
                setInputImageResource(com.govee.ui.R.mipmap.new_light_6061_pics_buxian_start)
            } else {
                showNumView(numText)
            }
        }
    }

    private fun showNumView(numText: Int) {
        centerTextView.let { textView ->
            textView.text = numText.toString()
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, 15f)
            textView.gravity = TEXT_ALIGNMENT_CENTER
            textView.setTextColor(ContextCompat.getColor(context, com.govee.ui.R.color.white))

            val makeMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            textView.measure(makeMeasureSpec, makeMeasureSpec)
            val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            val left = SIZE_WIDTH / 2 - textView.measuredWidth / 2
            val top = SIZE_HEIGHT / 2 + 2
            lp.leftMargin = left
            lp.topMargin = top
            textView.layoutParams = lp

            addView(textView)
        }
    }

    open fun change2InstallingState() {
        this.alpha = 1f
        invalidate()
    }

    open fun change2InstalledState() {
        this.alpha = 1f
        invalidate()
    }

    open fun change2UnInstalledState() {
        this.alpha = 0.4f
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (numText != -1) {
            //drawNumText(canvas)
        }
    }

    private fun drawNumText(canvas: Canvas) {
        val textPaint = Paint()
        textPaint.color = Color.parseColor("#ffffff")
        textPaint.textSize = 15f
        textPaint.style = Paint.Style.FILL
        //该方法即为设置基线上那个点究竟是left,center,还是right  这里我设置为center
        textPaint.textAlign = Paint.Align.CENTER

        val fontMetrics = textPaint.fontMetrics
        val top = fontMetrics.top //为基线到字体上边框的距离,即上图中的top
        val bottom = fontMetrics.bottom //为基线到字体下边框的距离,即上图中的bottom
        val textHeight = bottom - top //文本的高度，用于计算旋转中心
        val baseLineY = (height).toFloat() - bottom //基线中间点的y轴计算公式

        /*根据文本的中点进行旋转*/
        canvas.save()
        canvas.rotate(-mRotation, width / 2f, height - textHeight / 2f)
        canvas.drawText("$numText", width / 2f, baseLineY, textPaint)
        canvas.restore()
    }

}