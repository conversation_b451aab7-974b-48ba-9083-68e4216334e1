package com.govee.cubeview.reconfigure.shape.ui.y

import com.govee.cubeview.isLightMode
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.toColor
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/11/17
 *     version : 1.0.0
 *     desc    : Y 形灯安装模式模式的 UI 处理类
 *     <p>
 *         @see InstallUIStrategyH6065
 *     </p>
 */
class InstallUIStrategyH6065(private val shapeView: BaseShapeView) : ShapeUIStrategy {
    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        params.let {
            when (state) {
                0 -> {
                    shapeView.alpha = 0.4f
                }
                1 -> {
                    it.colorBottomEdge = R.color.ui_line_style_4_2_stroke_color
                    shapeView.alpha = 1f
                }
                2 -> {
                    val color =
                        if (shapeView.context.isLightMode()) R.color.FFB2E2F3 else R.color.FF204653
                    it.colorProjection = color
                    it.colorBottomEdge = color
                    shapeView.alpha = 1f
                }
            }
            val toColor = shapeView.toColor(it.colorBottomEdge)
            it.paintShapeBackground.color = toColor
            it.yGuidelineColor[0] = it.colorGuideLight
            it.yGuidelineColor[1] = it.colorGuideLight
            it.yGuidelineColor[2] = it.colorGuideLight
            it.yProjectionColor[0] = it.colorProjection
            it.yProjectionColor[1] = it.colorProjection
            it.yProjectionColor[2] = it.colorProjection
        }
    }

    override fun getType(): Type {
        return Type.Install
    }
}