package com.govee.cubeview.reconfigure.shape.ui

import android.graphics.Color
import android.graphics.CornerPathEffect
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.Constants
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.configuration.SquareConfig
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.assist.ImgPowerShapeView
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2022/9/7
 *     version : 1.0.0
 *     desc    :
 *     <p>
 *         图形基础参数，可配置不同参数
 *     </p>
 */

open class BaseShapeParams {

    // ----------------------------- 可变默认参数  --------------------------------

    var shapeType: Int = Shape.TYPE_DEFAULT/*图形类型*/

    var lineLength: Float = 40f/*图形边的长度*/

    var strokeWidth: Float = 2f/*图形边的宽度*/

    var colorModeStrokeWidth: Float = 5f/*颜色模式下图形的边宽度*/

    var attributesStrokeWidth: Float = 1.5f/*图形的其他属性的边宽度,如立体中间的 Y 形状*/

    var addImgRotationOffset = 30/*辅助(添加)图形偏移角度*/

    var powerImgRotationOffset = 180/*辅助(电源)图形偏移角度*/

    var colorTextSize = 16f/*颜色模式下的字体大小*/

    var defaultTextSize = 20.8f/*普通模式下的字体大小*/

    val defaultText = ""/*默认文本*/

    var centerText: String = defaultText/*中间文本*/

    var visibleText = true /*默认支持*/

    var textBgRadius = 12f/*文本圆角半径*/

    var powerAreaLen = 40f

    /**
     * 不同状态使用不同的 Paint
     */
    val paintBaseShape by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintShapeBackground by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintAttributes by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintCenterText by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintCenterTextBg by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintCenterDrawable by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    val paintSelectedBorder by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }


    // ----------------------------- 颜色参数  --------------------------------

    val showNearLowBlueColor: Int = com.govee.ui.R.color.ui_color_block_style_16_3_stroke_near_blue
    val showNearHighBlueColor: Int = com.govee.ui.R.color.ui_color_block_style_16_3_stroke
    val defaultShapeColor: Int = com.govee.ui.R.color.ui_color_block_style_16_1_color
    val baseShapeStrokeColor: Int = com.govee.ui.R.color.ui_color_block_style_16_1_stroke
    val defaultCenterTextColor: Int = com.govee.ui.R.color.font_style_185_1_textColor
    var textColor: Int = defaultCenterTextColor
    var shapeColor: Int = -1
    var attributesStrokeColor: Int = com.govee.ui.R.color.ui_line_style_1_1_stroke_color

    /*安装*/
    var installedColor: Int = com.govee.ui.R.color.ui_color_block_style_16_5_color
    var installedStrokeColor: Int = com.govee.ui.R.color.ui_color_block_style_16_5_stroke

    val unInstalledColor: Int = com.govee.ui.R.color.ui_line_style_1_1_stroke_color
    val unInstalledStrokeColor: Int = com.govee.ui.R.color.ui_color_block_style_16_1_stroke

    var installingColor: Int = com.govee.ui.R.color.ui_color_block_style_16_4_color
    var installingStrokeColor: Int = com.govee.ui.R.color.ui_color_block_style_16_4_stroke

    /*安装模式下中心文本颜色 - 6066*/
    val installedCenterTextColor: Int = com.govee.ui.R.color.font_style_226_1_textColor
    val installingCenterTextColor: Int = com.govee.ui.R.color.font_style_226_2_textColor

    val installInputCenterTextBgColor: Int = com.govee.ui.R.color.ui_point_style_1
    val installOutputCenterTextBgColor: Int = com.govee.ui.R.color.ui_point_style_15_color

    /*校准*/
    val checkedColor = com.govee.ui.R.color.ui_color_block_style_16_1_color
    val checkedStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_1_stroke

    var checkingColor = Color.GREEN
    var checkingStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_1_stroke

    var unCheckColor = com.govee.ui.R.color.ui_color_block_style_16_2_color
    val unCheckColorStroke = com.govee.ui.R.color.ui_color_block_style_16_2_stroke

    /*辅助电源模式*/
    var multiPowerUnSelectColor = com.govee.ui.R.color.ui_color_block_style_16_1_color

    var multiPowerSelectingColor = com.govee.ui.R.color.ui_color_block_style_16_6_color
    var multiPowerSelectingColorStroke = com.govee.ui.R.color.ui_color_block_style_16_6_stroke

    var multiPowerCanSelectColor = com.govee.ui.R.color.ui_color_block_style_16_1_color
    var multiPowerCanSelectColorStroke = com.govee.ui.R.color.ui_color_block_style_16_1_stroke

    /*预览模式*/
    val preViewColors by lazy {
        IntArray(5).apply {
            this[0] = Color.RED
            this[1] = Color.BLUE
            this[2] = Color.YELLOW
            this[3] = Color.YELLOW
            this[4] = Color.YELLOW
        }
    }

    /*   以下是 Y 形灯特殊颜色参数 */

    var colorProjection = com.govee.ui.R.color.ui_line_style_4_1_stroke_color/*投影*/
    var colorBottomEdge = com.govee.ui.R.color.ui_line_style_4_2_stroke_color/*底部*/
    var colorSheetMetal = com.govee.ui.R.color.ui_line_style_4_3_stroke_color/*金属片*/
    var colorGuideLight = com.govee.ui.R.color.ui_line_style_4_4_stroke_color/*导光线*/

    val yProjectionColor by lazy {/*之所以分三块是为了后续定义不同颜色*/
        IntArray(3).apply {
            this[0] = colorProjection
            this[1] = colorProjection
            this[2] = colorProjection
        }
    }
    val yGuidelineColor by lazy {
        IntArray(3)
    }

    /*606A 安装状态颜色*/
    var colorInputTextBg = installInputCenterTextBgColor
    var colorOutputTextBg = installOutputCenterTextBgColor

    // ----------------------------- 状态参数  --------------------------------

    var isAllEdited = false/*图形是否可全部编辑*/

    var nextCenterByInnerLength = true/*是否用内切圆/外切圆半径，计算下一个图形的中心坐标,默认用内切圆半径*/

    var handleCenterText4ColorMode: Boolean = true/*颜色模式下，是否对文本内容做颜色近似处理*/

    var isMultiPowerSelected = false/*是否为辅助电源连接图形*/

    var isMainPower: Boolean = false/*是否未主电源*/

    var editable = false/*是否可编辑*/

    var showDefaultMultiPower = true/* 是否显示默认辅助电源*/

    /**
     *  图形出边的连接状态（指当前图形是否作为连接器，连接下一个图形）数字的含义由不同图形确定
     * <p>
     *  默认 connectState = 0 表示当前图形未连接
     * </p>
     * */
    var connectState: Int = 0

    var inputNum: Int = 0
    var inputNumTemp: Int = 0

    var outputNum: Int = 0

    /**
     * 当前图形是否可添加
     */
    open fun canAddState(): Boolean {
        return connectState == 0
    }

    /**
     * 当前图形是否可删除
     */
    open fun canDelState(): Boolean {
        return canAddState()
    }

    // ----------------------------- 可变常量参数  --------------------------------

    /**
     * 计算下一个图形中心点的距离
     */
    val nextShapeCenterLineLength: Float
        get() {
            return if (nextCenterByInnerLength) {
                innerLineLength
            } else {
                outsideLineLength
            }
        }

    /**
     * 用于计算相对正多边形其他图形的坐标
     * 如：加号、电源、下一个中心坐标
     */
    val innerLineLength: Float
        get() {
            return Shape.getShapeInnerRadius(
                lineLength,
                shapeType,
                strokeWidth
            )
        }

    /**
     * 用于计算多边形顶点坐标
     */
    val outsideLineLength: Float
        get() {
            return Shape.getShapeOutsideRadius(
                lineLength,
                shapeType,
            )
        }

    /**
     * 图形默认角度
     */
    val defaultAngle: Float
        get() {
            return Constants.getShapeDefaultAngle(shapeType).toFloat()
        }

}

/**
 * 三角形图形配置表
 */
internal class TriangleShapeParams : BaseShapeParams() {
    init {
        shapeType = Shape.TYPE_TRIANGLE
        lineLength = 90f
        addImgRotationOffset = 60
        powerImgRotationOffset = 180 // 三角形顶点相反方向
    }
}

/**
 * Y形灯图形配置表
 */
internal class YShapeParams : BaseShapeParams() {
    init {
        lineLength = 56 * sqrt(3f)
        shapeType = Shape.TYPE_Y
        addImgRotationOffset = 240
        powerImgRotationOffset = 0
        nextCenterByInnerLength = false
        isAllEdited = true
        textBgRadius = 15f
        handleCenterText4ColorMode = false
        powerAreaLen = 90f
    }

    override fun canAddState(): Boolean {
        return connectState != 3
    }

    override fun canDelState(): Boolean {
        return connectState == 0
    }
}

/**
 * 六边形图形配置表
 */
internal class HexagonParams : BaseShapeParams() {
    init {
        shapeType = Shape.TYPE_HEXAGON
    }
}

/**
 * 立体六边形图形配置表
 */
class HexagonSolidParams : BaseShapeParams() {
    init {
        shapeType = Shape.TYPE_SOLID_HEXAGON
        checkingColor = Color.parseColor("#4DD248")
        unCheckColor = com.govee.ui.R.color.ui_color_block_style_16_1_color

        installedColor = com.govee.ui.R.color.ui_bg_style_19 /*用样式表表示白色*/
        installedStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke

        installingColor = com.govee.ui.R.color.ui_bg_style_19
        installingStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke
        attributesStrokeWidth = 2f
    }
}

/**
 * 空间六边形图形配置表
 */
class SpaceHexagonParams : BaseShapeParams() {
    init {
        isAllEdited = true
        shapeType = Shape.TYPE_SPACE_HEXAGON
        checkingColor = Color.parseColor("#4DD248")
        unCheckColor = com.govee.ui.R.color.ui_color_block_style_16_1_color

        installedColor = com.govee.ui.R.color.ui_bg_style_19 /*用样式表表示白色*/
        installedStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke

        //installingColor = com.govee.ui.R.color.ui_bg_style_19
        installingStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke
        attributesStrokeWidth = 2f
        lineLength = 46f
        defaultTextSize = 15.8f/*普通模式下的字体大小*/
        showDefaultMultiPower = false
    }

    /**
     *  该方法表示此方块是否可以被添加
     *  如果在此范围内，则认为可添加 0 ：当前一条边都没添加，63 ：表示已经添加完了
     *  具体看蓝牙协议
     *      1 byte-> 8 bit
     *      最多前六位故而 00111111 == 63
     *    @since canDelState() 同理
     */
    override fun canAddState(): Boolean {
        val total = inputNum + outputNum
        return total in 0..63
    }

    override fun canDelState(): Boolean {
        return connectState == 0
    }

    var showInputTag = false
    var showOutputTag = false
}


/**
 * 游戏墙灯图形配置表
 */
class GameWallParams : BaseShapeParams() {
    init {
        shapeType = Shape.TYPE_SPACE_HEXAGON
        checkingColor = Color.parseColor("#4DD248")
        unCheckColor = com.govee.ui.R.color.ui_color_block_style_16_1_color

        installedColor = com.govee.ui.R.color.ui_bg_style_19 /*用样式表表示白色*/
        installedStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke

        installingColor = com.govee.ui.R.color.ui_bg_style_19
        installingStrokeColor = com.govee.ui.R.color.ui_color_block_style_16_4_stroke
        attributesStrokeWidth = 2f
        colorModeStrokeWidth = 2f
    }

    /**
     *  该方法表示此方块是否可以被添加
     *  如果在此范围内，则认为可添加 0 ：当前一条边都没添加，63 ：表示已经添加完了
     *  具体看蓝牙协议
     *      1 byte-> 8 bit
     *      最多前六位故而 00111111 == 63
     *    @since canDelState() 同理
     */
    override fun canAddState(): Boolean {
        val total = inputNum + outputNum
        return total in 0..63
    }

    override fun canDelState(): Boolean {
        return connectState == 0
    }

    var showInputTag = false
    var showOutputTag = false
}

/**
 * 正方形灯块配置表
 */
class SquareParams : BaseShapeParams() {

    init {
        lineLength = SquareConfig.lineLength
        addImgRotationOffset = 45
        powerImgRotationOffset = 270
        shapeType = Shape.TYPE_SQUARE
        isAllEdited = true
        val cornerPathEffect = CornerPathEffect(5f)
        paintBaseShape.setPathEffect(cornerPathEffect)
        paintShapeBackground.setPathEffect(cornerPathEffect)
        paintSelectedBorder.setPathEffect(cornerPathEffect)
    }

    override fun canAddState(): Boolean {
        val total = inputNum + outputNum
        return total in 0..63
    }

    override fun canDelState(): Boolean {
        return connectState == 0
    }

    var showInputTag = false
    var showOutputTag = false
}

/**
 * 获取当前图形的辅助添加图形的中心位置
 */
internal fun BaseShapeView.addAssistShapeCenterPoints(): Array<PointF> {
    val addAssistShapeCenterLength = getAddAssistShapeCenterLength()
    return getShapePoint(
        centerPos,
        addAssistShapeCenterLength,
        offsetRotation + params.addImgRotationOffset, false
    )
}

/**
 * 该方法用于计算电源图形所在的位置
 * @param radius 下一个图形的位置距离
 */
internal fun BaseShapeView.powerAssistShapeCenterPoints(radius: Int = ImgPowerShapeView.SIZE_WIDTH / 2): PointF {
    return getNextShapeCenterPoints(
        offsetRotation + params.powerImgRotationOffset,
        params.nextShapeCenterLineLength + radius
    )
}

/**
 * 该方法用于计算辅助电源图形所在的位置
 */
internal fun BaseShapeView.multiPowerAssistShapeCenterPoints(
    edgeTag: Int,
    otherLength: Int = ImgPowerShapeView.SIZE_WIDTH / 2
): PointF {
    return edgeTag
        .let { Constants.getInstallNumberWithDirectionTag3(params.shapeType, it) }
        .let { getRotationByDirectionTag(it) + offsetRotation }
        .let {
            getNextShapeCenterPoints(
                it,
                nextLineLength = params.nextShapeCenterLineLength + otherLength
            )
        }
}

/**
 * 计算辅助电源的路径图形
 */
internal fun BaseShapeView.multiPowerPathRotation(): PointF {
    return mMultiPower.powerEdgeNUmber
        .let { Constants.getInstallNumberWithDirectionTag3(params.shapeType, it) }
        .let { getRotationByDirectionTag(it) }
        .let {
            getNextShapeCenterPoints(
                offsetRotation + it,
                params.innerLineLength + ImgPowerShapeView.SIZE_WIDTH
            )
        }
}


/**
 * 数组转换
 */
internal fun Array<PointF>.toSetFloatArray(arrayList: Array<FloatArray>) {
    forEachIndexed { index, pointF ->
        val floats = arrayList[index]
        pointF.x = floats[0]
        pointF.y = floats[1]
    }
}

/**
 * <p>
 *     便于后续可添加其他参数
 * </p>
 */
data class ShapeData(
    val shapePosition: ShapePosition,
    val modeType: CanvasLayoutModeType,
    val serialNumber: Int
) {
    var colorDefaultText = "0%"
}
