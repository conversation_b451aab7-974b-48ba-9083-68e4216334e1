package com.govee.cubeview.reconfigure

import android.graphics.PointF

/**
 *     author  : sinrow
 *     time    : 2022/11/4
 *     version : 1.0.0
 *     desc    : 实体类
 */


/**
 * 路径实体类
 * @param pathNum 安装路径序号
 * @param rotation 旋转角度
 * @param centerPointF 中心坐标
 */
class PathEntity {
    var pathNum: Int = 0
    var rotation: Float = 0f
    var centerPointF: PointF = PointF()
    var startShapePoint = PointF()
    var endShapePoint = PointF()
    var input = mutableListOf<Pair<Float, PointF>>()// 多边出。first：边序号  second：旋转角度
    var outputs = mutableListOf<Pair<Float, PointF>>()// 多边出。first：边序号  second：旋转角度
}