package com.govee.cubeview.reconfigure.shape.ui.y

import android.content.Context
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2023/5/22
 *     version : 1.0.0
 *     desc    :
 */
class PreViewUIStrategyH6065(context: Context) : NormalUIStrategyH6065(context) {


    override fun updateState(params: BaseShapeParams, state: Int) {
        params.let {
            val toColor = context.toColor(it.colorBottomEdge)
            it.paintShapeBackground.color = toColor
            it.yGuidelineColor[0] = it.colorGuideLight
            it.yGuidelineColor[1] = it.colorGuideLight
            it.yGuidelineColor[2] = it.colorGuideLight
        }
    }

    override fun getType(): Type = Type.Preview
}