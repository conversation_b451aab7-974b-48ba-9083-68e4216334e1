package com.govee.cubeview.reconfigure.proxy

import android.graphics.PointF
import com.govee.cubeview.CanvasLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.log
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.BusinessShapeView
import com.govee.cubeview.reconfigure.shape.assist.FocusPointView
import com.govee.cubeview.reconfigure.shape.ui.multiPowerAssistShapeCenterPoints
import com.govee.cubeview.shape.FocusPoint
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.showLog

/**
 *     author  : sinrow
 *     time    : 2023/12/14
 *     version : 1.0.0
 *     desc    :
 */
class CanvasLayoutH6063(canvasLayout: BaseCanvasLayout) : CanvasLayoutProxyV1(canvasLayout = canvasLayout) {

    override fun updateMultiPowerView() {
        pairMultiPowerList.clear()
        val size = shapeViews.size
        if (size <= 0) return
        val shapeType = shapeViews.first().params.shapeType
        val powerBoundary = Constants.getShapePowerBoundary(shapeType)
        // 大于 12 块就要选择双电源（主、辅）
        var selectNumber = 2
        if (size <= powerBoundary) {
            selectNumber = 1
        }
        powerViewPoints.clear()
        var shapeView1: BaseShapeView? = null/*当前选择的电源图形1 */
        var shapeView2: BaseShapeView? = null/*当前选择的电源图形2 */
        shapeViews
            .filter { (!it.mMultiPower.isDefault() || !it.mainPower.isDefault()) }
            .takeIf { it.isNotEmpty() }
            ?.let {
                it.forEach { baseShapeView ->
                    // 遍历得到当前哪块已经标记了电源信息，并且此信息中，是在范围内，则记录起来。
                    baseShapeView.getAvailMultiPower()?.let { multiPower ->
                        val assistShapeCenterPoints = baseShapeView.multiPowerAssistShapeCenterPoints(multiPower.powerEdgeNUmber)
                        if (checkAreaValid(assistShapeCenterPoints)) {
                            // !baseShapeView.params.isMainPower
                            // 标记此电源可用,赋值电源坐标信息
                            baseShapeView.params.isMultiPowerSelected = true
                            multiPower.powerPointF.set(assistShapeCenterPoints.x, assistShapeCenterPoints.y)
                            powerViewPoints.add(baseShapeView)
                        }
                    }
                }
                powerViewPoints.takeIf { it.isNotEmpty() }?.let { hasPowerShapeView ->
                    showLog(TAG, "updateMultiPowerView() hasPowerShapeView size = ${hasPowerShapeView.size}")
                    if (hasPowerShapeView.size >= 2) {
                        shapeView1 = hasPowerShapeView[0]
                        shapeView2 = hasPowerShapeView[1]
                    } else {
                        shapeView1 = hasPowerShapeView.lastOrNull()
                    }
                }
            }
        showLog(TAG, "updateMultiPowerView() shapeView1 = ${shapeView1.hashCode()}")
        showLog(TAG, "updateMultiPowerView() shapeView2 = ${shapeView2.hashCode()}")

        shapeView1?.let {
            showMultiPowerView(it)
        }
        shapeView2?.let {
            showMultiPowerView(it)
        }
    }

    private fun showMultiPowerView(baseShapeView: BaseShapeView) {
        showLog(TAG, "showMultiPowerView()")
        baseShapeView.getAvailMultiPower()?.let { multiPower ->
            config.createPowerView(context).apply {
                setLayoutParams(multiPower.powerPointF)
                showLog(TAG, "updateSelectedMultiPowerView1() 添加的 power 对象 = ${baseShapeView.mMultiPower.powerPointF}")
                canvasLayout.addView(this)
                powerViews.add(this)
                pairMultiPowerList.add(Pair(baseShapeView, this))
            }
        }
    }

    override fun checkFocusPointList(): MutableList<FocusPoint> {
        return mutableListOf<FocusPoint>().apply {
            if (cubeType == PanelLampType.GAME_WALL) {
                // 游戏灯条
                shapeViews.forEachIndexed { index, baseShapeView ->
                    // 添加中心点
                    baseShapeView.sortIndex = index
                    baseShapeView.centerPos.run {
                        add(FocusPoint(x, y, shapeViews.size, index, 0x02, 0x02, true))
                    }
                    //2、得到顶点坐标集合
                    if (baseShapeView.params.shapeType == Shape.TYPE_GAME_RECTANGLE) {
                        val shapePoint = baseShapeView.getShapePoint(
                            baseShapeView.centerPos,
                            baseShapeView.params.outsideLineLength,
                            baseShapeView.offsetRotation,
                            false
                        )
                        // 这里计算出清空
                        // 2、如果上一块图形非长条，则第一条边的点则不显示
                        // 3、如果出边不为0则,则需要判断下一个图形
                        baseShapeView.params.inputNum
                        shapePoint
                            .forEachIndexed { pointFIndex, floats ->
                                showLog("pointFIndex = $pointFIndex")
                                val tempIndex = pointFIndex + 1
                                if (pointFIndex < 2) {
                                    add(
                                        FocusPoint(
                                            floats.x, floats.y, shapeViews.size, index, tempIndex,
                                            /*默认值，这个参数是指，当前这点边沿着中心点的方向从0...3，默认参数:0xff*/
                                            /*但是由于 5.2.0的 UI 效果是两个块中间，需要发 0xff*/
                                            0xFF
                                        )
                                    )
                                }
                            }
                        baseShapeView.note.parentNote?.let {
                            // 上一个图形
                            showLog(
                                TAG,
                                "checkFocusPointList() 当前 $index , 上一个图形的类型 ${it.params.shapeType} , sortIndex = ${it.sortIndex}"
                            )
                            if (it.params.shapeType != Shape.TYPE_GAME_RECTANGLE) {
                                removeAt(size - 2)// 移除第一点
                            }
                        }
                        if (baseShapeView.params.outputNum != 0) {
                            // 有出边，移除第二点
                            removeLastOrNull()
                        }
                    }
                }
            }
        }
    }

    override fun updateFocusView() {
        mFocusPointList.onEach {
            config.createFocusView(context).apply {
                if (this is FocusPointView) {
                    /*赋值给到 view*/
                    focusPointMsg.apply {
                        shapeTotalQuantity = it.shapeTotalQuantity
                        selectShapeIndex = it.selectShapeIndex
                        selectEdgeIndex = it.selectEdgeIndex
                        selectEdgeIcIndex = it.selectEdgeIcIndex
                        isCenterPoint = it.isCenterPoint
                    }
                }
                doSomething {
                    // Y 形灯
                    focusPointCallback = {
                        val params = byteArrayOf(
                            it.selectShapeIndex.toByte(),
                            it.selectEdgeIndex.toByte(),
                            0x00, 0x00
                        )
                        listener?.selectedFocusPoint(params)
                        resetFocusPointStatus(this)
                    }
                }
                setLayoutParams(PointF(it.pointX, it.pointY))
            }.let {
                canvasLayout.addView(it)
                focusViews.add(it)
            }
        }
    }

    override fun getYShapeDefaultFocusPoint(): ByteArray {
        log(CanvasLayout.TAG, "getCurrentFocusPoint4YShape()")
        val yShapeCenterFocusPoint = handleYShapeCenterFocus()

        val byteArrayOf = ByteArray(4)
        byteArrayOf[0] = yShapeCenterFocusPoint.selectShapeIndex.toByte()
        byteArrayOf[1] = yShapeCenterFocusPoint.selectEdgeIndex.toByte()
        byteArrayOf[2] = 0x00
        byteArrayOf[3] = 0x00
        return byteArrayOf
    }

    override fun updateFocusPoint4YShape(params: ByteArray?): PointF {
        val pointF = PointF(shapeCenterPointF.x, shapeCenterPointF.y)
        if (focusViews.isEmpty()) return pointF
        params?.apply {
            log(
                CanvasLayout.TAG,
                "updateFocusPoint4YShape params = ${this[0]} ${this[1]} ${this[2]} ${this[3]}"
            )
            if (this[0].toInt() >= shapeViews.size) {
                val yShapeCenterFocus = handleYShapeCenterFocus()
                this[0] = yShapeCenterFocus.selectShapeIndex.toByte()
                this[1] = yShapeCenterFocus.selectEdgeIndex.toByte()
            }
            focusViews.onEach { it.isSelected = false }.filterIsInstance<FocusPointView>().filter {
                it.focusPointMsg.let { point ->
                    point.selectShapeIndex == Utils.byte2Int(this[0])
                        && point.selectEdgeIndex == Utils.byte2Int(this[1])
                }
            }.forEach {
                it.isSelected = true
                pointF.x = it.pos.x
                pointF.y = it.pos.y
            }
        }
        return pointF
    }

    override fun setShapeRotation(onceRotation: Float) {
        shapeViews.first().let { first ->
            val centerPos = first.centerPos
            shapeViews.onEach {
                if (it is BusinessShapeView) {
                    it.horizontalRotation(onceRotation, PointF(canvasLayout.canvasSize / 2f, canvasLayout.canvasSize / 2f))
                }
            }
        }
        notifyRefreshShapeUI()
    }
}