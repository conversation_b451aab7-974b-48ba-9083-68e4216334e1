package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.widget.ImageView
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/10/25
 *     version : 1.0.0
 *     desc    : 辅助电源图形
 */
class ImgPowerShapeView(context: Context) : BaseAssistView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 38//边长
        const val SIZE_HEIGHT = 38//边长
        const val PADDING = 9//内边距
    }

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    private fun init() {
        inputImgView.maxWidth = SIZE_WIDTH
        inputImgView.maxHeight = SIZE_HEIGHT
        setPadding(PADDING, PADDING, PADDING, PADDING)
        inputImgView.scaleType = ImageView.ScaleType.FIT_CENTER
        setInputImageResource(R.mipmap.new_light_6061_icon_power)
    }

    fun resetSize(width: Int, height: Int, padding: Int) {
        sizeWith = width
        sizeHeight = height
        inputImgView.maxWidth = width
        inputImgView.maxHeight = height
        setPadding(padding, padding, padding, padding)
        inputImgView.scaleType = ImageView.ScaleType.FIT_CENTER
        setInputImageResource(R.mipmap.new_light_6061_icon_power)
    }
}