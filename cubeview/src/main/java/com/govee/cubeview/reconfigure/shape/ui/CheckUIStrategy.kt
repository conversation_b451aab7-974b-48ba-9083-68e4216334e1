package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/10/10
 *     version : 1.0.0
 *     desc    :
 */
open class CheckUIStrategy(private val shapeView: BaseShapeView) : ShapeUIStrategy {
    override fun defaultState(params: BaseShapeParams) = updateState(params, -1)

    override fun updateState(params: BaseShapeParams, state: Int) {
        // 校验的颜色，透明度，大小等参数
        showLog("CheckUIStrategy", "updateState: state = $state ")
        params.let {
            when (state) {
                0 -> {
                    shapeView.alpha = 1f // 背景需要看具体的 view 来实现
                    it.paintBaseShape.color = shapeView.toColor(it.unCheckColorStroke)
                    it.paintShapeBackground.color = shapeView.toColor(it.unCheckColor)
                    it.visibleText = true
                }
                1 -> {
                    shapeView.alpha = 1f
                    it.paintBaseShape.color = shapeView.toColor(it.checkingStrokeColor)
                    it.paintShapeBackground.color = it.checkingColor
                    it.visibleText = false
                }
                2 -> {
                    shapeView.alpha = 1f
                    it.paintBaseShape.color = shapeView.toColor(it.checkedStrokeColor)
                    it.paintShapeBackground.color = shapeView.toColor(it.checkedColor)
                    it.visibleText = false
                }
                -1 -> {
                    shapeView.alpha = 1f
                    it.paintBaseShape.color = shapeView.toColor(it.checkedStrokeColor)
                    it.paintShapeBackground.color = shapeView.toColor(it.checkedColor)
                    it.visibleText = true
                }
            }
        }

    }

    override fun getType(): Type = Type.Check
}