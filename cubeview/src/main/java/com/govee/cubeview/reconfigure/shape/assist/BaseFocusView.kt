package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.graphics.PointF
import com.govee.cubeview.shape.FocusPoint


/**
 *     author  : sinrow
 *     time    : 2022/10/12
 *     version : 1.0.0
 *     desc    : 基础聚焦图形
 */
abstract class BaseFocusView(context: Context) : BaseAssistView(context) {

    var focusDragCallback: ((PointF) -> Unit)? = null//拖拽回调
    var focusPointCallback: ((FocusPoint) -> Unit)? = null//选中回调
    var isIn = false


    /*设置聚拢&扩散 true/false*/
    open fun showFocusState(isIn: Boolean) {

    }

    fun setScale(scale: Float) {
        if (scale <= 0) return
    }
}