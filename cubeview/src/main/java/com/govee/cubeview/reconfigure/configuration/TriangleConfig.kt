package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.TriangleView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.TriangleShapeParams

/**
 *     author  : sinrow
 *     time    : 2022/10/9
 *     version : 1.0.0
 *     desc    : 三角形 --- H6067
 */
class TriangleConfig : AbsShapeConfig() {

    override fun configShapeParams(): BaseShapeParams {
        return TriangleShapeParams()
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        return TriangleView(context).apply { setConfig(this@TriangleConfig) }
    }
}