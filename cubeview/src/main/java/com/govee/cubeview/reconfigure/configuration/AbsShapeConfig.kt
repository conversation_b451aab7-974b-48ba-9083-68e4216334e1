package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import android.graphics.PointF
import com.govee.cubeview.Utils
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.assist.*
import com.govee.cubeview.reconfigure.shape.data.BaseNote
import com.govee.cubeview.reconfigure.shape.ui.*
import com.govee.cubeview.shape.ShapePosition
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min

/**
 *     author  : sinrow
 *     time    : 2022/10/9
 *     version : 1.0.0
 *     desc    : 用于配置图形基础信息
 *              包括：基础参数表、UI 处理类、图形数据转换、定义图形数据结构、生成各种辅助图形
 */
abstract class AbsShapeConfig : IConfig {

    override fun configShapeParams(): BaseShapeParams = BaseShapeParams()

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {

        return HashMap<Type, ShapeUIStrategy>().apply {
            NormalUIStrategy(shapeView.context).inject(this)
            PreViewUIStrategy(shapeView.context).inject(this)
            ColorUIStrategy(shapeView.context).inject(this)
            InstallUIStrategy(shapeView).inject(this)
            CheckUIStrategy(shapeView).inject(this)
            MultiPowerStrategy(shapeView).inject(this)
            MoveFeastUIStrategy(shapeView).inject(this)
            GraffitiUIStrategy(shapeView.context).inject(this)
        }
    }

    override fun outputAfterConvertShapeData(shapeViews: MutableList<BaseShapeView>): ArrayList<ShapePosition> {
        return arrayListOf<ShapePosition>().apply {
            shapeViews.onEach {
                val toShapePosition = it.toShapePosition()
                add(toShapePosition)
            }
        }
    }

    override fun inputBeforeConvertShapeData(shapePosition: ArrayList<ShapePosition>): ArrayList<ShapePosition> {
        shapePosition.forEachIndexed { index, shape ->
            if (index != 0) {
                shape.lastPosition = index - 1
            }
        }
        return shapePosition
    }

    override fun updateOperatedShapeViews(mModeType: CanvasLayoutModeType, shapeViews: MutableList<BaseShapeView>) {

    }

    override fun configNote(): BaseNote = BaseNote()

    override fun createAddView(context: Context): ImgAddShapeView = ImgAddShapeView(context)

    override fun createDeleteView(context: Context): ImgDelShapeView = ImgDelShapeView(context)

    override fun createPowerView(context: Context): ImgPowerShapeView = ImgPowerShapeView(context)

    override fun createPathView(context: Context): ImgPathShapeView = ImgPathShapeView(context)

    override fun createFocusView(context: Context): BaseFocusView {
        return FocusDragView(context)
    }

    override fun getShapeRange(shapeViews: MutableList<BaseShapeView>): FloatArray {
        var minX = 0f
        var maxX = 0f
        var minY = 0f
        var maxY = 0f
        shapeViews.apply {
            first().let {
                val shapePoint4Canvas = it.getShapePoint4Canvas()
                shapePoint4Canvas.forEach { firstPoint ->
                    minX = firstPoint.x
                    maxX = firstPoint.x
                    minY = firstPoint.y
                    maxY = firstPoint.y
                }
            }
        }.forEach {
            val shapePoint = it.getShapePoint4Canvas()
            shapePoint.forEach { pointF ->
                minX = min(pointF.x, minX)
                maxX = max(pointF.x, maxX)
                minY = min(pointF.y, minY)
                maxY = max(pointF.y, maxY)
            }
        }
        return FloatArray(4).apply {
            this[0] = minX
            this[1] = maxX
            this[2] = minY
            this[3] = maxY
        }
    }

    override fun checkShapeCenterPointF(shapeViews: MutableList<BaseShapeView>): PointF {
        getShapeRange(shapeViews).apply {
            val centerPointFX = (this[0] + this[1]) / 2f
            val centerPointFY = (this[2] + this[3]) / 2f
            return PointF(centerPointFX, centerPointFY)
        }
    }

    override fun getShapeEffectCenterPoint(shapeViews: MutableList<BaseShapeView>): PointF {
        val centerPointF = checkShapeCenterPointF(shapeViews)
        val isInShapeList = arrayOfNulls<Boolean>(shapeViews.size)
        var inShapeCount = 0
        shapeViews.forEachIndexed { index, absCubeShapeView ->
            centerPointF.let {
                absCubeShapeView.isPointInShape(it).let { inShape ->
                    isInShapeList[index] = inShape
                    if (inShape) inShapeCount++
                    com.govee.cubeview.log("getShapeCenterPoint", "index = $index ; in = $inShape")
                }
            }
        }

        if (inShapeCount == 1) {
            /*在单个图形内（不包括边界）*/
            val index = isInShapeList.indexOfFirst { it == true }

            //默认中点
            var minDistance = Utils.distance(shapeViews[index].centerPos, centerPointF)
            var minDistancePointF = shapeViews[index].centerPos

            /*每个顶点*/
            val points: Array<PointF> = shapeViews[index].getShapePoint4Canvas()

            /*遍历计算距离*/
            points.forEachIndexed { _, edgePointF ->
                val dis = Utils.distance(edgePointF, centerPointF)
                if (dis < minDistance) {
                    minDistancePointF = edgePointF
                    minDistance = dis
                }
            }
            return minDistancePointF
        }
        return centerPointF
    }

}


interface IConfig {

    /**
     * 配置图形基础参数
     */
    fun configShapeParams(): BaseShapeParams

    /**
     * 配置图形 Shape UI 策略类
     */
    fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy>

    /**
     * 创建图形数据结构
     */
    fun configNote(): BaseNote

    /**
     * 转换输出图形前的数据
     */
    fun outputAfterConvertShapeData(shapeViews: MutableList<BaseShapeView>): ArrayList<ShapePosition>

    /**
     * 转换输入图形数据形成数据结构，返回上一个图形的下标
     * @param index 下标
     * @param position 数据
     */
    fun inputBeforeConvertShapeData(shapePosition: ArrayList<ShapePosition>): ArrayList<ShapePosition>

    /**
     * 更新当前的连接状态
     */
    fun updateOperatedShapeViews(mModeType: CanvasLayoutModeType, shapeViews: MutableList<BaseShapeView>)

    /**
     * 创建基础图形
     */
    fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView

    fun createAddView(context: Context): ImgAddShapeView

    fun createDeleteView(context: Context): ImgDelShapeView

    fun createPowerView(context: Context): ImgPowerShapeView

    fun createPathView(context: Context): ImgPathShapeView

    fun createFocusView(context: Context): BaseFocusView

    /**
     * 是否忽略首个电源图标
     */
    fun ignorePowerView(): Boolean = false

    fun fetchMultiPowerLimitIndex(shapeSize: Int): Int = ceil((shapeSize / 2f)).toInt()

    fun getShapeRange(shapeViews: MutableList<BaseShapeView>): FloatArray

    fun getShapeEffectCenterPoint(shapeViews: MutableList<BaseShapeView>): PointF

    fun checkShapeCenterPointF(shapeViews: MutableList<BaseShapeView>): PointF

    fun checkSingleClickModeType(modeType: CanvasLayoutModeType): Boolean = false
}