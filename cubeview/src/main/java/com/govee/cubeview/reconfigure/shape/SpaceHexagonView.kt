package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.Utils
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.reconfigure.shape.ui.SpaceHexagonParams
import com.govee.cubeview.reconfigure.shape.ui.toSetFloatArray
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor
import com.govee.ui.R
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2023/4/24
 *     version : 1.0.0
 *     desc    :
 */
class SpaceHexagonView(context: Context) : BusinessShapeView(context) {

    private val initAngle = -90f

    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)
        val shapePosition = shapeData.shapePosition

        // 如果主电源有赋值，则按照辅助电源形式显示
        shapePosition.mainPower?.let {
            mainPower.powerEdgeNUmber
            mainPower.powerNumber = it.powerNumber
            mainPower.powerEdgeNUmber = it.powerEdgeNUmber
            mainPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMainPower = true
        }
        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }
        params.inputNum = shapePosition.inputNum
        params.outputNum = shapePosition.outputNum

    }

    override fun getNextShapeCenterPointByRotation(
        pointF: PointF,
        length: Float,
        rotation: Float
    ): PointF {
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation + 30f + initAngle, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(
        centerPointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<PointF> {

        return Array(6) { PointF() }.apply {
            val matrix = Matrix()//旋转

            matrix.setRotate(rotation + initAngle, centerPointF.x, centerPointF.y)

            /*六边形，最左边的点为起始点 0 ，默认角度为 60，经过旋转后，变成了左上角顶点*/
            /*获取辅助图形的旋转点，需要从左上角开始，这样就可以达到了要求了*/

            val shapePoint = Array(6) { FloatArray(2) }
            shapePoint[0] =
                floatArrayOf(
                    (length * cos(2 * PI * 3 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 3 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[1] =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[2] =
                floatArrayOf(
                    (length * cos(2 * PI * 5 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 5 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[3] =
                floatArrayOf(
                    (length * cos(0.0) + centerPointF.x).toFloat(),
                    (length * sin(0.0) + centerPointF.y).toFloat()
                )
            shapePoint[4] =
                floatArrayOf(
                    (length * cos(2 * PI * 1 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 1 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[5] =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + centerPointF.y).toFloat()
                )

            matrix.mapPoints(shapePoint[0])
            matrix.mapPoints(shapePoint[1])
            matrix.mapPoints(shapePoint[2])
            matrix.mapPoints(shapePoint[3])
            matrix.mapPoints(shapePoint[4])
            matrix.mapPoints(shapePoint[5])

            toSetFloatArray(shapePoint)
        }
    }

    override fun toShapePosition(): ShapePosition {
        return super.toShapePosition().apply {
            this.inputNum = <EMAIL>
            this.outputNum = <EMAIL>
        }
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        //showLog("getRotationByDirectionTag: directionTag = $directionTag ")
        // 根据需求，左下角作为1边，然后顺序 123456，directionTag 为 0 需要做转换
        return directionTag * 60 - 60f
    }

    override fun drawBackground(canvas: Canvas, paint: Paint) {
        val showPartitionBackground = showPartitionBackground()
        //showLog(TAG, "drawBackground: showPartitionBackground = $showPartitionBackground ")
        if (showPartitionBackground) {
            drawCustomPreViewUI(canvas)
            return
        }

        if (!params.editable) {
            params.paintShapeBackground.color = toColor(R.color.ui_line_style_5_3_stroke_color)
            super.drawBackground(canvas, params.paintShapeBackground)
            alpha = 1.0f
        } else {
            super.drawBackground(canvas, paint)
        }

        val onlyDisplayTheTopArea = onlyDisplayTheTopArea()
        if (onlyDisplayTheTopArea && params.editable) {
            drawCustomCheckingUI(canvas)
            return
        }
    }

    /**
     * 处理预览模式下的背景颜色
     */
    private fun drawCustomPreViewUI(canvas: Canvas) {
        val allShapePoint =
            getShapeDefaultPoint(params.outsideLineLength)
        val shapePosition1 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[2].x, allShapePoint[2].y),
                PointF(allShapePoint[3].x, allShapePoint[3].y),
                PointF(allShapePoint[4].x, allShapePoint[4].y)
            )

        val shapePosition2 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[0].x, allShapePoint[0].y),
                PointF(allShapePoint[1].x, allShapePoint[1].y),
                PointF(allShapePoint[2].x, allShapePoint[2].y)
            )

        val shapePoint3 =
            arrayOf(
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(allShapePoint[4].x, allShapePoint[4].y),
                PointF(allShapePoint[5].x, allShapePoint[5].y),
                PointF(allShapePoint[0].x, allShapePoint[0].y)
            )

        drawView(canvas, shapePosition1, params.preViewColors[0])
        drawView(canvas, shapePosition2, params.preViewColors[1])
        drawView(canvas, shapePoint3, params.preViewColors[2])
    }


    private fun drawCustomCheckingUI(canvas: Canvas) {
        // 显示图形 y 轴最小，x 最大的
        // 校准的 ic 数，是怎么处理的？
        // 安装、校准的时候，发送的 ic 数量的逻辑：
        // ----  校准的个数*6 +当前块插入的序号对应的数量，如果是 1、2 编号，则发送0，3、4则发2，0、5则发 4 -----
        //            1, 2 -> icIndex = 0
        //            3, 4 -> icIndex = 2
        //            0, 5 -> icIndex = 4

        // 分区，然后确认顶部的是哪个边序号出去的。
        val second = getCheckStateAreaInfo().second
        drawView(canvas, second, params.checkingColor)
    }

    private val allShapePoint4Int = arrayOfNulls<IntArray>(6)
    private val minPoint = intArrayOf(0, 10000, 0)
    private val topArray = Array(4) { PointF() }


    //获取安装时的显示区域信息
    private fun getCheckStateAreaInfo(): Pair<Int, Array<PointF>> {

        val allShapePoint = getShapeDefaultPoint()

        var index = 0
        //x最大，y最小
        //转成int，防止float判断误差
        allShapePoint.forEachIndexed { i, floats ->
            allShapePoint4Int[i] = intArrayOf(floats.x.toInt(), floats.y.toInt())
        }

        allShapePoint4Int.forEachIndexed { i, curPoint ->
            if (curPoint!![1] == minPoint[1]) {// y 轴相等，判断 x
                if (curPoint[0] > minPoint[0]) {
                    index = i
                    minPoint[0] = curPoint[0]
                    minPoint[1] = curPoint[1]
                    minPoint[2] = 1
                }
            } else if (curPoint[1] < minPoint[1]) {// y 轴小于，则直接赋值
                index = i
                minPoint[0] = curPoint[0]
                minPoint[1] = curPoint[1]
            }
        }
        index = Utils.rangeInt(index, 6)

        showLog(TAG, "getCheckStateAreaInfo: offsetR=$offsetRotation , index=$index ")

        return Pair(
            index,
            topArray.apply {
                this[0] = allShapePoint[2]
                this[1] = allShapePoint[3]
                this[2] = allShapePoint[4]
                this[3] = selfCenterPointF
            }
        )
    }

    /**
     * 处于校验模式下，当前正在校验的方块只显示顶层视图
     */
    private fun onlyDisplayTheTopArea(): Boolean {
        return shapeState.let {
            it.type.isCheckType() || it.type.isInstallType() && (it.state == 2 || it.state == 1) || it.type.isCheckPreType()
        }
    }

    /**
     * 显示分区背景颜色
     * <p>
     *     作用于预览模式、校验模式
     * </p>
     */
    private fun showPartitionBackground(): Boolean {
        return shapeState.type.isPreViewType()
    }

    private val checkingColorPaint = Paint()

    /**
     * 图形分区
     * 使用 canvas.drawPath(it, checkingColorPaint)，根据路径 + Paint.Style.FILL 实现实心图形背景
     */
    private fun drawView(canvas: Canvas, shapePoint3: Array<PointF>, bgColor: Int) {
        baseShapePath.let {
            it.reset()
            shapePoint3.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }
            it.close()
            checkingColorPaint.reset()
            checkingColorPaint.color = bgColor
            checkingColorPaint.style = Paint.Style.FILL
            canvas.drawPath(it, checkingColorPaint)
            it.reset()
        }
    }


    override fun drawShapeOtherAttributes(canvas: Canvas, paint: Paint) {
        // 有两个业务。1、基础的中间属性 2、预览模式下，不同背景色

        val length = params.outsideLineLength
        val shapePoint =
            arrayOf(
                PointF(
                    (length * cos(2 * PI * 4 / 6) + selfCenterPointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + selfCenterPointF.y).toFloat()
                ),
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(
                    (length * cos(0.0) + selfCenterPointF.x).toFloat(),
                    (length * sin(0.0) + selfCenterPointF.y).toFloat()
                ),
                PointF((selfCenterPointF.x), (selfCenterPointF.y)),
                PointF(
                    (length * cos(2 * PI * 2 / 6) + selfCenterPointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + selfCenterPointF.y).toFloat()
                ),
            )

        baseShapePath.let {
            it.reset()
            shapePoint.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }
            val matrix = Matrix()//旋转
            matrix.setRotate(offsetRotation - 30, selfCenterPointF.x, selfCenterPointF.y)

            /*画路径*/
            it.transform(matrix)
            /*画路径*/
            canvas.drawPath(it, paint)

            it.reset()
        }
        for (directionTag in 0..5) {
            drawInstalledSerialNum(canvas, directionTag)
        }
    }

    override fun drawCenterText(canvas: Canvas, paint: Paint) {
        val textBgPaint = params.paintCenterTextBg
        textBgPaint.reset()
        var color = if (shapeState.type.isInstallType()) params.shapeColor else params.shapeColor

        if (modeType == CanvasLayoutModeType.MoveFeast) {
            color = toColor(moveFeastData.color)
        }
        textBgPaint.color = color

        textBgPaint.style = Paint.Style.FILL_AND_STROKE
        canvas.drawCircle(width / 2f, height / 2f, params.textBgRadius, textBgPaint)
        super.drawCenterText(canvas, paint)
    }

    override fun showSerialNumber(): Boolean {
        return false
    }

    override fun getPathShapeCenterPoints(): PointF {
//        showLog(TAG, "getPathShapeCenterPoints: offsetRotation = $offsetRotation ")
        return getNextShapeCenterPoints(offsetRotation + 180, params.innerLineLength)
    }

    override fun getPathEntity(lastShapeView: BaseShapeView?): PathEntity {
        val pathEntity = PathEntity()

        val inputSelectedList = Utils.parseSelectedBytes4Bit(params.inputNum.toByte())
        val outputSelectedList = Utils.parseSelectedBytes4Bit(params.outputNum.toByte())
        inputSelectedList.onEach {
            val rotationByDirectionTag = getRotationByDirectionTag(it) // directionTag * 60f
            val nextRotation = (offsetRotation + rotationByDirectionTag).checkAngle()// 出边的旋转角度
            val nextShapeCenterPoints = getNextShapeCenterPointByRotation(centerPos, params.innerLineLength, nextRotation)// 得到的中心点
            val calAngle = Utils.calAngle(nextShapeCenterPoints, centerPos).checkAngle().toFloat()
            pathEntity.input.add(Pair(calAngle, nextShapeCenterPoints))
        }
        outputSelectedList.onEach {
            val rotationByDirectionTag = getRotationByDirectionTag(it)
            val nextRotation = (offsetRotation + rotationByDirectionTag).checkAngle()
            val nextShapeCenterPoints = getNextShapeCenterPointByRotation(centerPos, params.innerLineLength, nextRotation)// 得到的中心点

            pathEntity.outputs.add(Pair((nextRotation + initAngle).checkAngle(), nextShapeCenterPoints))
        }
        return pathEntity
    }

    /**
     * 获取下一个图形的 shapePosition 信息
     */
    override fun getNextShapePosition(edgeTag: Int): ShapePosition {
        val type = params.shapeType
        val rotationByDirectionTag =
            getRotationByDirectionTag(edgeTag) // directionTag * 60f // 这个就是下一个图形的方向
        val nextRotation =
            rotationByDirectionTag + offsetRotation // directionTag * 60f + offsetRotation = next.offsetRotation.checkAngle().roundToInt()
        val nextCenterPoint =
            getNextShapeCenterPoints(nextRotation)
        /*设置自身offsetTag -- 需要计算输入位置冲突*/
//        val offsetTag = Constants.getNextShapeSupportOffset(edgeTag, type).first

        // 下一个图形的旋转角度
//        val finalRotation = (offsetRotation + rotationByDirectionTag + offsetTag.first) % 360


        return ShapePosition(
            type, nextCenterPoint.x, nextCenterPoint.y,
            offsetRotation.checkAngle().roundToInt()
        )
    }

    /**
     * @param subShapeView 下一块图形的信息
     */
    override fun updateStateBySub(subShapeView: BaseShapeView?) {
    }

    override fun notifyParentConnectState() {
        super.notifyParentConnectState()
        note.parentNote?.note?.subNotes?.remove(this)
    }

    override fun getAddAssistShapeCenterLength(): Float {
        return params.nextShapeCenterLineLength * 2
    }

    private fun drawInstalledSerialNum(canvas: Canvas, directionTag: Int) {
        if (!shapeState.type.isInstallType()) return// 非安装图形

        if (params !is SpaceHexagonParams) return
        (params as SpaceHexagonParams).let {
            if (!(it.showOutputTag || it.showInputTag)) return
            val textBgPaint = params.paintCenterTextBg
            textBgPaint.reset()
            // 正在安装，显示输入和输出
            val inputSelectedList = Utils.parseSelectedBytes4Bit(params.inputNum.toByte())
            val outputSelectedList = Utils.parseSelectedBytes4Bit(params.outputNum.toByte())
            var hasContainsDirectionTag = false

            if (it.showInputTag && inputSelectedList.contains(directionTag)) {
                // 输入边包含了此方向，需要显示蓝色
                textBgPaint.color = toColor(params.colorInputTextBg)
                hasContainsDirectionTag = true
            } else if (it.showOutputTag && outputSelectedList.contains(directionTag)) {
                // 输出边包含了此方向，需要显示黄色
                textBgPaint.color = toColor(params.colorOutputTextBg)
                hasContainsDirectionTag = true
            }
            if (!hasContainsDirectionTag) {
                showLog("drawInstalledSerialNum()，输入输出边不包含此方向 directionTag = $directionTag")
                return
            }

            val rotationByDirectionTag = getRotationByDirectionTag(directionTag) // directionTag * 60f

            val nextRotation = (offsetRotation + rotationByDirectionTag).checkAngle()// 出边的旋转角度

            val nextShapeCenterPoints = getNextShapeCenterPointByRotation(selfCenterPointF, 19f, nextRotation)// 得到的中心点

            val width = nextShapeCenterPoints.x
            val height = nextShapeCenterPoints.y


            textBgPaint.style = Paint.Style.FILL_AND_STROKE

            canvas.drawCircle(width, height, 9.5f, textBgPaint)// 9.5 为圆圈 19 半径的一半

            val centerText = (directionTag + 1).toString()
            val fontMetrics = params.paintCenterText.fontMetrics
            val top = fontMetrics.top//为基线到字体上边框的距离,即上图中的top
            val bottom = fontMetrics.bottom//为基线到字体下边框的距离,即上图中的bottom

            val baseLineY = (height) - top / 2 - bottom / 2//基线中间点的y轴计算公式

            canvas.drawText(centerText, (width), baseLineY, params.paintCenterText)

        }
    }

}