package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.SquareView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.SquareParams
import com.govee.cubeview.reconfigure.shape.ui.Type

/**
 *     author  : sinrow
 *     time    : 2024/6/14
 *     version : 1.0.0
 *     desc    :
 */
class SquareConfig : AbsShapeConfig() {

    companion object {
        const val lineLength = 24f
    }

    override fun configShapeParams(): BaseShapeParams {
        return SquareParams()
    }

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {
        return super.configUIStrategy(shapeView)
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        return SquareView(context).apply {
            setConfig(this@SquareConfig)
        }
    }


}