package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PointF
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2022/10/8
 *     version : 1.0.0
 *     desc    : 基础辅助图形
 *     1、初始化
 *     2、设置位置
 *     3、作用
 *         --- 运行功能  （提示、选中框、）
 *         --- 点击功能（添加、删除、聚焦点）
 */
abstract class BaseAssistView : FrameLayout, IBaseAssistView {

    protected val TAG = this.javaClass.simpleName.toString()

    val inputImgView by lazy { ImageView(context) }
    val centerTextView by lazy { TextView(context) }

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    companion object {
        var sizeWith = 19//边长
        var sizeHeight = 46//边长
    }

    val pos = PointF()
    protected val startPointF = PointF()
    protected val endPointF = PointF()

    override fun setLayoutParams(pointF: PointF) {
        pos.x = pointF.x
        pos.y = pointF.y
        val lp = LayoutParams(sizeWith, sizeHeight)
        val left = pointF.x.roundToInt() - sizeWith / 2
        val top = pointF.y.roundToInt() - sizeHeight / 2
        lp.leftMargin = left
        lp.topMargin = top
        layoutParams = lp
    }

    fun setAnimationPoint(start: PointF, end: PointF) {
        startPointF.set(start)
        endPointF.set(end)
    }

    override fun doSomething(block: () -> Unit) {
        block()
    }


    open fun setInputImageResource(resId: Int) {
        removeView(inputImgView)
        inputImgView.setImageResource(resId)
        addView(inputImgView)
    }

    open fun setImageBitmap(bitmap: Bitmap?) {
        bitmap?.let {
            removeView(inputImgView)
            inputImgView.setImageBitmap(it)
            addView(inputImgView)
        }
    }
}

interface IBaseAssistView {

    fun setLayoutParams(pointF: PointF)

    /**
     * 用于具体辅助类实现的的功能
     */
    fun doSomething(block: () -> Unit)

}
