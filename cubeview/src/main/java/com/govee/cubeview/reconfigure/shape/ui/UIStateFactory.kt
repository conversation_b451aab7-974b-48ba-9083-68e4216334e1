package com.govee.cubeview.reconfigure.shape.ui

/**
 *     author  : sinrow
 *     time    : 2022/9/27
 *     version : 1.0.0
 *     desc    :
 */


/**
 * 图形 UI 策略类
 */
interface ShapeUIStrategy {

    /**
     * 模式默认状态类型
     * <p>
     *     可根据实际情况调整默认参数
     * </p>
     */
    fun defaultState(params: BaseShapeParams) = updateState(params, 0)

    fun updateState(params: BaseShapeParams, state: Int)

    fun getType(): Type

    fun inject(hashMap: HashMap<Type, ShapeUIStrategy>) {
        hashMap[getType()] = this
    }

}

// 二级状态，0：未开始，1：正在进行，2：结束
data class UIType(val type: Type, var state: Int = 0) {


    /**
     * 该方法为了处理view使用 alpha 虚化时，导致图形的边框无法完全显示
     */
    fun checkLayoutParamsWidth(): Boolean {
        return type.let {
            it.isInstallType() ||
                it.isPreViewType() ||
                it.isCheckType() ||
                it.isMultiPowerType() && state == 2
        }
    }


    fun isMultiPower(): Boolean {
        return type == Type.MultiPower
    }
}


/**
 * 图形 Shape 状态模式
 */
enum class Type {
    Normal,//正常模式
    Preview,//预览模式
    Install,//安装模式
    Check,/*校验模式*/
    Color,//颜色模式
    MultiPower,// 辅助电源模式
    MoveFeast,//观影盛宴
    CheckPre,//检查预览
    AddLamp,//加装模式
    Graffiti,// 涂鸦模式
    SoftLine,// 软件加装
    ;

    fun isInstallType(): Boolean {
        return this == Install
    }

    fun isCheckType(): Boolean {
        return this == Check
    }

    fun isPreViewType(): Boolean {
        return this == Preview
    }

    fun isMultiPowerType(): Boolean {
        return this == MultiPower
    }

    fun isCheckPreType(): Boolean {
        return this == CheckPre
    }
}