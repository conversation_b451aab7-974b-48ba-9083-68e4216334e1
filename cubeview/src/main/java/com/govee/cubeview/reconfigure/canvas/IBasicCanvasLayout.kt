package com.govee.cubeview.reconfigure.canvas

import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.shape.ui.OnShapeListener
import com.govee.cubeview.shape.ShapePosition

/**
 *     author  : sinrow
 *     time    : 2022/9/6
 *     version : 1.0.0
 *     desc    :
 */
interface IBasicCanvasLayout {

    /**
     * 根据图形类型，初始化画布大小等参数
     */
    fun initParams()

    /**
     * 设置当前画布模式类型:颜色、编辑、预览等
     */
    fun setModeType(ty: CanvasLayoutModeType): BaseCanvasLayout

    /**
     * 设置画布类型
     */
    fun setCubeType(cubeType: PanelLampType): BaseCanvasLayout

    /**
     * 设置图形属性
     * <p>
     *     根据 @ShapePosition 中的 type 确认当前所需绘制的图形
     * </p>
     */
    fun setShapeData(points: ArrayList<ShapePosition>?)


    /**
     * 注册监听回调方法
     */
    fun registerListener(listener: OnShapeListener): BaseCanvasLayout
}
