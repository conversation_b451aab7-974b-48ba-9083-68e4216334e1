package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import android.graphics.PointF
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.DataProcessCenter
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.SpaceHexagonView
import com.govee.cubeview.reconfigure.shape.assist.DoubleStreamPathView
import com.govee.cubeview.reconfigure.shape.assist.ImgPathShapeView
import com.govee.cubeview.reconfigure.shape.data.BaseNote
import com.govee.cubeview.reconfigure.shape.data.SpaceNote
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.CheckUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.MoveFeastUIStrategyH6066
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.SpaceHexagonParams
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.reconfigure.shape.ui.space.CheckPreViewUIStrategyH606A
import com.govee.cubeview.reconfigure.shape.ui.space.InstallUIStrategyH606A
import com.govee.cubeview.shape.ShapePosition

/**
 *     author  : sinrow
 *     time    : 2023/4/24
 *     version : 1.0.0
 *     desc    :
 */
class SpaceHexagonConfig : AbsShapeConfig() {

    override fun configShapeParams(): BaseShapeParams {
        return SpaceHexagonParams()
    }

    override fun configNote(): BaseNote {
        return SpaceNote()
    }

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {
        return super.configUIStrategy(shapeView).apply {
            InstallUIStrategyH606A(shapeView).let { put(it.getType(), it) }
            CheckPreViewUIStrategyH606A(shapeView).let { put(it.getType(), it) }
            CheckUIStrategy(shapeView).inject(this)
            MoveFeastUIStrategyH6066(shapeView).inject(this)
        }
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        // 创建图形 view
        return SpaceHexagonView(context).apply { setConfig(this@SpaceHexagonConfig) }
    }

    override fun createPathView(context: Context): ImgPathShapeView {
        return DoubleStreamPathView(context)
    }

    override fun updateOperatedShapeViews(mModeType: CanvasLayoutModeType, shapeViews: MutableList<BaseShapeView>) {
        if (!mModeType.isEditMode()) return // 只有在编辑页面出现
        shapeViews.forEach {
            // 注入的时候，更新数据
            it.updateStateByGraphic(it, shapeViews)
        }
    }

    override fun outputAfterConvertShapeData(shapeViews: MutableList<BaseShapeView>): ArrayList<ShapePosition> {
        // 处理每一块边的图形数据
        shapeViews.firstOrNull()?.let {
            if (it.shapeState.isMultiPower()) { // 这个是不是要选择后输出呢
                // 如果是辅助电源选择的 UI 类型，选择后，输出。
                val reBuildSpaceViewShapes = DataProcessCenter.reBuildSpaceViewShapes(shapeViews)
                if (reBuildSpaceViewShapes.size == shapeViews.size) {
                    return super.outputAfterConvertShapeData(reBuildSpaceViewShapes)
                }
            }
        }
        return super.outputAfterConvertShapeData(shapeViews)
    }

    override fun ignorePowerView() = true

    override fun fetchMultiPowerLimitIndex(shapeSize: Int) = 0// 任意块都可拼接

    override fun checkShapeCenterPointF(shapeViews: MutableList<BaseShapeView>): PointF {
        var difX = 0f
        var difY = 0f
        shapeViews.forEach {
            difX += it.centerPos.x
            difY += it.centerPos.y
        }
        difX /= shapeViews.size
        difY /= shapeViews.size
        return PointF(difX, difY)
    }

}