package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import android.graphics.PointF
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.assist.BaseFocusView
import com.govee.cubeview.reconfigure.shape.assist.FocusPointView
import com.govee.cubeview.reconfigure.shape.data.BaseNote
import com.govee.cubeview.reconfigure.shape.data.GameNote
import com.govee.cubeview.reconfigure.shape.gamewall.GameRectangleView
import com.govee.cubeview.reconfigure.shape.gamewall.GameSquareView
import com.govee.cubeview.reconfigure.shape.gamewall.GameTriangleView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.CheckUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.GameWallParams
import com.govee.cubeview.reconfigure.shape.ui.MoveFeastUIStrategyH6066
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.reconfigure.shape.ui.space.CheckPreViewUIStrategyH606A
import com.govee.cubeview.reconfigure.shape.ui.space.InstallUIStrategyH606A
import com.govee.cubeview.shape.Shape.TYPE_GAME_RECTANGLE
import com.govee.cubeview.shape.Shape.TYPE_GAME_SQUARE
import com.govee.cubeview.shape.Shape.TYPE_GAME_TRIANGLE
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2023/11/28
 *     version : 1.0.0
 *     desc    :
 */
class GameWallConfig(private val cubeShapeType: Int = 0) : AbsShapeConfig() {

    companion object {
        private const val TAG = "GameWallConfig"

        /*根据正边行的边长来计算出内切圆半径*/
        private const val height = 200f
        private const val with = 80f
        var TYPE_GAME_RECTANGLE_LINE_LENGTH = sqrt((with * with + height * height)) / sqrt(2f)
        const val TYPE_GAME_TRIANGLE_LINE_LENGTH = with
        const val TYPE_GAME_SQUARE_LINE_LENGTH = with
        const val GameRectangleViewWidth = with
        const val GameRectangleViewHeight = height / 2
    }

    override fun configShapeParams(): BaseShapeParams {
        showLog(TAG, "configShapeParams() cubeShapeType = $cubeShapeType")
        return GameWallParams().apply {
            shapeType = cubeShapeType
            when (cubeShapeType) {
                TYPE_GAME_RECTANGLE -> {
                    lineLength = TYPE_GAME_RECTANGLE_LINE_LENGTH
                    addImgRotationOffset = 0
                    powerImgRotationOffset = 0
                }

                TYPE_GAME_TRIANGLE -> {
                    lineLength = TYPE_GAME_TRIANGLE_LINE_LENGTH
                    addImgRotationOffset = 60
                    powerImgRotationOffset = 180 // 三角形顶点相反方向
                }

                TYPE_GAME_SQUARE -> {
                    lineLength = TYPE_GAME_SQUARE_LINE_LENGTH
                    addImgRotationOffset = 45
                    powerImgRotationOffset = 45
                }
            }
        }
    }

    override fun configNote(): BaseNote {
        return GameNote()
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        // 需要根据 cubeShapeType 来构建不同的 view
        showLog(TAG, "createShapeView() cubeShapeType = $cubeShapeType")
        return when (cubeShapeType) {
            TYPE_GAME_RECTANGLE -> {
                GameRectangleView(context)
            }

            TYPE_GAME_TRIANGLE -> {
                GameTriangleView(context)
            }

            TYPE_GAME_SQUARE -> {
                GameSquareView(context)
            }

            else -> {
                /*其他显示三角形*/
//                GameTriangleView(context)
//                GameSquareView(context)
                GameRectangleView(context)
            }
        }.apply {
            setConfig(GameWallConfig(cubeShapeType))
        }
    }

    override fun inputBeforeConvertShapeData(shapePosition: ArrayList<ShapePosition>): ArrayList<ShapePosition> {
        return shapePosition
    }

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {
        return super.configUIStrategy(shapeView).apply {
            InstallUIStrategyH606A(shapeView).let { put(it.getType(), it) }
            CheckPreViewUIStrategyH606A(shapeView).let { put(it.getType(), it) }
            CheckUIStrategy(shapeView).inject(this)
            MoveFeastUIStrategyH6066(shapeView).inject(this)
        }
    }

    override fun checkShapeCenterPointF(shapeViews: MutableList<BaseShapeView>): PointF {
        var difX = 0f
        var difY = 0f
        shapeViews.forEach {
            difX += it.centerPos.x
            difY += it.centerPos.y
        }
        difX /= shapeViews.size
        difY /= shapeViews.size
        return PointF(difX, difY)
    }

    override fun checkSingleClickModeType(modeType: CanvasLayoutModeType): Boolean {
        if (modeType.isColorMode() || modeType.isMoveFeast()) {
            return true
        }
        return super.checkSingleClickModeType(modeType)
    }

    override fun ignorePowerView() = true

    override fun createFocusView(context: Context): BaseFocusView {
        return FocusPointView(context)
    }
}