package com.govee.cubeview.reconfigure.canvas

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import com.govee.cubeview.Constants
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.configuration.YConfig
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.HexagonSolidView
import com.govee.cubeview.reconfigure.shape.data.CubeMoveFeastData
import com.govee.cubeview.reconfigure.shape.ui.IBasicBusiness
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.yes

/**
 *     author  : sinrow
 *     time    : 2022/10/26
 *     version : 1.0.0
 *     desc    :
 */
open class OldCanvasLayoutBusiness(context: Context, attrs: AttributeSet? = null) :
    BaseCanvasLayout(context, attrs), IBasicBusiness {


    override fun flipShapeViewByX() {
        canvasLayoutProxyV1.flipShapeViewByX()
    }

    override fun setShapeRotation(onceRotation: Float) {
        canvasLayoutProxyV1.setShapeRotation(onceRotation)
    }

    override fun getShapeRotations(): ArrayList<ShapePosition> {
        return canvasLayoutProxyV1.getShapeRotations()
    }

    override fun installNextShape(changeLastShapeState: Boolean): Pair<BaseShapeView, Int>? {
        return canvasLayoutProxyV1.installNextShape(changeLastShapeState)
    }

    override fun installLastShape(): Pair<BaseShapeView, Int>? {
        return canvasLayoutProxyV1.installLastShape()
    }

    override fun installAllShape() {
        canvasLayoutProxyV1.installAllShape()
    }

    override fun checkNextShape(nextPos: Int): PointF? {
        return canvasLayoutProxyV1.checkNextShape(nextPos)
    }

    override fun checkAllShape(): Boolean {
        return canvasLayoutProxyV1.checkAllShape()
    }

    override fun setSelectAll(isSelectedAll: Boolean): Boolean {
        return canvasLayoutProxyV1.setSelectAll(isSelectedAll)
    }

    override fun updateFocusPoint(pointF: PointF, isIn: Boolean) {
        canvasLayoutProxyV1.updateFocusPoint(pointF, isIn)
    }

    override fun setColorParams(
        colorList: ArrayList<Int>,
        brightnessList: ArrayList<Int>,
        selectedList: ArrayList<Boolean>
    ) {
        canvasLayoutProxyV1.setColorParams(colorList, brightnessList, selectedList)
    }


    override fun createShapeView(points: ArrayList<ShapePosition>?, needUpdateCanvas: Boolean) {
        points?.run {
            // 需要判断是否,每一个图形的 type 不一样，导致无法确定,影响：画布大小，默认图形
            if (!isEmpty()) {
                canvasLayoutProxyV1.parseShapeData(points)
                needUpdateCanvas.yes { canvasLayoutProxyV1.move2defaultPosition() }
                updateShapeState()
            } else {
                canvasLayoutProxyV1.resetShape(true)
            }
        }
    }

    override fun updateShapeState(modeType: CanvasLayoutModeType) {
        /*需要根据当前 CanvasLayoutModeType 类型刷新,提供对外的方法x*/
        canvasLayoutProxyV1.notifyRefreshShapeUI(modeType)
    }

    override fun resetShape(reserve: Boolean) {
        initParams()
        canvasLayoutProxyV1.resetShape(reserve)
    }

    fun resetShape(reserve: Boolean, rotation: Float) {
        canvasLayoutProxyV1.resetShape(reserve)
        canvasLayoutProxyV1.setShapeRotation(rotation)
    }


    fun updateFocusPoint(pointF: PointF) {
        canvasLayoutProxyV1.updateFocusPoint(pointF)

    }

    fun getMDefaultScale() = canvasLayoutProxyV1.mDefaultScale

    fun getMDefaultOffsetPoint() = canvasLayoutProxyV1.mDefaultOffsetPoint

    fun move2defaultPosition() = canvasLayoutProxyV1.move2defaultPosition()

    fun getFocusPoint() = canvasLayoutProxyV1.focusPoint

    fun getCurInstallIndex(): Int = canvasLayoutProxyV1.curInstallIndex

    fun getCheckAllCmd4HexagonSolidView(): MutableList<Int> {
        return mutableListOf<Int>().apply {
            canvasLayoutProxyV1.shapeViews.forEach {
                if (it is HexagonSolidView) {
                    val checkStateIcIndex = it.getCheckStateIcIndex()
                    add(checkStateIcIndex.first)
                }
            }
        }
    }

    fun getInstallingIcPos(pos: Int): Int {
        return canvasLayoutProxyV1
            .shapeViews[pos]
            .takeIf { it is HexagonSolidView }
            ?.let {
                (it as HexagonSolidView).getCheckStateIcIndex().first
            } ?: 0
    }

    /**
     * 图形是否全部不可见
     */
    fun isShapeAllVisible() = canvasLayoutProxyV1.isShapeAllVisible()

    /**
     * 图形是否全部不可见
     */
    fun isShapeInvisible() = canvasLayoutProxyV1.isShapeInvisible()

    fun getShapeCenterPointF(): PointF {
        return canvasLayoutProxyV1.getShapeEffectCenterPoint()
    }

    fun getShapeViews(): MutableList<BaseShapeView> {
        return canvasLayoutProxyV1.shapeViews
    }

    fun getPowerShapeView(): BaseShapeView? {
        val shapeViewSequence = canvasLayoutProxyV1.shapeViews.asSequence().filter {
            it.params.isMultiPowerSelected
        }
        return shapeViewSequence.firstOrNull()
    }

    fun updateDataWithType(type: CanvasLayoutModeType) {
        canvasLayoutProxyV1.updateDataWithType(type, false)
    }

    fun updateDragViewScale(scale: Float) =
        canvasLayoutProxyV1.updateDragViewScale(scale)

    fun defColorUi() = canvasLayoutProxyV1.defColorUi()

    fun getYShapeIc(index: Int): MutableList<Int> {
        if (cubeType.config !is YConfig) return mutableListOf()
        return (cubeType.config as YConfig).mYShapeTree.ergodic(index)
    }

    fun getYShapeDefaultFocusPoint(): ByteArray {
        return canvasLayoutProxyV1.getYShapeDefaultFocusPoint()
    }

    fun resetDefaultFocusPoint(callBack: Boolean): PointF {
        return canvasLayoutProxyV1.resetDefaultFocusPoint(callBack)
    }

    fun updateFocusPoint4YShape(params: ByteArray?): PointF {
        return canvasLayoutProxyV1.updateFocusPoint4YShape(params)
    }

    fun getShapeDefaultAngle(panelLampType: PanelLampType): Int {
        return Constants.getShapeDefaultAngle(panelLampType.shapeType)
    }

    fun moveFeastAreaOrderMap(): HashMap<Int, MutableList<CubeMoveFeastData>> {
        return canvasLayoutProxyV1.moveFeastAreaOrderMap()
    }

    fun getShapeData4MoveFeast(): ByteArray {
        return canvasLayoutProxyV1.getShapeData4MoveFeast()
    }
}