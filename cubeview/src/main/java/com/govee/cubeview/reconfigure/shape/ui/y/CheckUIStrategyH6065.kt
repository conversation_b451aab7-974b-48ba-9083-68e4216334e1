package com.govee.cubeview.reconfigure.shape.ui.y

import com.govee.cubeview.isLightMode
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/11/21
 *     version : 1.0.0
 *     desc    : Y 形灯校验模式的 UI 处理类
 *     <p>
 *         state = 0 表示当前未校验
 *         state = 1 表示当前正在校验
 *         state = 2 表示当前已校验
 *         根据不同状态刷新不同逻辑
 *     </p>
 */
class CheckUIStrategyH6065(private val shapeView: BaseShapeView) : ShapeUIStrategy {
    private val lightMode = shapeView.context.isLightMode()

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        showLog("", "updateState: state = $state ")
        params.let {
            when (state) {
                0 -> {
                    shapeView.alpha = 0.4f
                    it.colorProjection = R.color.ui_line_style_4_1_stroke_color
                    it.colorBottomEdge = R.color.ui_line_style_4_2_stroke_color
                    it.colorGuideLight = R.color.ui_line_style_4_2_stroke_color
                }
                1 -> {
                    shapeView.alpha = 1f
                    it.colorProjection = R.color.ui_line_style_4_1_stroke_color
                    it.colorBottomEdge = R.color.ui_line_style_4_2_stroke_color
                    it.colorGuideLight = R.color.ui_line_style_4_1_stroke_color
                }
                2 -> {
                    shapeView.alpha = 1f
                    it.colorProjection = if (lightMode) R.color.FFB2E2F3 else R.color.white
                    it.colorBottomEdge = R.color.ui_line_style_4_2_stroke_color
                    it.colorGuideLight =
                        if (lightMode) R.color.ui_line_style_4_2_stroke_color else R.color.white
                }
            }
            val toColor = shapeView.toColor(it.colorBottomEdge)
            it.paintShapeBackground.color = toColor
            it.yGuidelineColor[0] = it.colorGuideLight
            it.yGuidelineColor[1] = it.colorGuideLight
            it.yGuidelineColor[2] = it.colorGuideLight
            it.yProjectionColor[0] = it.colorProjection
            it.yProjectionColor[1] = it.colorProjection
            it.yProjectionColor[2] = it.colorProjection
        }
    }

    override fun getType(): Type = Type.Check

}