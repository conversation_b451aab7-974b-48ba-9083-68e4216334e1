package com.govee.cubeview.reconfigure.shape.gamewall

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.shape.BusinessShapeView
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import kotlin.math.PI
import kotlin.math.ceil
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2023/11/28
 *     version : 1.0.0
 *     desc    : 小三角形
 */
class GameTriangleView(context: Context) : BusinessShapeView(context) {
    private var lastNotePosition: Int = -1

    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)
        val shapePosition = shapeData.shapePosition

        // 如果主电源有赋值，则按照辅助电源形式显示
        shapePosition.mainPower?.let {
            mainPower.powerEdgeNUmber
            mainPower.powerNumber = it.powerNumber
            mainPower.powerEdgeNUmber = it.powerEdgeNUmber
            mainPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMainPower = true
        }
        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }
        params.inputNum = shapePosition.inputNum
        params.outputNum = shapePosition.outputNum
        this.lastNotePosition = shapePosition.lastPosition
    }

    override fun getNextShapeCenterPointByRotation(
        pointF: PointF,
        length: Float,
        rotation: Float
    ): PointF {
        //showLog(TAG, "三角形下一个图形的长度 length = $length")
        return PointF().apply {
            val matrix = Matrix()
            val needRotation = needRotation(rotation)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 3) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 3) + pointF.y).toFloat()
                )
            matrix.setRotate(needRotation, pointF.x, pointF.y)
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun needRotation(rotation: Float): Float {
        /*该方法是为了把三角形挪正，默认多旋转30度*/
        return rotation + 30
    }

    override fun getShapePoint(
        centerPointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<PointF> {
        showLog(TAG, "getShapePoint() length = $length , rotation = $rotation")
        return Array(3) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(needRotation(rotation), centerPointF.x, centerPointF.y)

            val shapePoint = Array(3) { FloatArray(2) }.apply {
                for (i in indices) {
                    val angle = 2 * PI * i / 3
                    this[i] =
                        floatArrayOf(
                            centerPointF.x + (length * cos(angle)).toFloat(),
                            centerPointF.y + (length * sin(angle)).toFloat()
                        )
//                    if (i == 0) {
//                        this[0][0] = this[0][0] + 11
//                    }
                    matrix.mapPoints(this[i])
                }
            }

            forEachIndexed { index, pointF ->
                val floats = shapePoint[index]
                pointF.x = floats[0]
                pointF.y = floats[1]
            }
        }
    }

    override fun drawSelectedBorder(canvas: Canvas, paint: Paint) {
        // 三角形因为角的问题，需要减去 3才能显示 1px 的边界预览
        val shapeDefaultPoint =
            getShapeDefaultPoint(params.outsideLineLength - paint.strokeWidth / 2f - 3)
        baseShapePath.let {
            it.reset()
            shapeDefaultPoint.onEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)

                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            /*画路径*/
            it.transform(matrix)
            /*画背景色*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    override fun getNextShapePosition(edgeTag: Int): ShapePosition {
        return super.getNextShapePosition(3 - edgeTag)
    }

    override fun showSerialNumber(): Boolean {
        return false
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        // 奇数 300 ，偶数 = 60
        if (directionTag == 0) {
            return 180f
        }
        return if (directionTag % 2 == 0) {
            300f
        } else {
            60f
        }
    }

    override fun getPathShapeCenterPoints(): PointF {
        // 加 180 的原因：顶点为默认起始点，若需要按照视觉从下到上的角度，需要逆时针旋转180(顺时针旋转180)
        showLog(
            TAG,
            "getPathShapeCenterPoints:  getPathShapeCenterPoints= ${offsetRotation - params.defaultAngle + 180} "
        )
        return getNextShapeCenterPoints(
            offsetRotation - params.defaultAngle + 180,
            params.innerLineLength
        )
    }

    override fun getPathShapePathNum(lastOffsetRotation: Float): Int {
        return if ((lastOffsetRotation - offsetRotation).checkAngle().roundToInt() % 300 == 0) {
            2
        } else {
            1
        }
    }

    override fun toShapePosition(): ShapePosition {
        return ShapePosition(
            params.shapeType, ceil(centerPos.x), ceil(centerPos.y), getRotationValue(), 0, params.connectState
        ).apply {
            if (params.isMultiPowerSelected && !mMultiPower.isDefault()) {
                this.ext = mMultiPower
            }
            if (params.isMultiPowerSelected && !<EMAIL>()) {
                this.mainPower = <EMAIL>
            }
            this.editable = params.editable
            this.inputNum = params.inputNum
            this.outputNum = params.outputNum
            this.lastPosition = lastNotePosition
        }
    }
}