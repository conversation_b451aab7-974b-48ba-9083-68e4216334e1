package com.govee.cubeview.reconfigure.shape.data

import com.govee.cubeview.reconfigure.DataProcessCenter
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.showLog

/**
 *     author  : sinrow
 *     time    : 2022/11/14
 *     version : 1.0.0
 *     desc    : 图形的数据结构，基础图形包含父节点、子节点
 */
interface IBaseNote {

    fun putParentNode(parentNote: BaseShapeView)

    fun notifyParentNode(connectState: Int) // 更新父类状态，如：连接状态

    fun flip()

    fun checkState(): Int
}

interface IYBaseNote {
    fun putLeftNote(shapeView: BaseShapeView)

    fun putRightNote(shapeView: BaseShapeView)
}

/**
 * 图形的基础数据结构，用于处理图形与图形上下的关系
 * @see parentNote 父节点
 * @see subNote 子节点
 */
open class BaseNote : IBaseNote {

    var parentNote: BaseShapeView? = null
    var subNote: BaseShapeView? = null // 为了兼容一对多的情况，改为几个形式
    val subNotes: HashMap<BaseShapeView, Int> = hashMapOf()
    var graphShapes: MutableList<BaseShapeView> = mutableListOf()
    var selfShapeView: BaseShapeView? = null
    var position: Int = -1

    override fun putParentNode(parentNote: BaseShapeView) {
        this.parentNote = parentNote
    }

    override fun notifyParentNode(connectState: Int) {
        this.parentNote?.let {
            it.params.connectState = connectState
        }
    }

    override fun flip() {

    }

    override fun checkState(): Int {
        return subNote?.let { 1 } ?: 0
    }
}

/**
 * Y 形灯的数据结构，包含了左右子节点
 */
class YNote : BaseNote(), IYBaseNote {
    var leftNote: BaseShapeView? = null
    var rightNote: BaseShapeView? = null

    override fun putLeftNote(shapeView: BaseShapeView) {
        this.leftNote = shapeView
    }

    override fun putRightNote(shapeView: BaseShapeView) {
        this.rightNote = shapeView
    }

    override fun flip() {
        val tempLeft = leftNote
        val tempRight = rightNote
        leftNote = tempRight
        rightNote = tempLeft
    }

    override fun checkState(): Int {
        var state = 0
        if (leftNote != null) {
            state += 1
        }
        if (rightNote != null) {
            state += 2
        }
        return state
    }
}

class SpaceNote : BaseNote() {
    override fun checkState(): Int {
        // 根据图形中心算法
        val updateSpaceViewState = DataProcessCenter.updateSpaceViewState(selfShapeView, graphShapes)
        showLog("checkState: state = $updateSpaceViewState ")
        return updateSpaceViewState
    }
}

class GameNote : BaseNote() {
    override fun checkState(): Int {
        return 0
    }
}