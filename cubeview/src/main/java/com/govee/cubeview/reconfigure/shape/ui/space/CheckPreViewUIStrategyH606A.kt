package com.govee.cubeview.reconfigure.shape.ui.space

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.Type

/**
 *     author  : sinrow
 *     time    : 2023/6/8
 *     version : 1.0.0
 *     desc    :
 */
class CheckPreViewUIStrategyH606A(private val shapeView: BaseShapeView) : InstallUIStrategyH606A(shapeView) {
    override fun getType(): Type = Type.CheckPre
}