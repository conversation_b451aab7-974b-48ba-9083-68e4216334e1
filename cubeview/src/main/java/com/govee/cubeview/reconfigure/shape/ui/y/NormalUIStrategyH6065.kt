package com.govee.cubeview.reconfigure.shape.ui.y

import android.content.Context
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.NormalUIStrategy
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/11/17
 *     version : 1.0.0
 *     desc    :
 */
open class NormalUIStrategyH6065(context: Context) : NormalUIStrategy(context) {

    override fun updateState(params: BaseShapeParams, state: Int) {
        params.let {
            val toColor = context.toColor(it.colorBottomEdge)
            it.paintShapeBackground.color = toColor
            it.yGuidelineColor[0] = it.colorGuideLight
            it.yGuidelineColor[1] = it.colorGuideLight
            it.yGuidelineColor[2] = it.colorGuideLight
        }
    }
}