package com.govee.cubeview.reconfigure

import android.os.Build
import com.govee.cubeview.Utils
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.assist.ImgPowerShapeView
import com.govee.cubeview.shape.Shape.useSoftLineNum
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import kotlin.math.abs

/**
 *     author  : sinrow
 *     time    : 2023/4/27
 *     version : 1.0.0
 *     desc    :    方块灯
 */
internal object DataProcessCenter {

//    fun translateSpaceConnectState(state: Int): Int {
//        if (state == 0) return state
//        Utils.isPointInPolygon()
//    }
//
//    /**
//     *
//     * 第一个表示输入边状态，第二个表示输出边状态
//     */
//    fun translateSpaceConnectState(state: Int): IntArray {
//        val signedBytesFor2 = Utils.getSignedBytesFor2(state, false)
//        val inputState = Utils.byte2Int(signedBytesFor2[0])
//        val outputState = Utils.byte2Int(signedBytesFor2[1])
//        return intArrayOf(inputState, outputState)
//    }
//
//    /**
//     * 转换成链接状态
//     */
//    fun translateSpaceConnectState(inputStream: Int, outputStream: Int): Int {
//        return Utils.getSignedInt(byteArrayOf(inputStream.toByte(), outputStream.toByte()), false)
//    }

    private var sortIndex = 1


    /**
     * 用于 606A 选择辅助电源后，重新排序但前的输入输出边
     */
    fun reBuildSpaceViewShapes(baseShapeViews: MutableList<BaseShapeView>): MutableList<BaseShapeView> {
        if (baseShapeViews.isEmpty()) return baseShapeViews
        var firstShapeView: BaseShapeView? = null
//        baseShapeViews.onEach {
//            showLog("前-转换的连接状态 inputNum = ${it.params.inputNum} ,outputNum = ${it.params.outputNum} ")
//        }
        baseShapeViews.forEachIndexed { index, it ->
            it.sortIndex = 0
            it.params.inputNum = 0
            it.params.outputNum = 0
            if (it.params.isMainPower) {
                it.mMultiPower.copyThis(it.mainPower)
                it.params.inputNum = makeEdgeNum(it.mainPower.powerEdgeNUmber)// 要转成 bit
                firstShapeView = it
                //showLog("firstShapeView = $it")
                showLog("递归遍历开始", "reBuildSpaceViewShapes() index = $index --------------------------------")
            }

        }
        sortIndex = 1
        firstShapeView?.let {
            it.sortIndex = sortIndex
            doRecursionShape(baseShapeViews, it)
            baseShapeViews.sortBy { view -> view.sortIndex }
        }
//        baseShapeViews.onEach {
//            showLog("后-转换的连接状态 sortIndex = ${it.sortIndex} , inputNum = ${it.params.inputNum} ,outputNum = ${it.params.outputNum} ")
//        }
        return baseShapeViews
    }

    // 递归遍历图形
    private fun doRecursionShape(baseShapeViews: MutableList<BaseShapeView>, baseShapeView: BaseShapeView) {
        val inputNum = baseShapeView.params.inputNumTemp
        showLog("递归遍历开始", "doRecursionShape() index = $sortIndex , 入边 = $inputNum , baseShapeView = $baseShapeView")
        val list = mutableListOf<Pair<Int, BaseShapeView>>()
        val editable = baseShapeView.params.editable
        for (i in 0..5) {// 遍历六条边
            val nextShapePosition = baseShapeView.getNextShapePosition(i)
            checkNearShapeView(baseShapeViews, nextShapePosition)?.let { nearShapeView ->
                if (nearShapeView.sortIndex == 0) {// 证明没有排序
                    val check = if (editable) {
                        // 可编辑的下一个必须是可编辑的
                        nearShapeView.params.editable
                    } else {
                        // 上一个是不可编辑的，那么下一个就随便了
                        true
                    }
                    showLog("递归遍历开始", "doRecursionShape() check = $check 第几条边 = $i")
                    if (nearShapeView.params.inputNum == 0 && check) {// 证明该块图形没有被接入
                        list.add(Pair(i, nearShapeView))
                        showLog("递归遍历开始", "doRecursionShape()  此出边的块临时无接入 = $i")
                    }
                }
            }
        }
        if (list.isNotEmpty()) {
            var check = true
            showLog("递归遍历开始", "doRecursionShape()  当前块是否可编辑 $editable")
            if (editable) {
                // 当前块可编辑的情况下执行以下逻辑
                val size = list.filter { it.second.params.editable }.size // 过滤得到可以编辑的图形
                // 得到之后，判断集合当中有一个是 true 的，如果是包含了多个的话，那么就先多出，如果不是则单出？这样的话就只有加装才会出现的逻辑了
                showLog("递归遍历开始", "doRecursionShape() 有多少个出边 $size")
                if (size > 1) {
                    check = false
                    list.forEach {
                        val makeEdgeNum = makeEdgeNum(it.first) + baseShapeView.params.outputNum
                        baseShapeView.params.outputNum = makeEdgeNum // 输出边
                        val subAngle = (baseShapeView.offsetRotation - it.second.offsetRotation).checkAngle()
                        val subIndex = subAngle / 60
                        val fixIndex = ((it.first + 3 + subIndex) % 6).toInt() //
                        it.second.params.inputNum = makeEdgeNum(fixIndex)
                        it.second.params.inputNumTemp = fixIndex
                        if (it.second.params.isMultiPowerSelected) {
                            val edgeNum = makeEdgeNum(it.second.mMultiPower.powerEdgeNUmber)
                            it.second.params.inputNum += edgeNum
                        }
                    }
                    list.forEach {
                        it.second.sortIndex = sortIndex++
                        doRecursionShape(baseShapeViews, it.second) // 递归遍历
                    }
                }
            }
            showLog("递归遍历开始", "doRecursionShape(),子递归前判断 $check")
            if (check) {
                list.forEach {
                    // 在遍历完一个之后，再遍历下一个
                    val enable = it.second.params.inputNum == 0
                    showLog("递归遍历开始", "doRecursionShape() 此边是否可编辑 = $enable")
                    if (enable) {
                        val makeEdgeNum = makeEdgeNum(it.first) + baseShapeView.params.outputNum
                        baseShapeView.params.outputNum = makeEdgeNum // 输出边
                        val subAngle = (baseShapeView.offsetRotation - it.second.offsetRotation).checkAngle()
                        val subIndex = subAngle / 60
                        val fixIndex = ((it.first + 3 + subIndex) % 6).toInt() //
                        it.second.params.inputNum = makeEdgeNum(fixIndex)
                        it.second.params.inputNumTemp = fixIndex
                        if (it.second.params.isMultiPowerSelected) {
                            val edgeNum = makeEdgeNum(it.second.mMultiPower.powerEdgeNUmber)
                            it.second.params.inputNum += edgeNum
                            it.second.params.inputNum += it.second.mMultiPower.powerEdgeNUmber
                        }
                        it.second.sortIndex = sortIndex++
                        doRecursionShape(baseShapeViews, it.second) // 递归遍历
                    }
                }
            }
        }
    }

    /**
     * 根据
     */
    fun checkNearShapeView(baseShapeViews: MutableList<BaseShapeView>, nextShapePosition: ShapePosition): BaseShapeView? {
        val diff = 2
        baseShapeViews.onEach {
            val centerPos = it.centerPos
            if (abs(centerPos.x - nextShapePosition.x) < diff && abs(centerPos.y - nextShapePosition.y) < diff) {
                return it
            }
        }
        return null
    }

    private fun makeEdgeNum(edgeNUmber: Int): Int {
        return Utils.makeSelectByOneBit(BooleanArray(8).apply {
            val coerceAtMost = (edgeNUmber).coerceAtLeast(0).coerceAtMost(7)
            this[coerceAtMost] = true
        }).toInt()
    }

    /**
     * return 0：未连接,可删除
     *        1：已连接，不能删除
     */
    fun updateSpaceViewState(selfShapeView: BaseShapeView?, baseShapeViews: MutableList<BaseShapeView>): Int {
        if (selfShapeView == null) return 0
        val size = baseShapeViews.size - 1 // 列表数据
        if (size == 0) return 1
        var firstShapeView: BaseShapeView? = null
        val toMutableList = baseShapeViews
            .asSequence()
            .filter { it != selfShapeView }.toMutableList().apply {
                onEachIndexed { index, baseShapeView ->
                    baseShapeView.sortIndex = 0
                    baseShapeView.params.inputNum = 0
                    baseShapeView.params.outputNum = 0
                    if (index == 0) {
                        baseShapeView.params.isMultiPowerSelected = true
//                        baseShapeView.mMultiPower.powerNumber = 1
                        baseShapeView.params.inputNum = makeEdgeNum(baseShapeView.mMultiPower.powerEdgeNUmber)// 要转成 bit
                        firstShapeView = baseShapeView
                    }
                }
            }
        if (firstShapeView == null) return 1

        doCheckShapeConnectedState(toMutableList, firstShapeView!!)

        val conformSize = toMutableList.filter { it.sortIndex != 0 }.size
        return if (size == conformSize) 0 else 1
    }

    private fun doCheckShapeConnectedState(baseShapeViews: MutableList<BaseShapeView>, baseShapeView: BaseShapeView) {
        baseShapeView.sortIndex = 1
        for (i in 0..5) {// 遍历六条边
            val nextShapePosition = baseShapeView.getNextShapePosition(i)
            val checkNearShapeView = checkNearShapeView(baseShapeViews, nextShapePosition)
            checkNearShapeView?.let { nearShapeView ->
                if (nearShapeView.sortIndex == 0) {// 证明没有排序
                    if (nearShapeView.params.inputNum == 0) {// 证明该块图形没有被接入
                        val makeEdgeNum = makeEdgeNum(i) + baseShapeView.params.outputNum
                        baseShapeView.params.outputNum = makeEdgeNum // 输出边
                        val subAngle = (baseShapeView.offsetRotation - nearShapeView.offsetRotation).checkAngle()
                        val subIndex = subAngle / 60
                        val fixIndex = ((i + 3 + subIndex) % 6).toInt() //
                        nearShapeView.params.inputNum = makeEdgeNum(fixIndex)
                        doCheckShapeConnectedState(baseShapeViews, nearShapeView) // 递归遍历
                    }
                }
            }
        }
    }

    internal fun BaseShapeView.isContainer(mutableList: MutableList<ShapePosition>): Boolean {
        val diff = 2
        mutableList.onEach {
            if (abs(centerPos.x - it.x) < diff && abs(centerPos.y - it.y) < diff) {
                return true
            }
        }
        return false
    }

    internal fun MutableList<Pair<BaseShapeView, ImgPowerShapeView>>.checkContainer(baseShapeView: BaseShapeView): Boolean {
        forEach {
            if (it.first == baseShapeView) {
                return true
            }
        }
        return false
    }


    /**
     * 计算第二电源合理位置
     *
     * @param sortHashMap 每一块对应的连接关系
     * @param shapes 当前图形的连接关系
     */
    fun calculateSupply2Location(
        sortHashMap: HashMap<BaseShapeView, BaseShapeView>,
        shapes: ArrayList<ShapePosition>
    ): ArrayList<Int> {
        showLog("calculateSupply2Location() 开始计算第二电源位置")
        val map = HashMap<BaseShapeView, MutableList<BaseShapeView>>()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            sortHashMap.forEach { (t, u) ->
                mutableListOf<BaseShapeView>().apply {
                    add(t)// 添加本身
                    add(u)// 添加上一个
                    findParentShapeView(this, sortHashMap, u)
                }.let {
                    map[t] = it
                    showLog("当前位置 ${t.note.position} , 父类关系 ${it.map { it.note.position }}")
                }
            }
        } else {
            return arrayListOf()
        }

        val maxSize = map.values.maxOfOrNull { it.size } ?: return arrayListOf()

        val location = arrayListOf<Int>()

        map.values
            .filter { it.size == maxSize }
            .takeIf { it.isNotEmpty() }?.forEach {
                val checkShapeIsEnable =
                    checkShapeIsEnable(it.filterIndexed { index, _ -> index < maxSize / 2 }.toMutableList(), shapes)
                if (checkShapeIsEnable != null) {
                    showLog("检查第二电源----- 最终确认当前的最佳位置是 ${checkShapeIsEnable.note.position}")
                    location.add(checkShapeIsEnable.note.position)
                }
            }

        map.clear()
        return location
    }

    /**
     * 检查当前块是否满足要求(是否可接入第二电源)
     * @param checkListShapeView 最深路径的1/2（向下取整），从最远端开始检查，此集合是倒序（从远到近）
     * @param shapes 当前图形的连接关系
     */
    private fun checkShapeIsEnable(
        checkListShapeView: MutableList<BaseShapeView>,
        shapes: ArrayList<ShapePosition>
    ): BaseShapeView? {
        // 当前块可用
        checkListShapeView.removeFirstOrNull()?.let {
            // 判断边是否有可接入电源的边
            val nearShape = it.isNearShape(shapes)
            if (nearShape == 4) {
                return checkShapeIsEnable(checkListShapeView, shapes)
            } else {
                return it
            }
        }
        return null
    }

    /**
     * @param new 当前块的上一块的集合
     * @param old 每一块对应的上一块 map 集合 key：当前块；value：上一块
     * @param curShapeView 当前块
     */
    private fun findParentShapeView(
        new: MutableList<BaseShapeView>,
        old: HashMap<BaseShapeView, BaseShapeView>,
        curShapeView: BaseShapeView
    ) {
        old[curShapeView]?.let {
            new.add(it)
            // 找得到就继续找
            findParentShapeView(new, old, it)
        }
    }

    /**
     * 判断此块周边有多少个相邻的块
     */
    private fun BaseShapeView.isNearShape(mutableList: MutableList<ShapePosition>, diff: Int = 2): Int {
        var count = 0
        val hasNotSupportShape = mutableListOf<ShapePosition>()
        // 符合多电源选择
        // 第一个显示规则
        for (i in 0..3) {
            val nextShapePosition = getNextShapePosition(i)
            hasNotSupportShape.add(nextShapePosition)
        }
        mutableList.onEachIndexed { index, shapePosition ->
            hasNotSupportShape.forEach {
                if (abs(it.x - shapePosition.x) < diff && abs(it.y - shapePosition.y) < diff) {
                    count++
                    showLog(
                        "isNearShape = $index ,count = $count, shapePosition = ${abs(centerPos.x - shapePosition.x)} , ${
                            abs(
                                centerPos.y - shapePosition.y
                            )
                        }"
                    )
                }
            }
        }
        return count
    }

    /**
     * 计算软线位置区分区域逻辑，返回 shapes 集合中，每一块所在的区域区分，用 List<Int> 结果返回；
     */
    fun calculateSoftLocation4Area(shapes: ArrayList<ShapePosition>, sortHashMap: HashMap<BaseShapeView, BaseShapeView>): List<Int> {
        //showLog("=== 两阶段区域分组开始 ===")

        // 第一阶段：完整递归遍历所有物理连接，忽略软线，得到基础连通区域
        val baseRegionIds = MutableList(shapes.size) { -1 }
        val visited = mutableSetOf<Int>()
        var baseRegionCount = 0

        // 按 position 排序，保证递归入口顺序稳定
        val allShapeViews =
            (sortHashMap.keys + sortHashMap.values).toSet().sortedBy { it.note.position }
        //showLog("第一阶段：完整递归遍历，总共 ${allShapeViews.size} 个 BaseShapeView")

        // 第一阶段递归函数：忽略软线，完整遍历物理连接
        fun dfsIgnoreSoftLine(shape: BaseShapeView, region: Int) {
            val idx = shape.note.position
            if (visited.contains(idx)) return
            visited.add(idx)
            if (idx < baseRegionIds.size) {
                baseRegionIds[idx] = region
            }
            showLog("  第一阶段递归: position=$idx -> baseRegion=$region")

            // 递归所有物理连接，忽略软线
            val neighbors = getAllConnectedShapes(shape, sortHashMap)
            for (neighbor in neighbors) {
                dfsIgnoreSoftLine(neighbor, region)
            }
        }

        // 第一阶段递归入口
        for (shape in allShapeViews) {
            val pos = shape.note.position
            if (!visited.contains(pos)) {
                showLog("第一阶段递归入口: position=$pos, baseRegion=$baseRegionCount")
                dfsIgnoreSoftLine(shape, baseRegionCount)
                baseRegionCount++
            }
        }

        //showLog("第一阶段完成，基础连通区域数：$baseRegionCount")
        //showLog("基础 baseRegionIds: $baseRegionIds")

        // 第二阶段：根据软线位置，将连通区域进行切分
        val finalRegionIds = baseRegionIds.toMutableList()
        val softLinePositions = useSoftLineNum.toSet()
        var finalRegionCount = 0
        val processedBaseRegions = mutableSetOf<Int>()

        //showLog("第二阶段：根据软线切分，软线位置: $softLinePositions")

        // 遍历每个基础连通区域，根据软线切分
        for (baseRegion in 0 until baseRegionCount) {
            if (processedBaseRegions.contains(baseRegion)) continue

            // 找到当前基础区域的所有 position
            val positionsInBaseRegion = mutableListOf<Int>()
            baseRegionIds.forEachIndexed { idx, region ->
                if (region == baseRegion) {
                    positionsInBaseRegion.add(idx)
                }
            }

            showLog("处理基础区域 $baseRegion，包含 positions: $positionsInBaseRegion")

            // 在当前基础区域内，根据软线进行二次分组
            val subVisited = mutableSetOf<Int>()

            fun dfsWithSoftLineBreak(position: Int, finalRegion: Int) {
                if (subVisited.contains(position)) return
                subVisited.add(position)
                finalRegionIds[position] = finalRegion
                //showLog("    二次分组: position=$position -> finalRegion=$finalRegion")

                // 查找当前 position 的物理连接，遇到软线断开
                allShapeViews.find { it.note.position == position }?.let { shape ->
                    val neighbors = getAllConnectedShapes(shape, sortHashMap)
                    for (neighbor in neighbors) {
                        val neighborPos = neighbor.note.position
                        // 只处理同一基础区域内的连接，且遇到软线断开
                        if (positionsInBaseRegion.contains(neighborPos) &&
                            !softLinePositions.contains(neighborPos)
                        ) {
                            dfsWithSoftLineBreak(neighborPos, finalRegion)
                        }
                    }
                }
            }

            // 对当前基础区域进行二次分组
            for (position in positionsInBaseRegion) {
                if (!subVisited.contains(position)) {
                    //showLog("  二次分组入口: position=$position, finalRegion=$finalRegionCount")
                    dfsWithSoftLineBreak(position, finalRegionCount)
                    finalRegionCount++
                }
            }

            processedBaseRegions.add(baseRegion)
        }

        //showLog("第二阶段完成，最终区域数：$finalRegionCount")
        //showLog("最终 finalRegionIds: $finalRegionIds")

        // shapes 的索引 i 对应 BaseShapeView.note.position = i，所以直接返回 finalRegionIds
        //showLog("=== 两阶段区域分组结束，结果: $finalRegionIds ===")
        return finalRegionIds
    }

    // 2. 辅助函数：获取所有与当前 shape 物理连接的 shape（正反向）
    private fun getAllConnectedShapes(
        shape: BaseShapeView,
        sortHashMap: HashMap<BaseShapeView, BaseShapeView>
    ): List<BaseShapeView> {
        val result = mutableListOf<BaseShapeView>()
        // 正向：child -> parent
        sortHashMap[shape]?.let { parent -> result.add(parent) }
        // 反向：parent -> child
        sortHashMap.forEach { (child, parent) ->
            if (parent === shape) result.add(child)
        }
        return result
    }

}