package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.YView
import com.govee.cubeview.reconfigure.shape.assist.BaseFocusView
import com.govee.cubeview.reconfigure.shape.assist.DynamicImgPathShapeView
import com.govee.cubeview.reconfigure.shape.assist.FocusPointView
import com.govee.cubeview.reconfigure.shape.assist.ImgPathShapeView
import com.govee.cubeview.reconfigure.shape.data.BaseNote
import com.govee.cubeview.reconfigure.shape.data.YNote
import com.govee.cubeview.reconfigure.shape.data.YShapeTree
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.reconfigure.shape.ui.YShapeParams
import com.govee.cubeview.reconfigure.shape.ui.y.*
import com.govee.cubeview.shape.ShapePosition

/**
 *     author  : sinrow
 *     time    : 2022/10/9
 *     version : 1.0.0
 *     desc    : Y形灯 --- H6065
 */
class YConfig : AbsShapeConfig() {

    override fun configShapeParams(): BaseShapeParams {
        return YShapeParams()
    }

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {
        return super.configUIStrategy(shapeView).apply {
            NormalUIStrategyH6065(shapeView.context).inject(this)
            PreViewUIStrategyH6065(shapeView.context).inject(this)
            MultiPowerStrategyH6065(shapeView.context).inject(this)
            InstallUIStrategyH6065(shapeView).inject(this)
            CheckUIStrategyH6065(shapeView).inject(this)
            MoveFeastUIStrategyH6065(shapeView).inject(this)
        }
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        return YView(context).apply { setConfig(this@YConfig) }
    }

    override fun createPathView(context: Context): ImgPathShapeView {
        return DynamicImgPathShapeView(context)
    }

    override fun configNote(): BaseNote {
        return YNote()
    }

    val mYShapeTree by lazy { YShapeTree() }

    override fun outputAfterConvertShapeData(shapeViews: MutableList<BaseShapeView>): ArrayList<ShapePosition> {
        return mYShapeTree
            .apply { reset() }
            .apply {
                shapeViews.forEach {
                    insert(it.note.parentNote, it)
                }
            }.ergodicMid()
    }

    override fun inputBeforeConvertShapeData(shapePosition: ArrayList<ShapePosition>): ArrayList<ShapePosition> {
        mYShapeTree.reset()
        return shapePosition.onEachIndexed { index, position ->
            mYShapeTree.insert(index, position)
            if (index != 0) {
                val findLastShape = mYShapeTree.findLastShape(index)
                position.lastPosition = findLastShape
            }
        }
    }

    override fun createFocusView(context: Context): BaseFocusView {
        return FocusPointView(context)
    }
}