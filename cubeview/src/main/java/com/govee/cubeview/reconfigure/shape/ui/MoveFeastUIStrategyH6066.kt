package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/12/9
 *     version : 1.0.0
 *     desc    :
 */
class MoveFeastUIStrategyH6066(shapeView: BaseShapeView) : MoveFeastUIStrategy(shapeView) {

    override fun updateState(params: BaseShapeParams, state: Int) {
        super.updateState(params, state)
        params.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFFFFFFF_30)
    }
}