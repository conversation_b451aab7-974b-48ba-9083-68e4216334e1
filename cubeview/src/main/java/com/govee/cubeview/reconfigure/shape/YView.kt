package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.checkAngle
import com.govee.cubeview.isLightMode
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.reconfigure.shape.data.YNote
import com.govee.cubeview.reconfigure.shape.ui.toSetFloatArray
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor
import com.govee.ui.R
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.sin
import kotlin.math.sqrt

/**
 *     author  : sinrow
 *     time    : 2022/9/8
 *     version : 1.0.0
 *     desc    :
 */
class YView(context: Context) : BusinessShapeView(context) {

    private var diffX = 8f// 宽度

    /*模糊遮罩滤镜，用于模拟灯光泛光色*/
    private val mBlurMaskFilter by lazy { BlurMaskFilter(18f, BlurMaskFilter.Blur.NORMAL) }

    override fun getNextShapeCenterPointByRotation(
        pointF: PointF,
        length: Float,
        rotation: Float
    ): PointF {
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 3) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 3) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(
        centerPointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<PointF> {
        val matrix = Matrix()//旋转
        matrix.setRotate(needRotation(rotation), centerPointF.x, centerPointF.y)

        val shapePoint = Array(9) { FloatArray(2) }

        for (i in 0..2) {
            shapePoint[i] =
                floatArrayOf(
                    centerPointF.x + (length * cos(2 * PI * i / 3)).toFloat(),
                    centerPointF.y + (length * sin(2 * PI * i / 3)).toFloat()
                )
            if (!forSelf) {
                matrix.mapPoints(shapePoint[i])
            }
        }
        if (!forSelf) {
            return Array(3) { PointF() }.apply {
                toSetFloatArray(shapePoint.filterIndexed { index, _ -> index < 3 }
                    .toTypedArray())
            }
        }

        val point0 = shapePoint[0]
        val point1 = shapePoint[2]
        val point2 = shapePoint[1]

        val a1 = PointF(point0[0], point0[1] + diffX)/*左上角顶点*/
        val a2 = PointF(point0[0], point0[1] - diffX)/*左下角底点*/


        val a3 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y - diffX)/*中心左边点*/

        val a4 = PointF(point1[0] + diffX * sqrt(3f) / 2, point1[1] - diffX / 2)/*底部左边点*/
        val a5 = PointF(point1[0] - diffX * sqrt(3f) / 2, point1[1] + diffX / 2)/*底部右边点*/

        val a6 = PointF(centerPointF.x - diffX * 2 / sqrt(3f), centerPointF.y)/*中心右边点*/

        val a7 = PointF(point2[0] - diffX * sqrt(3f) / 2, point2[1] - diffX / 2)/*右上角底点*/

        val a8 = PointF(point2[0] + diffX * sqrt(3f) / 2, point2[1] + diffX / 2)/*右上角顶点*/

        val a9 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y + diffX)/*中心顶点*/

        shapePoint[0] = floatArrayOf(a1.x, a1.y)
        shapePoint[1] = floatArrayOf(a2.x, a2.y)
        shapePoint[2] = floatArrayOf(a3.x, a3.y)
        shapePoint[3] = floatArrayOf(a4.x, a4.y)
        shapePoint[4] = floatArrayOf(a5.x, a5.y)
        shapePoint[5] = floatArrayOf(a6.x, a6.y)
        shapePoint[6] = floatArrayOf(a7.x, a7.y)
        shapePoint[7] = floatArrayOf(a8.x, a8.y)
        shapePoint[8] = floatArrayOf(a9.x, a9.y)

        shapePoint.forEach {
            matrix.mapPoints(it)
        }

        return Array(9) { PointF() }.apply {
            toSetFloatArray(shapePoint)
        }
    }

    override fun needRotation(rotation: Float): Float {
        /*该方法是为了把三角形挪正，默认多旋转30度,Y 形灯头朝下，需要旋转 180 度*/
        return rotation + 30f
    }

    override fun showSerialNumber(): Boolean {
        return modeType == CanvasLayoutModeType.Check && shapeState.state == 0
            || modeType == CanvasLayoutModeType.EditPre
            || modeType == CanvasLayoutModeType.ColorMode
    }

    override fun drawCenterText(canvas: Canvas, paint: Paint) {

        if (modeType == CanvasLayoutModeType.Check && shapeState.state != 0) return

        if (modeType == CanvasLayoutModeType.Check || modeType == CanvasLayoutModeType.EditPre || modeType == CanvasLayoutModeType.MoveFeast) {
            val textBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            /*若为白天模式则使用图形背景色，若为暗夜模式则使用固定的背景色*/
            var color =
                if (context.isLightMode()) params.shapeColor else toColor(R.color.ui_line_style_4_4_stroke_color)
            var radius = params.textBgRadius
            if (modeType == CanvasLayoutModeType.MoveFeast) {
                color = toColor(moveFeastData.color)
                radius = 20f
            }

            textBgPaint.color = color

            textBgPaint.style = Paint.Style.FILL_AND_STROKE
            textBgPaint.textSize =
                Utils.getDimensionPixelSize(context, R.dimen.font_style_185_1_textSize).toFloat()

            canvas.drawCircle(width / 2f, height / 2f, radius, textBgPaint)
        }
        super.drawCenterText(canvas, paint)
    }

    override fun drawBackground(canvas: Canvas, paint: Paint) {
        diffX = 9f
        super.drawBackground(canvas, paint)
    }

    override fun onDraw(canvas: Canvas) {
        canvas?.run {
            /* 图形绘制流程*/
            // 1、绘制基本图形(背景+边框)
            // 2、绘制辅助参数
            // 3、绘制中心文本内容(用于序号，颜色亮度等)

            drawBaseShape(this, params.paintBaseShape)
            if (params.editable) {
                val centerText = params.centerText
                if (centerText.isNotEmpty() && params.visibleText) {
                    drawCenterText(this, params.paintCenterText)
                }
                if (modeType == CanvasLayoutModeType.MoveFeast) {
                    drawCenterDrawBitmap(canvas, params.paintCenterDrawable)
                }
                if (isSelected) {
                    // 当前为选中状态，则绘制选择框（图形可自己处理）
                    drawSelectedBorder(this, params.paintSelectedBorder)
                }
            }
        }
    }

    override fun drawBaseShape(canvas: Canvas, paint: Paint) {
        // 基础图形分为四层图形
        // 投影 ，发光片，用于灯亮的时候，灯边模糊遮罩过滤
        // 底边，图形基础边
        // 金属片，中间固定不变的图形框
        // 导光线，随则底部颜色变化而变化
        val editable = params.editable
        if (editable) {
            drawProjection(canvas)
            drawBackground(canvas, params.paintShapeBackground)
            drawSheetMetal(canvas)
            drawGuideLight(canvas)
        } else {
            params.paintShapeBackground.color = toColor(R.color.FFE6E6E6)
            drawBackground(canvas, params.paintShapeBackground)
            alpha = 1.0f
        }
    }

    /* 投影 --- 模糊遮罩滤镜*/
    private fun drawProjection(canvas: Canvas) {
        if (!(modeType.isCheckMode() && shapeState.state != 0)/*校验模式下，未校验不显示投影*/
            && !(shapeState.type.isInstallType() && shapeState.state == 2)/*安装模式下，已经校验不显示*/
            && !modeType.isColorMode()/*颜色模式显示*/
        ) return

        diffX = 9f
        val shapePoint = getShapeDefaultPoint(params.outsideLineLength - 8)
        // 重写图形

        val rect = Array(4) { PointF() }
        rect[0] = shapePoint[0]
        rect[1] = shapePoint[1]
        rect[2] = shapePoint[2]
        rect[3] = shapePoint[8]

        var color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yProjectionColor[0])

        drawRectView(rect, canvas, color)

        rect[0] = shapePoint[2]
        rect[1] = shapePoint[3]
        rect[2] = shapePoint[4]
        rect[3] = shapePoint[5]

        color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yProjectionColor[1])

        drawRectView(rect, canvas, color)

        rect[0] = shapePoint[5]
        rect[1] = shapePoint[6]
        rect[2] = shapePoint[7]
        rect[3] = shapePoint[8]
        color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yProjectionColor[2])

        drawRectView(rect, canvas, color)

    }

    private fun drawRectView(
        shapePoint: Array<PointF>,
        canvas: Canvas,
        color: Int
    ) {
        baseShapePath.let {
            it.reset()
            shapePoint.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)
                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = color
            paint.style = Paint.Style.FILL
            paint.maskFilter = mBlurMaskFilter

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    /*金属片 - 常驻，颜色固定不变 */
    private fun drawSheetMetal(canvas: Canvas) {
        diffX = 5f
        val shapeDefaultPoint = getShapeDefaultPoint()
        // 重写图形
        baseShapePath.let {
            it.reset()
            shapeDefaultPoint.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)
                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = toColor(params.colorSheetMetal)
            paint.style = Paint.Style.FILL

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    private fun drawGuideLight(canvas: Canvas) {
        diffX = 9f
        val length = params.outsideLineLength - 4
        val centerPointF = PointF((width / 2).toFloat(), (height / 2).toFloat())// 中心点

        val matrix = Matrix()//旋转
        matrix.setRotate(needRotation(offsetRotation), centerPointF.x, centerPointF.y)

        val shapePoint = Array(6) { FloatArray(2) }

        for (i in 0..2) {
            shapePoint[i] =
                floatArrayOf(
                    centerPointF.x + (length * cos(2 * PI * i / 3)).toFloat(),
                    centerPointF.y + (length * sin(2 * PI * i / 3)).toFloat()
                )
        }


        val point0 = shapePoint[0]
        val point1 = shapePoint[2]
        val point2 = shapePoint[1]


        val a1 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y)
        val a2 = PointF(centerPointF.x - diffX / (2 * sqrt(3f)), centerPointF.y - diffX / 2)
        val a3 = PointF(centerPointF.x - diffX / (2 * sqrt(3f)), centerPointF.y + diffX / 2)

        shapePoint[0] = floatArrayOf(point0[0], point0[1])

        shapePoint[1] = floatArrayOf(a1.x, a1.y)

        shapePoint[2] = floatArrayOf(point1[0], point1[1])

        shapePoint[3] = floatArrayOf(a2.x, a2.y)

        shapePoint[4] = floatArrayOf(point2[0], point2[1])

        shapePoint[5] = floatArrayOf(a3.x, a3.y)


        shapePoint.forEach {
            matrix.mapPoints(it)
        }

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.strokeWidth = 2f

        var color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yGuidelineColor[0])

        paint.color = color

        canvas.drawLine(
            shapePoint[0][0],
            shapePoint[0][1],
            shapePoint[1][0],
            shapePoint[1][1],
            paint
        )
        color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yGuidelineColor[1])

        paint.color = color

        canvas.drawLine(
            shapePoint[2][0],
            shapePoint[2][1],
            shapePoint[3][0],
            shapePoint[3][1],
            paint
        )
        color =
            if (modeType.isColorMode()) params.shapeColor else toColor(params.yGuidelineColor[2])
        paint.color = color

        canvas.drawLine(
            shapePoint[4][0],
            shapePoint[4][1],
            shapePoint[5][0],
            shapePoint[5][1],
            paint
        )
    }

    override fun drawSelectedBorder(canvas: Canvas, paint: Paint) {

        val min = min(width, height).coerceAtLeast(80)

        val minX = (width - min) / 2f
        val minY = (height - min) / 2f
        val maxX = (width + min) / 2f
        val maxY = (height + min) / 2f

        val rect = Array(4) { FloatArray(2) }

        rect[0] = floatArrayOf(minX, minY)
        rect[1] = floatArrayOf(minX, maxY)
        rect[2] = floatArrayOf(maxX, maxY)
        rect[3] = floatArrayOf(maxX, minY)


        baseShapePath.let {
            it.reset()
            rect.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF[0], pointF[1])
                } else {
                    it.lineTo(pointF[0], pointF[1])
                }
            }
            it.close()

            paint.color = toColor(R.color.ui_rect_style_2_stroke)
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 2f

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    override fun getPathEntity(lastShapeView: BaseShapeView?): PathEntity? {
        lastShapeView?.let {
            val overRotation = 20f + offsetRotation
            val shapePoint = getShapePoint(centerPos, params.outsideLineLength, overRotation, false)

            val centerPoint = shapePoint[2]

            val start =
                getShapePoint(centerPos, params.outsideLineLength - 20, offsetRotation + 30, false)
            val end =
                getShapePoint(centerPos, params.outsideLineLength + 20, offsetRotation + 13, false)

            return PathEntity().apply {
                this.pathNum = -1
                this.rotation = offsetRotation + 180
                this.centerPointF = centerPoint
                this.startShapePoint.set(start[2])
                this.endShapePoint.set(end[2])
            }
        }
        return null
    }

    override fun getNextShapePosition(edgeTag: Int): ShapePosition {
        val type = params.shapeType
        val defRotation = Constants.getShapeDefaultAngle(params.shapeType)/*旋转角度默认参数*/

        val rotationByDirectionTag = getRotationByDirectionTag(edgeTag) // directionTag * 60f
        val nextRotation =
            // directionTag * 60f + offsetRotation = next.offsetRotation.checkAngle().roundToInt()
            (offsetRotation - defRotation).checkAngle() + rotationByDirectionTag
        val nextCenterPoint =
            getNextShapeCenterPoints(nextRotation)
        val finalRotation = rotationByDirectionTag + offsetRotation
        showLog(
            TAG,
            "getNextShapePosition:  nextCenterPoint = $nextCenterPoint , nextRotation = $nextRotation"
        )
        return ShapePosition(
            type, nextCenterPoint.x, nextCenterPoint.y,
            finalRotation.checkAngle().roundToInt()
        )
    }

    override fun shapeArea4Parent(): FloatArray {
        val shapePoint4Canvas = getShapePoint4Canvas()
        var minX = shapePoint4Canvas.first().x
        var maxX = shapePoint4Canvas.first().x
        var minY = shapePoint4Canvas.first().y
        var maxY = shapePoint4Canvas.first().y
        shapePoint4Canvas.forEach { pointF ->
            minX = min(pointF.x, minX)
            maxX = max(pointF.x, maxX)
            minY = min(pointF.y, minY)
            maxY = max(pointF.y, maxY)
        }
        tempArea[0] = minX
        tempArea[1] = maxX
        tempArea[2] = minY
        tempArea[3] = maxY
        return tempArea
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        // 顺时针从下开始 0 1 2 ，故左边为 300 ，右边为 60
        // 奇数 300 ，偶数 = 60
        if (directionTag < 0) return 0f
        return if (directionTag % 2 == 0) {
            60f
        } else {
            300f
        }
    }

    override fun updateStateBySub(subShapeView: BaseShapeView?) {
        //showLog(TAG, "updateConnectState: edgeTag =  subShapeView = $subShapeView")
        if (note !is YNote) return

        subShapeView?.note?.parentNote = this // 确定子类的父类是谁

        subShapeView?.let {// 确定当前类的子类是谁
            val diff = (it.offsetRotation - offsetRotation).checkAngle()
            //showLog(TAG, "updateConnectState: diff = $diff")
            when (if (diff == 60f) 2 else 1) {
                1 -> {
                    (note as YNote).leftNote = subShapeView
                }
                2 -> {
                    (note as YNote).rightNote = subShapeView
                }
                else -> {
                    note.parentNote = subShapeView
                }
            }
        }

        params.connectState = note.checkState()

        // 根据下一块的自身旋转角度，再使用当前的坐标轴，可计算出下一块实际的中心坐标
        subShapeView?.let {

            val defRotation = Constants.getShapeDefaultAngle(params.shapeType)/*旋转角度默认参数*/

            val rotationByDirectionTag = (it.offsetRotation - offsetRotation).checkAngle()

            val nextRotation =
                // directionTag * 60f + offsetRotation = next.offsetRotation.checkAngle().roundToInt()
                (offsetRotation - defRotation).checkAngle() + rotationByDirectionTag

            val nextCenterPoint =
                this.getNextShapeCenterPoints(nextRotation)

//            showLog(
//                TAG,
//                "updateConnectState: centerPos nextCenterPoint = ${it.centerPos}  nextRotation = $nextRotation"
//            )
            //showLog(TAG, "updateConnectState: retry nextCenterPoint = $nextCenterPoint ")
            it.setShapeLayoutParams(nextCenterPoint.x, nextCenterPoint.y)
        }
    }

    override fun notifyParentConnectState() {
        val yNote = note as YNote
        yNote.parentNote?.let {
            val parentNote = it.note as YNote
            if (parentNote.leftNote == this) {
                parentNote.leftNote = null
            }
            if (parentNote.rightNote == this) {
                parentNote.rightNote = null
            }
            it.params.connectState = it.note.checkState()
        }
    }
}