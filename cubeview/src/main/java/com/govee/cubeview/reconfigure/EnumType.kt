package com.govee.cubeview.reconfigure

import com.govee.cubeview.reconfigure.configuration.GameWallConfig
import com.govee.cubeview.reconfigure.configuration.HexagonConfig
import com.govee.cubeview.reconfigure.configuration.HexagonSolidConfig
import com.govee.cubeview.reconfigure.configuration.IConfig
import com.govee.cubeview.reconfigure.configuration.SpaceHexagonConfig
import com.govee.cubeview.reconfigure.configuration.SquareConfig
import com.govee.cubeview.reconfigure.configuration.TriangleConfig
import com.govee.cubeview.reconfigure.configuration.YConfig
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.reconfigure.shape.ui.UIType
import com.govee.cubeview.shape.Shape

/**
 *     author  : sinrow
 *     time    : 2022/9/6
 *     version : 1.0.0
 *     desc    :
 */

/*画布UI类型*/
enum class CanvasLayoutModeType {
    DefaultType,//默认，白色块
    Install,//安装
    Edit,//拼接
    EditPre,/*编辑前预览*/
    Check,//校验
    ColorMode,///颜色模式
    Focus,///diy&场景聚焦模式
    MultiPower,// 新增电源位置选择
    Preview,//效果预览
    MoveFeast,//观影盛宴
    CheckPre,// 检查预览
    AddLamp,// 加装
    Graffiti,// 涂鸦模式
    SoftLine,// 软件加装
    ;

    fun isInstallMode(): Boolean {
        return this == Install
    }

    fun defaultShowSerialNumber(): Boolean {
        return this == Edit || this == Check || this == EditPre
    }

    fun defaultEnableClickType(): Boolean {
        return this == ColorMode || this == MoveFeast
    }

    fun isColorMode(): Boolean {
        return this == ColorMode
    }

    fun isEditMode(): Boolean {
        return this == Edit || this == AddLamp
    }

    fun isCheckMode(): Boolean {
        return this == Check
    }

    fun isMultiPower(): Boolean {
        return this == MultiPower
    }

    fun isGraffiti(): Boolean {
        return this == Graffiti
    }


    fun checkMultiPowerView4Refresh(): Boolean {
        return this == MultiPower
            || this == Install
            || this == Check
            || this == EditPre
            || this == MoveFeast
            || this == CheckPre
            || this == AddLamp
    }

    fun isMoveFeast(): Boolean {
        return this == MoveFeast
    }

    fun convertUIType(): UIType {
        return when (this) {
            DefaultType, Edit, EditPre, Focus, AddLamp, SoftLine -> UIType(Type.Normal)
            Install -> UIType(Type.Install)
            Check -> UIType(Type.Check)
            ColorMode -> UIType(Type.Color)
            Preview -> UIType(Type.Preview)
            MultiPower -> UIType(Type.MultiPower)
            MoveFeast -> UIType(Type.MoveFeast)
            CheckPre -> UIType(Type.CheckPre)
            Graffiti -> UIType(Type.Graffiti)
        }
    }

    fun checkPathView4Refresh(): Boolean {
        return this == Install
    }

    fun checkPowerView4Refresh(): Boolean {
        return this == Edit || this == EditPre || this == Install || this == Check || this == MultiPower || this == MoveFeast || this == CheckPre || this == AddLamp || this == SoftLine
    }

    fun checkOperateView4Refresh(): Boolean {
        return this == Edit || this == AddLamp
    }

    fun checkFocusView4Refresh(): Boolean {
        return this == Focus
    }
}

/**
 * 定义面板灯类型
 * <p>
 *  六边形类型、三角形类型、Y形类型、组合图形类型
 * </p>
 * @param config 图形配置表
 * @param shapeType 与嵌入式约定的类型
 *
 */
enum class PanelLampType(
    val config: IConfig,
    var shapeType: Int = 0
) {
    HEXAGON(
        HexagonConfig(),
        com.govee.cubeview.shape.Shape.TYPE_HEXAGON
    ),
    SOLID_HEXAGON(
        HexagonSolidConfig(),
        com.govee.cubeview.shape.Shape.TYPE_SOLID_HEXAGON
    ),
    TRIANGLE(
        TriangleConfig(),
        com.govee.cubeview.shape.Shape.TYPE_TRIANGLE
    ),
    Y(
        YConfig(),
        com.govee.cubeview.shape.Shape.TYPE_Y
    ),
    SPACE_HEXAGON(
        SpaceHexagonConfig(),
        com.govee.cubeview.shape.Shape.TYPE_SPACE_HEXAGON
    ),
    GAME_WALL(
        GameWallConfig(),
        com.govee.cubeview.shape.Shape.TYPE_GAME_TRIANGLE
    ),
    SQUARE(
        SquareConfig(),
        com.govee.cubeview.shape.Shape.TYPE_SQUARE
    )
    ;

    fun isH6063Shapes(): Boolean {
        return shapeType == Shape.TYPE_GAME_TRIANGLE || shapeType == Shape.TYPE_GAME_RECTANGLE || shapeType == Shape.TYPE_GAME_SQUARE
    }
}

