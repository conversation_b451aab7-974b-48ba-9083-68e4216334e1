package com.govee.cubeview.reconfigure.shape.ui

import android.content.Context
import android.graphics.Paint
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/9/30
 *     version : 1.0.0
 *     desc    :
 */
open class NormalUIStrategy(val context: Context) : ShapeUIStrategy {

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        params.paintBaseShape.let {
            it.color = context.toColor(params.baseShapeStrokeColor)
            it.style = Paint.Style.STROKE
            it.strokeWidth = params.strokeWidth
        }
    }

    override fun getType(): Type = Type.Normal
}
