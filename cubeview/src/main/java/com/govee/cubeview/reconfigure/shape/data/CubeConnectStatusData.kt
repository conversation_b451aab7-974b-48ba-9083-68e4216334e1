package com.govee.cubeview.reconfigure.shape.data

import android.graphics.PointF
import com.govee.cubeview.reconfigure.configuration.GameWallConfig
import com.govee.cubeview.reconfigure.configuration.SquareConfig
import com.govee.cubeview.shape.Shape

/**
 *     author  : sinrow
 *     time    : 2023/11/29
 *     version : 1.0.0
 *     desc    : 方块灯系列；从设备读取回来的子方块之间链接状态
 */
data class CubeConnectStatusData(
    val type: Int = 0,
    val inputStatus: Byte,
    val outputStatus: Byte
)

internal data class CubeConnectTemp(
    var type: Int,// 方块类型
    val centerOffset: PointF,// 中心点坐标
    val angle: Float,// 旋转角度
    val outputEdge: Int,// 边序号
    val lastNote: Int,//上一个节点序号
    val tag: Long
) {
    private val TAG = "CubeConnectStatusData"

    fun parseNextAngle(nextType: Int, inputEdge: Int): Float {
        val nearShapeSideCount = when (this.type) {
            Shape.TYPE_GAME_RECTANGLE -> {
                2
            }

            Shape.TYPE_GAME_SQUARE -> {
                4
            }

            Shape.TYPE_GAME_TRIANGLE -> {
                3
            }

            Shape.TYPE_SQUARE -> {
                4
            }

            else -> {
                0
            }
        }
        val nearPerAngle = 360f / nearShapeSideCount
        val nextAngle1: Float = when (this.type) {
            Shape.TYPE_GAME_RECTANGLE -> {
                when (nextType) {
                    Shape.TYPE_GAME_RECTANGLE,
                    Shape.TYPE_GAME_SQUARE,
                    Shape.TYPE_GAME_TRIANGLE -> {
                        0f
                    }

                    else -> {
                        0f
                    }
                }
            }

            Shape.TYPE_GAME_SQUARE -> {
                when (nextType) {

                    Shape.TYPE_GAME_RECTANGLE,
                    Shape.TYPE_GAME_SQUARE,
                    Shape.TYPE_GAME_TRIANGLE -> {
                        (outputEdge + 2) * nearPerAngle
                    }

                    else -> {
                        (outputEdge + 2) * nearPerAngle
                    }
                }
            }

            Shape.TYPE_GAME_TRIANGLE -> {
                when (nextType) {
                    Shape.TYPE_GAME_RECTANGLE,
                    Shape.TYPE_GAME_SQUARE,
                    Shape.TYPE_GAME_TRIANGLE -> {
                        (outputEdge + 1) * nearPerAngle + 60f
                    }

                    else -> {
                        (outputEdge + 1) * nearPerAngle + 60f
                    }
                }
            }

            Shape.TYPE_SQUARE -> {
                // 旋转角度
                540 + nearPerAngle * outputEdge - nearPerAngle * inputEdge
            }

            else -> {
                180f
            }
        }

        //showLog(TAG, "handleNextAngle() nextAngle = $nextAngle1 angle = $angle , outputEdge = $outputEdge")
        return nextAngle1 + angle
    }

    fun getRadius(shapeType: Int): Float {
        //如果两条都是长方形，则使用内切圆半径，如果非相邻则使用真实数据。
        if (shapeType == Shape.TYPE_GAME_RECTANGLE) {
            return GameWallConfig.GameRectangleViewHeight
        }
        val lineLength = when (shapeType) {
            Shape.TYPE_GAME_RECTANGLE -> {
                GameWallConfig.TYPE_GAME_RECTANGLE_LINE_LENGTH
            }

            Shape.TYPE_GAME_TRIANGLE -> {
                GameWallConfig.TYPE_GAME_TRIANGLE_LINE_LENGTH
            }

            Shape.TYPE_GAME_SQUARE -> {
                GameWallConfig.TYPE_GAME_SQUARE_LINE_LENGTH
            }

            Shape.TYPE_SQUARE -> {
                SquareConfig.lineLength
            }

            else -> {
                0f
            }
        }
        return Shape.getShapeInnerRadius(
            lineLength,
            shapeType,
            2f
        ).apply {
            //showLog(TAG, "getRadius() lineLength = $lineLength , shapeType = $shapeType, 内切圆半径是 $this")
        }
    }

    fun getNextAngleReal(nextAngleReal: Int): Float {
        return if (type == Shape.TYPE_SQUARE) {
            (outputEdge - 1) * 90 + angle
        } else {
            nextAngleReal.toFloat()
        }
    }
}