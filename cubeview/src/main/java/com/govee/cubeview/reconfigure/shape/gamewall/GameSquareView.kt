package com.govee.cubeview.reconfigure.shape.gamewall

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.shape.BusinessShapeView
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import kotlin.math.PI
import kotlin.math.ceil
import kotlin.math.cos
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2023/11/28
 *     version : 1.0.0
 *     desc    : 正方形
 */
class GameSquareView(context: Context) : BusinessShapeView(context) {
    private var lastNotePosition: Int = -1

    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)
        val shapePosition = shapeData.shapePosition

        // 如果主电源有赋值，则按照辅助电源形式显示
        shapePosition.mainPower?.let {
            mainPower.powerEdgeNUmber
            mainPower.powerNumber = it.powerNumber
            mainPower.powerEdgeNUmber = it.powerEdgeNUmber
            mainPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMainPower = true
        }
        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }
        params.inputNum = shapePosition.inputNum
        params.outputNum = shapePosition.outputNum
        this.lastNotePosition = shapePosition.lastPosition
    }

    override fun getNextShapeCenterPointByRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        //showLog(TAG, "下一个图形的长度 rotation = ${rotation - 90}, length = $length")
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation - 90, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 0 / 4) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 0 / 4) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(centerPointF: PointF, length: Float, rotation: Float, forSelf: Boolean): Array<PointF> {
        showLog(TAG, "getShapePoint() length = $length , rotation = $rotation")
        return Array(4) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(needRotation(rotation), centerPointF.x, centerPointF.y)
            val shapePoint = Array(4) { FloatArray(2) }.apply {
                for (i in indices) {
                    val angle = 2 * PI * i / 4
                    //showLog(TAG, "getShapePoint() angle = $angle")
                    // 左上角为开始点
                    this[i] =
                        floatArrayOf(
                            centerPointF.x + (length * cos(angle)).toFloat(),
                            centerPointF.y + (length * sin(angle)).toFloat()
                        )
//                    if (i == 0) {
//                        this[0][0] = this[0][0] + 11
//                    }
                    matrix.mapPoints(this[i])
                }
            }

            forEachIndexed { index, pointF ->
                val floats = shapePoint[index]
                pointF.x = floats[0]
                pointF.y = floats[1]
            }
        }
    }

    override fun needRotation(rotation: Float): Float {
        return rotation + 45
    }

    override fun showSerialNumber(): Boolean {
        return false
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        showLog(TAG, "getRotationByDirectionTag() directionTag = $directionTag")
        return (directionTag * 90f + 180f).checkAngle()
    }

    override fun toShapePosition(): ShapePosition {
        return ShapePosition(
            params.shapeType, ceil(centerPos.x), ceil(centerPos.y), getRotationValue(), 0, params.connectState
        ).apply {
            if (params.isMultiPowerSelected && !mMultiPower.isDefault()) {
                this.ext = mMultiPower
            }
            if (params.isMultiPowerSelected && !<EMAIL>()) {
                this.mainPower = <EMAIL>
            }
            this.editable = params.editable
            this.inputNum = params.inputNum
            this.outputNum = params.outputNum
            this.lastPosition = lastNotePosition
        }
    }
}