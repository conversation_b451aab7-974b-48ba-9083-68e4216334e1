package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.checkAngle
import com.govee.cubeview.isNearLowBlueColor
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.shape.ui.IShapeBusiness
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/9/27
 *     version : 1.0.0
 *     desc    : Shape 图形基础业务层，对外提供翻转、旋转等功能，状态刷新等
 *              // 不同形状-不同的场景-不同的表现
 */
abstract class BusinessShapeView(context: Context) :
    BaseShapeView(context), IShapeBusiness {


    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)

        params.run {
            // 根据不同画布状态初始化默认参数

            shapeColor = toColor(params.defaultShapeColor)

            paintBaseShape.let {
                it.style = Paint.Style.STROKE
                it.strokeWidth = strokeWidth
                it.color = toColor(baseShapeStrokeColor)
            }

            paintShapeBackground.let {
                it.color = shapeColor
                it.style = Paint.Style.FILL
            }
            paintCenterText.let {
                it.style = Paint.Style.FILL
                it.textAlign = Paint.Align.CENTER
                it.color = toColor(textColor)
                it.textSize = if (modeType.isColorMode()) colorTextSize else defaultTextSize
            }
            paintSelectedBorder.let {
                it.style = Paint.Style.STROKE
                it.strokeWidth = colorModeStrokeWidth
                val nearLowBlueColor = shapeColor.isNearLowBlueColor()
                val colorRes =
                    if (nearLowBlueColor) showNearLowBlueColor else showNearHighBlueColor
                it.color = toColor(colorRes)
            }

            paintAttributes.let {
                it.style = Paint.Style.STROKE
                it.strokeWidth = attributesStrokeWidth
                it.color = toColor(attributesStrokeColor)
            }

            if (showSerialNumber()) {
                centerText = shapeData.serialNumber.toString()
            }

            if (modeType.isColorMode()) {
                centerText = shapeData.colorDefaultText
            }
        }

        uiStrategyMap[shapeState.type]?.defaultState(params)
    }

    override fun flipByX(origin: PointF) {

        // 改变中心点坐标
        centerPos.let { pointF ->
            pointF.x = origin.x * 2 - pointF.x
        }
        showLog(TAG, "flipByX:  centerPoints = $centerPos")
        /*改变旋转角度*/
        offsetRotation =
            360 - (offsetRotation - params.defaultAngle) + params.defaultAngle

        /*重置中心点坐标 layoutParams*/
        setShapeLayoutParams(centerPos.x, centerPos.y)
        /*重新校验连接状态*/
        note.flip()
        params.connectState = note.checkState()
    }


    override fun horizontalRotation(onceRotation: Float, origin: PointF) {

        offsetRotation = (offsetRotation + onceRotation).checkAngle()

        val detPoint = floatArrayOf(centerPos.x, centerPos.y)
        rotatePoint(onceRotation, detPoint, origin)
        setShapeLayoutParams(detPoint[0], detPoint[1])
    }

    override fun showSerialNumber(): Boolean {
        return modeType.defaultShowSerialNumber()
    }

    override fun enableClick(canvasLayoutModeType: CanvasLayoutModeType): Boolean {
        // 默认可点击为颜色模式
        return canvasLayoutModeType.defaultEnableClickType() && params.editable
    }

    protected fun rotatePoint(rotate: Float, detPoint: FloatArray, srcPointF: PointF) {
        val matrix = Matrix()
        matrix.setRotate(rotate, srcPointF.x, srcPointF.y)
        /*旋转*/
        matrix.mapPoints(detPoint)
    }

    override fun doSomething4ColorMode(block: () -> Unit) {
        if (modeType.isColorMode()) {
            setOnClickListener { block() }
        }
    }

    override fun doSomething4MoveFeast(block: () -> Unit) {
        if (modeType == CanvasLayoutModeType.MoveFeast) {
            setOnClickListener {
                block()
            }
        }
    }
}