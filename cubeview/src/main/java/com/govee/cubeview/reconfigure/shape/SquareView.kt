package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.Rect
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor
import kotlin.math.PI
import kotlin.math.ceil
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2024/6/14
 *     version : 1.0.0
 *     desc    :
 */
class SquareView(context: Context) : BusinessShapeView(context) {
    private var lastNotePosition: Int = -1

    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)
        val shapePosition = shapeData.shapePosition

        // 如果主电源有赋值，则按照辅助电源形式显示
        shapePosition.mainPower?.let {
            mainPower.powerEdgeNUmber
            mainPower.powerNumber = it.powerNumber
            mainPower.powerEdgeNUmber = it.powerEdgeNUmber
            mainPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMainPower = true
        }
        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }
        params.inputNum = shapePosition.inputNum
        params.outputNum = shapePosition.outputNum
        this.lastNotePosition = shapePosition.lastPosition
        params.paintCenterText.let {
            it.style = Paint.Style.FILL
            it.textAlign = Paint.Align.CENTER
            it.color = toColor(params.textColor)
            it.textSize = if (modeType.isColorMode()) params.colorTextSize else 13f
        }
    }

    override fun getNextShapeCenterPointByRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        //showLog(TAG, "下一个图形的长度 rotation = ${(rotation).checkAngle()}, length = $length")
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 4) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 4) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(centerPointF: PointF, length: Float, rotation: Float, forSelf: Boolean): Array<PointF> {
        //showLog(TAG, "getShapePoint() length = $length , rotation = $rotation")
        return Array(4) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(needRotation(rotation), centerPointF.x, centerPointF.y)
            val shapePoint = Array(4) { FloatArray(2) }.apply {
                for (i in indices) {
                    val angle = 2 * PI * i / 4
                    //showLog(TAG, "getShapePoint() angle = $angle")
                    // 左上角为开始点
                    this[i] =
                        floatArrayOf(
                            centerPointF.x + (length * cos(angle)).toFloat(),
                            centerPointF.y + (length * sin(angle)).toFloat()
                        )
                    matrix.mapPoints(this[i])
                }
            }

            forEachIndexed { index, pointF ->
                val floats = shapePoint[index]
                pointF.x = floats[0]
                pointF.y = floats[1]
            }
        }
    }

    override fun needRotation(rotation: Float): Float {
        return rotation + 45
    }

    override fun showSerialNumber(): Boolean {
        return true
    }

    override fun drawShapeOtherAttributes(canvas: Canvas, paint: Paint) {
//        paint.color = Color.RED
//        val shapeDefaultPoint = getShapePoint(selfCenterPointF, params.outsideLineLength, offsetRotation + 45)
//        shapeDefaultPoint.forEachIndexed { index, it ->
//            canvas.drawText(index.plus(1).toString(), it.x, it.y, paint)
//        }

//        canvas.drawText(shapeData.serialNumber.toString(), selfCenterPointF.x, selfCenterPointF.y, paint)
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        showLog(TAG, "getRotationByDirectionTag() directionTag = $directionTag")
        return (directionTag * 90f).checkAngle()
    }

    override fun toShapePosition(): ShapePosition {
        return ShapePosition(
            params.shapeType, ceil(centerPos.x), ceil(centerPos.y), getRotationValue(), 0, params.connectState
        ).apply {
            if (params.isMultiPowerSelected && !mMultiPower.isDefault()) {
                this.ext = mMultiPower
            }
            if (params.isMultiPowerSelected && !<EMAIL>()) {
                this.mainPower = <EMAIL>
            }
            this.editable = params.editable
            this.inputNum = params.inputNum
            this.outputNum = params.outputNum
            this.lastPosition = lastNotePosition
        }
    }

    override fun drawCenterDrawBitmap(canvas: Canvas, paint: Paint) {
        moveFeastData.let {
            if (it.isDrawCenterDrawable()) {
                val bitmap = it.getCenterDrawable(context, max(width, height).toFloat())
                bitmap.let {
                    val left = (width - bitmap.width)
                    val top = (height - bitmap.height)

                    canvas.drawCircle(
                        width / 2f,
                        height / 2f,
                        max(width, height) * 0.1f,
                        params.paintShapeBackground
                    )

                    canvas.drawBitmap(
                        bitmap,
                        Rect(0, 0, bitmap.width, bitmap.height),
                        Rect(left, top, bitmap.width, bitmap.height),
                        paint
                    )
                }

            }
        }
    }
}