package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.graphics.PointF
import android.view.MotionEvent
import android.widget.ImageView
import com.govee.cubeview.showLog
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/10/12
 *     version : 1.0.0
 *     desc    : 拖拽聚焦图形 --- 用于聚焦模式下，当前选中点
 */
class FocusDragView(context: Context) : BaseFocusView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 56//边长
        const val SIZE_HEIGHT = 56//边长

    }

    private var moveX = 0f
    private var moveY = 0f
    private var enableMove = false

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    private fun init() {
        /*处于绘制最高级*/
        z = 2f
        inputImgView.maxWidth = SIZE_WIDTH
        inputImgView.maxHeight = SIZE_HEIGHT
        inputImgView.scaleType = ImageView.ScaleType.FIT_CENTER
        showFocusState(isIn)
    }

    override fun showFocusState(isIn: Boolean) {
        this.isIn = isIn
        setInputImageResource(if (isIn) R.mipmap.new_btn_fangxiang_zhongxin_nei else R.mipmap.new_btn_fangxiang_zhongxin)
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (enableMove) {
            /*向父布局申请不拦截触摸事件*/
            parent.requestDisallowInterceptTouchEvent(true)
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun setLayoutParams(pointF: PointF) {
        translationX = 0f
        translationY = 0f
        super.setLayoutParams(pointF)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!enableMove) return false
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                moveX = event.x
                moveY = event.y
            }
            MotionEvent.ACTION_MOVE -> {
                translationX = x + (event.x - moveX) - left
                translationY = y + (event.y - moveY) - top

                showLog(
                    TAG,
                    "translationX=${translationX}  x=${x}  event.x=${event.x}  moveX=${moveX}"
                )

            }
            MotionEvent.ACTION_UP -> {
                pos.set(
                    left + SIZE_WIDTH / 2f + translationX,
                    top + SIZE_HEIGHT / 2f + translationY
                )
                showLog(
                    TAG,
                    "left=${left}  top=${top}  translationX=${translationX}  translationY=${translationY}"
                )

                focusDragCallback?.invoke(pos)
            }
            MotionEvent.ACTION_CANCEL -> {
                pos.set(
                    left + SIZE_WIDTH / 2f + translationX,
                    top + SIZE_HEIGHT / 2f + translationY
                )
                focusDragCallback?.invoke(pos)
            }
        }
        return true
    }

    override fun doSomething(block: () -> Unit) {
        enableMove = true
        super.doSomething(block)
    }
}