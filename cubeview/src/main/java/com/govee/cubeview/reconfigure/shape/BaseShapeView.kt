package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.view.View
import android.widget.FrameLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.reconfigure.configuration.IConfig
import com.govee.cubeview.reconfigure.shape.assist.ImgPowerShapeView
import com.govee.cubeview.reconfigure.shape.data.BaseNote
import com.govee.cubeview.reconfigure.shape.data.CubeMoveFeastData
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.IShapeView
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.reconfigure.shape.ui.UIType
import com.govee.cubeview.reconfigure.shape.ui.powerAssistShapeCenterPoints
import com.govee.cubeview.shape.MultiPower
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.view.ImgAddShapeView
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2022/9/6
 *     version : 1.0.0
 *     desc    :
 *     1、图形抽象类，处理基础图形的构建的整个流程
 *     2、具体构建方式处理，由具体实现类实现
 *     3、不同状态的刷新，状态由实现类处理
 *     4、基类只提供 画基本图形，背景色，中心文本
 *
 */
abstract class BaseShapeView constructor(context: Context) : View(context), IShapeView {

    protected val TAG = javaClass.simpleName.toString()

    /*延迟初始化*/
    lateinit var params: BaseShapeParams
    lateinit var modeType: CanvasLayoutModeType
    lateinit var note: BaseNote

    lateinit var uiStrategyMap: HashMap<Type, ShapeUIStrategy>

    lateinit var shapeState: UIType /*表示当前图形的 UI 状态*/

    protected var tempArea = FloatArray(4)/*记录占用的面积大小，表示：上下左右坐标*/

    internal val selfCenterPointF: PointF /*图形中心坐标，需要注意使用时机，view 未绘制时，获取为 0*/
        get() {
            return PointF(measuredWidth / 2f, measuredHeight / 2f)
        }

    private val shapeBgShapePath by lazy { Path() }

    val baseShapePath by lazy { Path() }

    /*主电源信息*/
    val mainPower by lazy { MultiPower(-1, -1, mutableListOf()) }

    /*辅助电源信息*/
    val mMultiPower by lazy { MultiPower(-1, -1, mutableListOf()) }

    /*自己的旋转偏移量*/
    var offsetRotation = 0f

    /*下一个图形出边的编号 tag*/
    var nextDirectionTag: Int = -1

    /*自己的tag偏移量，默认电源入口的tag值 -- first 偏移角度， sec 对应的tag值*/
    var offsetTag: Pair<Float, Int>? = null


    /*相对画布的中心点*/
    val centerPos by lazy { PointF() }
    val moveFeastData by lazy { CubeMoveFeastData() }

    @Transient
    var sortIndex = 0 //  用于606A选择电源后的排序的临时变量

    @Transient
    var count = 0

    override fun setConfig(config: IConfig) {
        params = config.configShapeParams()
        uiStrategyMap = config.configUIStrategy(this)
        note = config.configNote()
    }

    override fun initShapeData(shapeData: ShapeData) {

        val shapePosition = shapeData.shapePosition

        modeType = shapeData.modeType
        params.editable = shapePosition.editable
        params.connectState = shapePosition.state

        shapeState = modeType.convertUIType()

        offsetRotation = (shapePosition.angle.toFloat()).checkAngle()

        moveFeastData.serialNumber = shapeData.serialNumber

        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }

        setShapeLayoutParams(shapePosition.x, shapePosition.y)

    }

    override fun getRotationValue(): Int {
        return offsetRotation.checkAngle().roundToInt()
    }

    override fun getAddAssistShapeCenterLength(): Float {
        return params.nextShapeCenterLineLength + ImgAddShapeView.SIZE_ADD / 2
    }

    open fun toShapePosition(): ShapePosition {
        return ShapePosition(
            params.shapeType, ceil(centerPos.x), ceil(centerPos.y), getRotationValue(), 0, params.connectState
        ).apply {
            if (params.isMultiPowerSelected && !mMultiPower.isDefault()) {
                this.ext = mMultiPower
            } else if (params.isMultiPowerSelected && !<EMAIL>()) {
                this.mainPower = <EMAIL>
            }
            this.editable = params.editable
        }
    }

    fun getShapeDefaultPoint(len: Float = params.outsideLineLength): Array<PointF> {
        return getShapePoint(selfCenterPointF, len, offsetRotation)
    }

    fun getShapePoint4Canvas(forSelf: Boolean = true): Array<PointF> {
        return getShapePoint(centerPos, params.outsideLineLength, offsetRotation, forSelf)
    }

    open fun getNextShapeCenterPoints(
        nextRotation: Float, nextLineLength: Float = params.nextShapeCenterLineLength * 2
    ): PointF {
        /*自身角度+加号的角度，即为下一个图形的旋转角度*/
        //showLog(TAG, "getNextShapeCenterPoints: length = $nextLineLength")
        return getNextShapeCenterPointByRotation(
            centerPos, nextLineLength, nextRotation
        )
    }

    /**
     * 用于获取图形路径绘制的中心点
     * <p>
     *     需要旋转到插入边的旋转角度，六边形从左上角开始，旋转 180 + 默认角度
     * </p>
     */
    open fun getPathShapeCenterPoints(): PointF {
        return getNextShapeCenterPoints(
            offsetRotation - params.defaultAngle, params.innerLineLength
        )
    }

    /**
     *  用于获取图形路径绘制的中心点
     *  <p>
     *  <p>
     *      @return Pair<A,B> A:旋转角度，B:中心坐标
     */
    open fun getPathEntity(lastShapeView: BaseShapeView?): PathEntity? {
        lastShapeView?.let {
            val pathShapePathNum = getPathShapePathNum(it.offsetRotation)
            val pathNum = Constants.getInstallNumberWithDirectionTag2(
                params.shapeType, pathShapePathNum
            )
            showLog(TAG, "getPathEntity: pathShapePathNum = $pathShapePathNum , pathNum = $pathNum")
            val rotation = offsetRotation - params.defaultAngle

            val centerPoint =
                PointF((it.centerPos.x + centerPos.x) / 2, (it.centerPos.y + centerPos.y) / 2)

            return PathEntity().apply {
                this.pathNum = pathNum
                this.rotation = rotation
                this.centerPointF = centerPoint
            }

        } ?: run {
            val pointF = powerAssistShapeCenterPoints(ImgPowerShapeView.SIZE_WIDTH).let {
                Utils.centerPoint(it, centerPos)
            }
            return PathEntity().apply {
                this.pathNum = -1
                this.rotation = (offsetRotation - params.defaultAngle)
                this.centerPointF = pointF
            }
        }
    }

    /**
     * 获取不同序号
     */
    open fun getPathShapePathNum(lastOffsetRotation: Float): Int {
        return -1
    }


    /**
     * 获取下一个图形的 shapePosition 信息
     */
    open fun getNextShapePosition(edgeTag: Int): ShapePosition {
        val type = params.shapeType
        val rotationByDirectionTag = getRotationByDirectionTag(edgeTag) // directionTag * 60f
        val nextRotation =
            rotationByDirectionTag + offsetRotation // directionTag * 60f + offsetRotation = next.offsetRotation.checkAngle().roundToInt()
        val nextCenterPoint = getNextShapeCenterPoints(nextRotation)
        return ShapePosition(
            type, nextCenterPoint.x, nextCenterPoint.y, nextRotation.checkAngle().roundToInt()
        )
    }

    /**
     * 因为关系是双方的，所以更新的时候，需要把双方关系都确认清楚
     * 1.确定子类的父类是谁
     * 2.确定父类的子类是谁
     * 子类：默认 sub，Y 形灯分为 left/right,其他类型根据形态各自划分
     *
     * 新增：
     *      该方法传入下一个图形信息，可以根据此信息确定上下关系信息。也可以根据此信息来做坐标矫正，防止图形出现偏差
     * <p>
     *     该方法用于生成子View 时，使用父类让其关联子View
     * </p>
     */
    open fun updateStateBySub(subShapeView: BaseShapeView?) {
        // 这个方法是为了添加的时候，改变 state 状态值
        subShapeView?.note?.parentNote = this // 确定子类的父类是谁
        note.subNote = subShapeView // 确定当前类的子类是谁
        params.connectState = note.checkState()
    }

    open fun updateStateByGraphic(selfShapeView: BaseShapeView, baseShapeView: MutableList<BaseShapeView>?) {
        baseShapeView?.let {
            if (it.isNotEmpty()) {
                note.graphShapes.apply {
                    clear()
                    addAll(it)
                }
                note.selfShapeView = selfShapeView
                params.connectState = note.checkState()
            }
        }
    }

    open fun notifyParentConnectState() {
        note.parentNote?.updateStateBySub(null)
    }

    protected open fun needRotation(rotation: Float): Float {
        return rotation
    }

    /**
     * 根据父布局提供的中心坐标，设置 LayoutParams 参数
     */
    internal fun setShapeLayoutParams(centerPointX: Float, centerPointY: Float) {
        /*一般图形为内切圆半径*/
        /*安装模式因为需要虚化 aloha 导致边所以需要外边框*/

        val checkLayoutParamsWidth = shapeState.checkLayoutParamsWidth()
        //showLog(TAG, "setShapeLayoutParams: checkLayoutParamsWidth = $checkLayoutParamsWidth ")
        val shapeRectWith =
            if (checkLayoutParamsWidth) params.outsideLineLength else params.innerLineLength
        setShapeLayoutParams(centerPointX, centerPointY, shapeRectWith)
    }

    internal fun getAvailMultiPower(): MultiPower? {
        if (!mainPower.isDefault()) {
            return mainPower
        }
        if (!mMultiPower.isDefault()) {
            return mMultiPower
        }
        return null
    }

    internal fun getAvailMultiPower2(): MultiPower? {
        if (!mMultiPower.isDefault()) {
            return mMultiPower
        }
        return null
    }


    /**
     *  根据父布局提供的中心坐标，设置 LayoutParams 参数
     *  @param centerPointX
     *  @param centerPointY
     */
    override fun setShapeLayoutParams(
        centerPointX: Float, centerPointY: Float, shapeRectWith: Float
    ) {
        /*设置图形中心点 -- 相对画布的位置*/
        centerPos.x = centerPointX
        centerPos.y = centerPointY

        val lp = FrameLayout.LayoutParams(
            (shapeRectWith * 2).roundToInt(), (shapeRectWith * 2).roundToInt()
        )
        val roundToInt = (centerPointX - shapeRectWith).roundToInt()
        val roundToInt1 = (centerPointY - shapeRectWith).roundToInt()
        //showLog(TAG, "setShapeLayoutParams: roundToInt = $roundToInt roundToInt1 = $roundToInt1")
        lp.leftMargin = roundToInt
        lp.topMargin = roundToInt1
        layoutParams = lp
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        canvas.run {
            /* 图形绘制流程*/
            // 1、绘制基本图形(背景+边框)
            // 2、绘制辅助参数
            // 3、绘制中心文本内容(用于序号，颜色亮度等)

            drawBackground(this, params.paintShapeBackground)
            drawShapeOtherAttributes(this, params.paintAttributes)
            drawBaseShape(this, params.paintBaseShape)
            val centerText = params.centerText
            if (centerText.isNotEmpty() && params.visibleText) {
                drawCenterText(this, params.paintCenterText)
            }
            if (modeType.isMoveFeast()) {
                drawCenterDrawBitmap(this, params.paintCenterDrawable)
            }

            if (isSelected) {
                // 当前为选中状态，则绘制选择框（图形可自己处理）
                drawSelectedBorder(this, params.paintSelectedBorder)
            }
        }
    }


    override fun drawBaseShape(canvas: Canvas, paint: Paint) {
        val shapeDefaultPoint = getShapeDefaultPoint(params.outsideLineLength)
        baseShapePath.let {
            it.reset()
            shapeDefaultPoint.onEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)
                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            /*画路径*/
            it.transform(matrix)
            /*画图形边框*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    override fun drawBackground(canvas: Canvas, paint: Paint) {
        val shapeDefaultPoint =
            getShapeDefaultPoint(params.outsideLineLength - paint.strokeWidth / 2f)
        shapeBgShapePath.let {
            it.reset()
            shapeDefaultPoint.onEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)

                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            /*画路径*/
            it.transform(matrix)
            /*画背景色*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    override fun drawSelectedBorder(canvas: Canvas, paint: Paint) {
        // 因为画笔 strokeWidth 是居中的，选择框最外面的边界应该需要保留一半的 baseShape 宽度
        // 然后在减去选择框画笔的宽度一半。这样计算得出的坐标，形成的 path 图形，效果会好一些
        val shapeDefaultPoint =
            getShapeDefaultPoint(params.outsideLineLength - paint.strokeWidth / 2f)
        baseShapePath.let {
            it.reset()
            shapeDefaultPoint.onEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF.x, pointF.y)

                } else {
                    it.lineTo(pointF.x, pointF.y)
                }
            }
            it.close()

            /*画路径*/
            it.transform(matrix)
            /*画背景色*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    override fun drawShapeOtherAttributes(canvas: Canvas, paint: Paint) {
        // do something
    }

    override fun drawCenterText(canvas: Canvas, paint: Paint) {
        // do something
        val centerText = params.centerText
        val fontMetrics = paint.fontMetrics
        val top = fontMetrics.top//为基线到字体上边框的距离,即上图中的top
        val bottom = fontMetrics.bottom//为基线到字体下边框的距离,即上图中的bottom

        val baseLineY = (height / 2).toFloat() - top / 2 - bottom / 2//基线中间点的y轴计算公式

        canvas.drawText(centerText, (width / 2).toFloat(), baseLineY, paint)

    }

    override fun drawCenterDrawBitmap(canvas: Canvas, paint: Paint) {
        moveFeastData.let {
            if (it.isDrawCenterDrawable()) {
                val bitmap = it.getCenterDrawable(context, max(width, height).toFloat())
                val left = (width - bitmap.width)
                val top = (height - bitmap.height)

                canvas.drawCircle(
                    width / 2f,
                    height / 2f,
                    20f,
                    params.paintShapeBackground
                )

                canvas.drawBitmap(
                    bitmap,
                    Rect(0, 0, bitmap.width, bitmap.height),
                    Rect(left, top, bitmap.width, bitmap.height),
                    paint
                )

            }
        }
    }

    fun updateUIState() {
        updateUIState(shapeState)
    }

    fun updateUIState(state: Int) {
        updateUIState(shapeState.also { it.state = state })
    }

    override fun updateUIState(type: UIType) {
        // 先根据功能，再判断 UI 状态，抽离出来，形成策略模式
        shapeState = type

        with(uiStrategyMap[type.type]) {
//            showLog(TAG, "updateUIState:  = ${type.state} ")
            this?.updateState(params, type.state)
        }

        invalidate()
    }

    override fun setShapeColor(color: Int) {
        params.shapeColor = color
    }

    override fun setShapeCenterText(content: String) {
        params.centerText = content
    }

    open fun shapeArea4Parent(): FloatArray {
        val x = centerPos.x
        val y = centerPos.y
        tempArea[0] = x - params.outsideLineLength / 2
        tempArea[1] = x + params.outsideLineLength / 2
        tempArea[2] = y - params.outsideLineLength / 2
        tempArea[3] = y + params.outsideLineLength / 2
        return tempArea
    }

    fun isPointInShape(point: PointF): Boolean {
        val shapeDefaultPoint = getShapePoint4Canvas()
        return Utils.isPointInPolygon(PointF(point.x, point.y), shapeDefaultPoint.toMutableList())
    }

    /**
     * 内部包裹点击事件，防止绘制图形时设置的 LayoutParams 过大导致点击区域有误
     * <p>
     *     经确认内切圆区域点击范围可接受
     * </p>
     */
    fun onClickListener(block: () -> Unit) = setOnClickListener {

        setShapeLayoutParams(centerPos.x, centerPos.y, params.innerLineLength)
        block()
    }

    var singleClickListener: (() -> Unit)? = null

    fun resetColorAndBrightness() {
        params.centerText = params.defaultText
        params.shapeColor = params.defaultShapeColor
    }
}