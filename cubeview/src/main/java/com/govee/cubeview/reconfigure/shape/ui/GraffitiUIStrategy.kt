package com.govee.cubeview.reconfigure.shape.ui

import android.content.Context
import android.graphics.Color
import com.govee.cubeview.Utils
import com.govee.cubeview.toColor
import com.govee.ui.R


/**
 *     author  : sinrow
 *     time    : 2022/9/30
 *     version : 1.0.0
 *     desc    :
 */
open class GraffitiUIStrategy(val context: Context) : ShapeUIStrategy {

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        with(params) {
            params.paintShapeBackground.color = params.shapeColor
            paintCenterText.let {
                if (handleCenterText4ColorMode) {
                    val red = Color.red(shapeColor)
                    val green = Color.green(shapeColor)
                    val blue = Color.blue(shapeColor)
                    if (red * 0.299 + green * 0.578 + blue * 0.114 >= 192) {
                        //浅色
                        it.color = Color.BLACK
                    } else {
                        //深色
                        it.color = Color.WHITE
                    }
                } else {
                    val curLightMode = Utils.curLightMode(context)
                    if (curLightMode) {
                        it.color = Color.BLACK
                    } else {
                        it.color = Color.WHITE
                    }
                }
            }
            paintAttributes.let {
                /*颜色反转*/
                val red = Color.red(shapeColor)
                val green = Color.green(shapeColor)
                val blue = Color.blue(shapeColor)
                if (red * 0.299 + green * 0.578 + blue * 0.114 >= 192) {
                    //浅色
                    it.color = context.toColor(R.color.ui_line_style_2_1_stroke_color)
                } else {
                    //深色
                    it.color = context.toColor(R.color.ui_line_style_2_2_stroke_color)
                }
                it.strokeWidth = 1.5f
            }
        }
    }

    override fun getType(): Type = Type.Graffiti

}