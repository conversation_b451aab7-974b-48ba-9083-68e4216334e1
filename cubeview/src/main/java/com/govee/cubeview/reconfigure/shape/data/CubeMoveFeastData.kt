package com.govee.cubeview.reconfigure.shape.data

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix


/**
 *     author  : sinrow
 *     time    : 2022/12/2
 *     version : 1.0.0
 *     desc    :
 *     @param type
 *            0:主色调
 *            255:熄灯
 *            1-244:其他区域，UI 显示颜色+文字
 */
data class CubeMoveFeastData(
    var type: Int = 0,
    var color: Int = com.govee.ui.R.color.ui_color_block_style_16_1_color, /*颜色色值*/
    var centerText: String = "",/*中心文本*/
    var serialNumber: Int = 0/*方块图形序号*/
) {

    companion object {
        const val type_main_color = 0
        const val type_light_off = 0xff
        val type_other_area = 1..254
    }

    fun isDrawCenterText(): Boolean {
        return type_other_area.contains(type)
    }

    fun isDrawCenterDrawable(): Boolean {
        return type == type_main_color || type == type_light_off
    }

    fun getCenterDrawable(context: Context, lineLength: Float): Bitmap {
        val bitmapId = if (type == type_main_color) com.govee.ui.R.mipmap.new_area_icon_screen
        else com.govee.ui.R.mipmap.new_area_icon_light_off
        val decodeResource = BitmapFactory.decodeResource(
            context.resources, bitmapId
        )
        val matrix = Matrix()
        val scale = lineLength * 0.8f / decodeResource.width
        matrix.postScale(scale, scale) //长和宽放大缩小的比例
        return Bitmap.createBitmap(
            decodeResource,
            0,
            0,
            decodeResource.width,
            decodeResource.height,
            matrix,
            true
        )
    }

    fun set(cubeMoveFeastData: CubeMoveFeastData) {
        this.type = cubeMoveFeastData.type
        this.color = cubeMoveFeastData.color
        this.centerText = cubeMoveFeastData.centerText
    }

}