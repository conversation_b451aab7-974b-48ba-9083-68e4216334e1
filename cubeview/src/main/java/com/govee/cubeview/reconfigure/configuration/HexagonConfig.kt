package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.HexagonView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.HexagonParams

/**
 *     author  : sinrow
 *     time    : 2022/10/9
 *     version : 1.0.0
 *     desc    : 普通六边形 --- H6061
 */
class HexagonConfig : AbsShapeConfig() {
    override fun configShapeParams(): BaseShapeParams {
        return HexagonParams()
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        return HexagonView(context).apply { setConfig(this@HexagonConfig) }
    }

}

