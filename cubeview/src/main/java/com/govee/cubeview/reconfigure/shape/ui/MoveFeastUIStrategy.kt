package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/12/2
 *     version : 1.0.0
 *     desc    :
 *
 */
open class MoveFeastUIStrategy(val shapeView: BaseShapeView) : ShapeUIStrategy {
    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    /**
     * @param state 当前状态
     *         0：主颜色
     *         1：灭灯
     *         2：区域颜色
     */
    override fun updateState(params: BaseShapeParams, state: Int) {
        params.let {
            it.paintBaseShape.color =
                shapeView.toColor(com.govee.ui.R.color.ui_color_block_style_52_color)
            it.paintCenterText.color =
                shapeView.toColor(com.govee.ui.R.color.font_style_20_textColor)
            it.paintShapeBackground.color =
                shapeView.toColor(shapeView.moveFeastData.color)

            shapeView.moveFeastData.also { moveFeast ->
                if (moveFeast.isDrawCenterText()) {
                    it.centerText = shapeView.moveFeastData.centerText
                } else if (moveFeast.isDrawCenterDrawable()) {
                    it.centerText = ""
                }
            }
        }
    }

    override fun getType(): Type = Type.MoveFeast

}