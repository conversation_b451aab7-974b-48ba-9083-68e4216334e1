package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.graphics.Canvas
import android.graphics.PointF
import android.widget.ImageView
import com.govee.cubeview.gone
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.visible
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2023/4/28
 *     version : 1.0.0
 *     desc    : 双流（输入、输出边）
 */
class DoubleStreamPathView(context: Context) : ImgPathShapeView(context) {

    private val outputImgView by lazy { mutableListOf<ImageView>() }
    private val inputImgViews by lazy { mutableListOf<ImageView>() }

    companion object {
        //边长
        const val SIZE_WIDTH = 16//边长
        const val SIZE_HEIGHT = 23//边长

    }

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
//        init()
    }

    override fun init() {
        inputImgView.maxWidth = sizeWith
        inputImgView.maxHeight = sizeHeight
        inputImgView.scaleType = ImageView.ScaleType.CENTER_INSIDE
        inputImgView.rotation = mRotation
    }

    override fun setData(angle: Float, numText: Int) {

    }

    override fun setData(pathEntity: PathEntity) {
        pathEntity.run {
            inputImgViews.clear()
            input.forEach {
                val inputImageView = createInputImageView()
                inputImageView.rotation = it.first
                setLayoutParams4ImageView(inputImageView, it.second)
                addView(inputImageView)
                inputImgViews.add(inputImageView)
            }
            outputImgView.clear()
            outputs.forEach {
                val outputImageView = createOutputImageView()
                outputImageView.rotation = it.first
                setLayoutParams4ImageView(outputImageView, it.second)
                addView(outputImageView)
                outputImgView.add(outputImageView)
            }
            change2UnInstalledState()
        }
    }

    private fun createOutputImageView(): ImageView {
        return ImageView(context).apply {
            this.maxWidth = sizeWith
            this.maxHeight = sizeHeight
            this.scaleType = ImageView.ScaleType.CENTER_INSIDE
            this.rotation = mRotation
            this.setImageResource(com.govee.ui.R.mipmap.new_light_606a_pics_buxian_start_chu)
        }
    }

    private fun createInputImageView(): ImageView {
        return ImageView(context).apply {
            this.maxWidth = sizeWith
            this.maxHeight = sizeHeight
            this.scaleType = ImageView.ScaleType.CENTER_INSIDE
            this.rotation = mRotation
            this.setImageResource(com.govee.ui.R.mipmap.new_light_606a_pics_buxian_start_ru)
        }
    }

    override fun setLayoutParams(pointF: PointF) {

    }

    private fun setLayoutParams4ImageView(imageView: ImageView, pointF: PointF) {
        pos.x = pointF.x
        pos.y = pointF.y
        val lp = LayoutParams(sizeWith, sizeHeight)
        val left = pointF.x.roundToInt() - sizeWith / 2
        val top = pointF.y.roundToInt() - sizeHeight / 2
        lp.leftMargin = left
        lp.topMargin = top
        imageView.layoutParams = lp
    }

    override fun change2InstallingState() {
        this.alpha = 1f
        inputImgViews.onEach {
            it.visible()
        }
        outputImgView.onEach {
            it.visible()
        }
    }

    override fun change2InstalledState() {
        inputImgViews.onEach {
            it.gone()
        }
        outputImgView.onEach {
            it.gone()
        }
    }

    override fun change2UnInstalledState() {
        inputImgViews.onEach {
            it.gone()
        }
        outputImgView.onEach {
            it.gone()
        }
    }

    override fun onDraw(canvas: Canvas) {
    }

}