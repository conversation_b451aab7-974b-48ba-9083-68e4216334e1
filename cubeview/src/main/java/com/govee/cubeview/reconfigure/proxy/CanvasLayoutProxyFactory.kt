package com.govee.cubeview.reconfigure.proxy

import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout

/**
 *     author  : sinrow
 *     time    : 2023/8/10
 *     version : 1.0.0
 *     desc    :
 */

internal object CanvasLayoutProxyFactory {

    fun create(type: PanelLampType, baseCanvasLayout: BaseCanvasLayout): CanvasLayoutProxyV1 {
        return if (type == PanelLampType.SPACE_HEXAGON) {
            CanvasLayoutH606A(baseCanvasLayout)
        } else if (type.isH6063Shapes()) {
            CanvasLayoutH6063(baseCanvasLayout)
        } else if (type == PanelLampType.SQUARE) {
            CanvasLayoutH6069(baseCanvasLayout)
        } else {
            CanvasLayoutProxyV1(baseCanvasLayout)
        }
    }

}