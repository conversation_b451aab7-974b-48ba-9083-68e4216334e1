package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.view.animation.Animation
import android.view.animation.TranslateAnimation
import android.widget.ImageView
import com.govee.cubeview.gone
import com.govee.cubeview.reconfigure.PathEntity
import com.govee.cubeview.visible

/**
 *     author  : sinrow
 *     time    : 2022/11/17
 *     version : 1.0.0
 *     desc    : 动态路径图形 ，作用于 Y 形灯
 */
class DynamicImgPathShapeView(context: Context) : ImgPathShapeView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 17//边长
        const val SIZE_HEIGHT = 40//边长
    }

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    override fun init() {
        inputImgView.maxWidth = sizeWith
        inputImgView.maxHeight = sizeHeight
        inputImgView.scaleType = ImageView.ScaleType.CENTER_INSIDE
        rotation = mRotation
        setInputImageResource(com.govee.ui.R.mipmap.new_light_6065_pics_buxian_start)
    }

    override fun setData(angle: Float, numText: Int) {
        this.mRotation = angle
        this.numText = numText
        rotation = mRotation
    }

    override fun setData(pathEntity: PathEntity) {
        pathEntity.let {
            this.mRotation = it.rotation
            this.numText = it.pathNum
            rotation = mRotation
        }
    }

    override fun doSomething(block: () -> Unit) {
        // 滑动角度和中心点上下移动
        super.doSomething(block)
        gone()
    }

    private fun startAnimation() {

        val startPointFX = startPointF.x - pos.x
        val startPointFY = startPointF.y - pos.y

        val endPointFX = endPointF.x - pos.x
        val endPointFY = endPointF.y - pos.y

        val tAnim = TranslateAnimation(
            startPointFX,
            endPointFX,
            startPointFY,
            endPointFY
        ) //设置视图上下移动的位置

        tAnim.duration = 1000
        tAnim.repeatCount = Animation.INFINITE
        tAnim.repeatMode = Animation.INFINITE
        this.animation = tAnim
        tAnim.start()
    }

    private fun stopAnimation() {
        clearAnimation()
    }

    override fun change2InstallingState() {
        super.change2InstallingState()
        visible()
        startAnimation()
    }

    override fun change2InstalledState() {
        visible()
        stopAnimation()
    }

    override fun change2UnInstalledState() {
        stopAnimation()
        gone()
    }

}