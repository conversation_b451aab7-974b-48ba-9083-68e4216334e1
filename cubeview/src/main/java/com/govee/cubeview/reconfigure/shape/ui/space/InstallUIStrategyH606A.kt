package com.govee.cubeview.reconfigure.shape.ui.space

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.BaseShapeParams
import com.govee.cubeview.reconfigure.shape.ui.ShapeUIStrategy
import com.govee.cubeview.reconfigure.shape.ui.SpaceHexagonParams
import com.govee.cubeview.reconfigure.shape.ui.Type
import com.govee.cubeview.showLog
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2023/5/15
 *     version : 1.0.0
 *     desc    : 606A 需要显示输入输出边
 */
open class InstallUIStrategyH606A(private val shapeView: BaseShapeView) : ShapeUIStrategy {

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        if (params !is SpaceHexagonParams) return
        params.let {
            showLog("InstallUIStrategyH606A state = $state")
            it.showInputTag = false
            it.showOutputTag = false
            when (state) {
                0 -> {
                    shapeView.alpha = 1f
                    it.shapeColor = shapeView.toColor(it.installingColor)
                    it.paintBaseShape.color = shapeView.toColor(it.installedStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(it.installingColor)
                    it.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFEAEAEA)
                }
                1 -> {
                    shapeView.alpha = 1f
                    it.shapeColor = shapeView.toColor(it.installingColor)
                    it.paintBaseShape.color = shapeView.toColor(it.installingStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(it.installingColor)
                    it.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFEAEAEA)
                    it.showInputTag = true
                    it.showOutputTag = true
                    it.colorInputTextBg = params.installInputCenterTextBgColor
                    it.colorOutputTextBg = params.installOutputCenterTextBgColor
                }
                2 -> {
                    shapeView.alpha = 1f
                    it.shapeColor = shapeView.toColor(it.installingColor)
                    it.paintBaseShape.color = shapeView.toColor(it.installedStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(it.installingColor)
                    it.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFEAEAEA)
                }
            }
            it.paintShapeBackground.color = it.shapeColor
        }
    }


    override fun getType(): Type = Type.Install
}