package com.govee.cubeview.reconfigure.configuration

import android.content.Context
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.HexagonSolidView
import com.govee.cubeview.reconfigure.shape.ui.*

/**
 *     author  : sinrow
 *     time    : 2022/10/27
 *     version : 1.0.0
 *     desc    : 立体六边形 --- H6066
 */
class HexagonSolidConfig : AbsShapeConfig() {
    override fun configShapeParams(): BaseShapeParams {
        return HexagonSolidParams()
    }

    override fun configUIStrategy(shapeView: BaseShapeView): HashMap<Type, ShapeUIStrategy> {
        return super.configUIStrategy(shapeView).apply {
            InstallUIStrategyH6066(shapeView).let { put(it.getType(), it) }
            CheckUIStrategy(shapeView).inject(this)
            MoveFeastUIStrategyH6066(shapeView).inject(this)
        }
    }

    override fun createShapeView(context: Context, cubeShapeType: Int): BaseShapeView {
        return HexagonSolidView(context).apply { setConfig(this@HexagonSolidConfig) }
    }

}