package com.govee.cubeview.reconfigure.canvas

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.widget.FrameLayout
import com.govee.cubeview.CanvasLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.proxy.CanvasLayoutProxyFactory
import com.govee.cubeview.reconfigure.shape.ui.OnShapeListener
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/9/6
 *     version : 1.0.0
 *     desc    :
 *     这个基类主要用于隔离基础业务逻辑，比如说流程：
 *     提供了画布的基础功能：
 *     1、尺寸大小
 *     2、对外的通用方法
 *     3、提供 addView/removeView
 *
 *     <P>
 *         1、初始化画布参数
 *         2、设置图形参数
 *
 *     </P>
 */
abstract class BaseCanvasLayout : FrameLayout, IBasicCanvasLayout {

    val TAG = javaClass.simpleName.toString()
    internal val canvasLayoutProxyV1 by lazy { CanvasLayoutProxyFactory.create(cubeType, this) }

    internal var mModeType = CanvasLayoutModeType.DefaultType
    internal var cubeType = PanelLampType.HEXAGON
    internal var listener: OnShapeListener? = null
    var canvasSize = 0//画布布局
    var changeLayoutParams: CanvasLayout.OnChangeLayoutParams? = null

    constructor(context: Context) : super(context) {
        initAttrs(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initAttrs(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initAttrs(context, attrs)
    }

    private fun initAttrs(context: Context?, attrs: AttributeSet?) {
        context?.obtainStyledAttributes(attrs, R.styleable.BaseCanvasLayout)?.let {
            val shapeType =
                it.getInt(R.styleable.BaseCanvasLayout_cubeShapeViewType, Shape.TYPE_DEFAULT)
            restCanvasLayoutParams(shapeType)
            it.recycle()
        }
    }

    private fun restCanvasLayoutParams(cubeShapeViewType: Int) {
        canvasSize = Constants.getCanvasLayoutSize(cubeShapeViewType)
    }

    override fun initParams() {
        val lp: LayoutParams? = layoutParams as? LayoutParams

        lp?.apply {
            this.width = canvasSize
            this.height = canvasSize
            this.gravity = Gravity.CENTER
            layoutParams = this
        }
        clipChildren = false // 防止 view 被 layoutParams 裁剪,需要设置到根布局才生效
        listener?.let { canvasLayoutProxyV1.registerListener(it) }
    }

    override fun setModeType(ty: CanvasLayoutModeType): BaseCanvasLayout {
        mModeType = ty
        return this
    }

    override fun setCubeType(cubeType: PanelLampType): BaseCanvasLayout {
        this.cubeType = cubeType
        listener?.let { canvasLayoutProxyV1.registerListener(it) }
        return this
    }

    override fun setShapeData(points: ArrayList<ShapePosition>?) {
        initParams()
        showLog(TAG, "setShapeData:  ")
        createShapeView(points)
    }

    override fun registerListener(listener: OnShapeListener): BaseCanvasLayout {
        this.listener = listener
        return this
    }

    fun setCubeShapeViewType(type: PanelLampType, size: Int): BaseCanvasLayout {
        this.cubeType = type
        canvasSize = size
        changeLayoutParams?.apply { layoutParams(width, height) }
        return this
    }

    open fun createShapeView(points: ArrayList<ShapePosition>?, needUpdateCanvas: Boolean = true) {
    }

    open fun updateShapeState(modeType: CanvasLayoutModeType = this.mModeType) {
    }

    open fun setShapeData(points: ArrayList<ShapePosition>?, needUpdateCanvas: Boolean) {
        initParams()
        showLog(TAG, "setShapeData:  ")
        createShapeView(points, needUpdateCanvas)
    }
}