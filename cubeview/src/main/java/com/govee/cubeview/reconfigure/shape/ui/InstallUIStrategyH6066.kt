package com.govee.cubeview.reconfigure.shape.ui

import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.toColor

/**
 *     author  : sinrow
 *     time    : 2022/9/30
 *     version : 1.0.0
 *     desc    :
 */
class InstallUIStrategyH6066(private val shapeView: BaseShapeView) : ShapeUIStrategy {

    override fun defaultState(params: BaseShapeParams) {
        updateState(params, 0)
    }

    override fun updateState(params: BaseShapeParams, state: Int) {
        params.let {
            when (state) {
                0 -> {
                    shapeView.alpha = 0.4f
                    it.shapeColor = shapeView.toColor(it.unInstalledColor)
                    it.paintBaseShape.color = shapeView.toColor(it.unInstalledStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(params.defaultCenterTextColor)
                    it.paintAttributes.color =
                        shapeView.toColor(com.govee.ui.R.color.ui_line_style_1_1_stroke_color)

                }
                1 -> {
                    shapeView.alpha = 1f
                    it.shapeColor = shapeView.toColor(it.installingColor)
                    it.paintBaseShape.color = shapeView.toColor(it.installingStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(it.installingCenterTextColor)
                    it.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFEAEAEA)
                }
                2 -> {
                    shapeView.alpha = 1f
                    it.shapeColor = shapeView.toColor(it.installingColor)
                    it.paintBaseShape.color = shapeView.toColor(it.installedStrokeColor)
                    it.paintCenterText.color = shapeView.toColor(it.installedCenterTextColor)
                    it.paintAttributes.color = shapeView.toColor(com.govee.ui.R.color.FFEAEAEA)
                }
            }
            it.paintShapeBackground.color = it.shapeColor
        }
    }


    override fun getType(): Type = Type.Install

}