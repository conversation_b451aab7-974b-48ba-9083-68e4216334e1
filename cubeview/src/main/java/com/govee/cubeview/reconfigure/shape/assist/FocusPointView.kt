package com.govee.cubeview.reconfigure.shape.assist

import android.content.Context
import android.widget.ImageView
import com.govee.cubeview.shape.FocusPoint
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/10/12
 *     version : 1.0.0
 *     desc    : 聚焦固定图形 -- 作用于 Y形灯，当前效果的选中点
 */
class FocusPointView(context: Context) : BaseFocusView(context) {

    companion object {
        //边长
        const val SIZE_WIDTH = 29//边长
        const val SIZE_HEIGHT = 29//边长
    }

    var focusPointMsg = FocusPoint(0f, 0f, 0, 0, 0, 0)

    init {
        sizeWith = SIZE_WIDTH
        sizeHeight = SIZE_HEIGHT
        init()
    }

    private fun init() {
        inputImgView.maxWidth = SIZE_WIDTH
        inputImgView.maxHeight = SIZE_HEIGHT
        inputImgView.scaleType = ImageView.ScaleType.FIT_CENTER
        setBackgroundResource(R.drawable.component_ui_point_style_9_selector)
    }

    override fun doSomething(block: () -> Unit) {
        super.doSomething(block)
        setOnClickListener {
            focusPointCallback?.invoke(focusPointMsg)
        }
    }
}