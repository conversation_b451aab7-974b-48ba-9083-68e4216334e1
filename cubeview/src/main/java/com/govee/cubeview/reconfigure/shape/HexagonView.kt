package com.govee.cubeview.reconfigure.shape

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import com.govee.cubeview.checkAngle
import com.govee.cubeview.reconfigure.shape.ui.toSetFloatArray
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2022/9/15
 *     version : 1.0.0
 *     desc    :
 */
class HexagonView(context: Context) : BusinessShapeView(context) {

    /*已左上角的点作为旋转的起始方向*/
    override fun getNextShapeCenterPointByRotation(
        pointF: PointF,
        length: Float,
        rotation: Float
    ): PointF {
        return PointF().apply {
            val matrix = Matrix()
            matrix.setRotate(rotation - 30f, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(
        centerPointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<PointF> {
        return Array(6) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(rotation, centerPointF.x, centerPointF.y)
            /*六边形，最左边的点为起始点 0 ，默认角度为 60，经过旋转后，变成了左上角顶点*/
            /*获取辅助图形的旋转点，需要从左上角开始，这样就可以达到了要求了*/

            val shapePoint = Array(6) { FloatArray(2) }
            shapePoint[0] =
                floatArrayOf(
                    (length * cos(2 * PI * 3 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 3 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[1] =
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[2] =
                floatArrayOf(
                    (length * cos(2 * PI * 5 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 5 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[3] =
                floatArrayOf(
                    (length * cos(0.0) + centerPointF.x).toFloat(),
                    (length * sin(0.0) + centerPointF.y).toFloat()
                )
            shapePoint[4] =
                floatArrayOf(
                    (length * cos(2 * PI * 1 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 1 / 6) + centerPointF.y).toFloat()
                )
            shapePoint[5] =
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 6) + centerPointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + centerPointF.y).toFloat()
                )

            matrix.mapPoints(shapePoint[0])
            matrix.mapPoints(shapePoint[1])
            matrix.mapPoints(shapePoint[2])
            matrix.mapPoints(shapePoint[3])
            matrix.mapPoints(shapePoint[4])
            matrix.mapPoints(shapePoint[5])

            toSetFloatArray(shapePoint)
        }
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        /*directionTag： 建议以 index 作为序号*/
        /* 0->360 */
        /* 1-> 60 */
        /* 2->120 */
        /* 3->180 */
        /* 4->240 */
        /* 5->300 */
        return directionTag * 60f
    }

    override fun getPathShapeCenterPoints(): PointF {
        return getNextShapeCenterPoints(offsetRotation + 180, params.innerLineLength)
    }

    override fun getPathShapePathNum(lastOffsetRotation: Float): Int {
        return (offsetRotation - lastOffsetRotation).checkAngle().roundToInt() / 60
    }

}