package com.govee.cubeview.reconfigure.shape.gamewall

import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import com.govee.cubeview.reconfigure.configuration.GameWallConfig
import com.govee.cubeview.reconfigure.shape.BusinessShapeView
import com.govee.cubeview.reconfigure.shape.ui.ShapeData
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import kotlin.math.PI
import kotlin.math.ceil
import kotlin.math.cos
import kotlin.math.sin

/**
 *     author  : sinrow
 *     time    : 2023/11/28
 *     version : 1.0.0
 *     desc    : 长方形
 */
class GameRectangleView(context: Context) : BusinessShapeView(context) {

    private var lastNotePosition: Int = -1
    override fun initShapeData(shapeData: ShapeData) {
        super.initShapeData(shapeData)
        val shapePosition = shapeData.shapePosition

        // 如果主电源有赋值，则按照辅助电源形式显示
        shapePosition.mainPower?.let {
            mainPower.powerEdgeNUmber
            mainPower.powerNumber = it.powerNumber
            mainPower.powerEdgeNUmber = it.powerEdgeNUmber
            mainPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMainPower = true
        }
        shapePosition.ext?.let {
            mMultiPower.powerNumber = it.powerNumber
            mMultiPower.powerEdgeNUmber = it.powerEdgeNUmber
            mMultiPower.optionalEdgeNumbers = it.optionalEdgeNumbers
            params.isMultiPowerSelected = true
        }
        params.inputNum = shapePosition.inputNum
        params.outputNum = shapePosition.outputNum
        lastNotePosition = shapePosition.lastPosition
    }

    override fun getNextShapeCenterPointByRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        showLog(TAG, "getNextShapeCenterPointByRotation() length = $length,rotation = $rotation")
        return PointF().apply {// 45 = x+180
            val matrix = Matrix()
            matrix.setRotate(rotation - 90, pointF.x, pointF.y)
            val fa =
                floatArrayOf(
                    (length * cos(2 * PI * 0 / 4) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 0 / 4) + pointF.y).toFloat()
                )
            /*旋转*/
            matrix.mapPoints(fa)
            this.x = fa[0]
            this.y = fa[1]
        }
    }

    override fun getShapePoint(centerPointF: PointF, length: Float, rotation: Float, forSelf: Boolean): Array<PointF> {
        showLog(TAG, "getShapePoint() length = $length , rotation = $rotation , forSelf = $forSelf")
        return Array(4) { PointF() }.apply {
            val matrix = Matrix()//旋转
            matrix.setRotate(needRotation(rotation), centerPointF.x, centerPointF.y)
            val shapePoint = Array(4) { FloatArray(2) }.apply {
                val nextX = GameWallConfig.GameRectangleViewHeight
                val nextY = GameWallConfig.GameRectangleViewWidth / 2

                if (forSelf) {
//                    val nextX = length * cos(scale)
//                    val nextY = length * sin(scale)

                    showLog(TAG, "getShapePoint() nextX ${nextX * 2}")
                    showLog(TAG, "getShapePoint() nextY ${nextY * 2}")
                    this[0] =// 左上角
                        floatArrayOf(
                            centerPointF.x - nextX,
                            centerPointF.y - nextY
                        )
                    this[1] = // 右上角
                        floatArrayOf(
                            centerPointF.x + nextX,
                            centerPointF.y - nextY
                        )
                    this[2] =// 右下角
                        floatArrayOf(
                            centerPointF.x + nextX,
                            centerPointF.y + nextY
                        )
                    this[3] =// 左下角
                        floatArrayOf(
                            centerPointF.x - nextX,
                            centerPointF.y + nextY
                        )
                    matrix.mapPoints(this[0])
                    matrix.mapPoints(this[1])
                    matrix.mapPoints(this[2])
                    matrix.mapPoints(this[3])
                } else {
                    this[0] = // 第一条边
                        floatArrayOf(
                            centerPointF.x + nextX,
                            centerPointF.y
                        )
                    this[1] = // 第二条边
                        floatArrayOf(
                            centerPointF.x - nextX,
                            centerPointF.y
                        )
                    matrix.mapPoints(this[0])
                    matrix.mapPoints(this[1])
                }
            }

            forEachIndexed { index, pointF ->
                val floats = shapePoint[index]
                pointF.x = floats[0]
                pointF.y = floats[1]
            }
        }
    }

    override fun needRotation(rotation: Float): Float {
        return rotation + 90
    }

    override fun showSerialNumber(): Boolean {
        return false
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        return if (directionTag == 0) {
            180f
        } else {
            0f
        }
    }

    override fun toShapePosition(): ShapePosition {
        return ShapePosition(
            params.shapeType, ceil(centerPos.x), ceil(centerPos.y), getRotationValue(), 0, params.connectState
        ).apply {
            if (params.isMultiPowerSelected && !mMultiPower.isDefault()) {
                this.ext = mMultiPower
            }
            if (params.isMultiPowerSelected && !<EMAIL>()) {
                this.mainPower = <EMAIL>
            }
            this.editable = params.editable
            this.inputNum = params.inputNum
            this.outputNum = params.outputNum
            this.lastPosition = lastNotePosition
        }
    }

    override fun drawCenterDrawBitmap(canvas: Canvas, paint: Paint) {
        moveFeastData.let {
            if (it.isDrawCenterDrawable()) {
                val bitmap = it.getCenterDrawable(context, 80f)
                val left = (width - bitmap.width) / 2.toFloat()
                val top = (height - bitmap.height) / 2.toFloat()

                canvas.drawCircle(
                    width / 2f,
                    height / 2f,
                    20f,
                    params.paintShapeBackground
                )

                canvas.drawBitmap(
                    bitmap,
                    left, top,
                    paint
                )

            }
        }
    }

    override fun doSomething4ColorMode(block: () -> Unit) {
        if (modeType.isColorMode()) {
            singleClickListener = block
        }
    }

    override fun doSomething4MoveFeast(block: () -> Unit) {
        if (modeType.isMoveFeast()) {
            singleClickListener = block
        }
    }

}