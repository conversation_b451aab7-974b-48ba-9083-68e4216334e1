package com.govee.cubeview.reconfigure.proxy

import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.cubeview.no
import com.govee.cubeview.reconfigure.DataProcessCenter.checkContainer
import com.govee.cubeview.reconfigure.DataProcessCenter.isContainer
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.ui.addAssistShapeCenterPoints
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.yes

/**
 *     author  : sinrow
 *     time    : 2024/6/20
 *     version : 1.0.0
 *     desc    :
 */
class CanvasLayoutH6069(canvasLayout: BaseCanvasLayout) : CanvasLayoutProxyV1(canvasLayout) {

    override fun updatePowerView() {
        if (shapeViews.isEmpty()) return
        config.createPowerView(context).apply {
            val edge = 20
            resetSize(edge, edge, 5)
            val first = shapeViews.first()
            val inputNum = first.params.inputNum
            val inputEdges = Utils.parseBytes4Bit(inputNum.toByte(), true).indexOfFirst { it == true }
                .coerceAtLeast(0)// true  0010 0010  false ture
            showLog(TAG, "updatePowerView() inputEdges = $inputEdges")

            val nextShapeCenterPoints = first.getNextShapeCenterPoints(
                first.offsetRotation + first.params.powerImgRotationOffset + inputEdges * 90,
                first.params.nextShapeCenterLineLength + 5 + 3
            )

            setLayoutParams(nextShapeCenterPoints)
        }.let {
            powerViews.add(it)
            canvasLayout.addView(it)
        }
    }

    override fun updatePowerItemView() {
        if (shapeViews.isEmpty()) return
        if (config.ignorePowerView()) return
        config.createPowerView(context).apply {
            val edge = 20
            resetSize(edge, edge, 5)
            val first = shapeViews.first()
            val inputNum = first.params.inputNum
            val inputEdges = Utils.parseBytes4Bit(inputNum.toByte(), true).indexOfFirst { it == true }
                .coerceAtLeast(0)// true  0010 0010  false ture

            val nextShapeCenterPoints = first.getNextShapeCenterPoints(
                first.offsetRotation + first.params.powerImgRotationOffset + inputEdges * 90,
                first.params.nextShapeCenterLineLength + 5 + 3
            )

            setLayoutParams(nextShapeCenterPoints)
        }.let {
            powerViews.onEach { canvasLayout.removeView(it) }.clear()
            powerViews.add(it)
            canvasLayout.addView(it)
        }
    }

    /**
     * 606A 的辅助电源的选择规则发生变更
     * 1、可选择的块高亮，支持用户点击选择，选中后可以再次取消选中
     * 2、第一个电源的规则：6个边有一个边没有接就可以选择
     * 3、第二个电源的规则：需要和第一个电源位置隔开，灯板和第一个电源的边有相接的部分不可选，在这个基础上 6 个边有1个边没接就可选
     * 4、主电源、副电源的区分：x、y 最大的则为主电源
     * 现象：如果选择第一个之后，剩下的不支持第二个电源选择，则提示该选项无法支持第二个电源选择（需要加一个标记位）
     *
     */
    override fun updateMultiPowerView() {
        pairMultiPowerList.clear()
        val size = shapeViews.size
        if (size <= 0) return
        val shapeType = shapeViews.first().params.shapeType
        val powerBoundary = Constants.getShapePowerBoundary(shapeType)
        // 大于 12 块就要选择双电源（主、辅）
        var selectNumber = 2
        if (size <= powerBoundary) {
            selectNumber = 1
        }
        powerViewPoints.clear()
        var shapeView1: BaseShapeView? = null/*当前选择的电源图形1 */
        var shapeView2: BaseShapeView? = null/*当前选择的电源图形2 */
        shapeViews
            .filter { (!it.mMultiPower.isDefault() || !it.mainPower.isDefault()) }
            .takeIf { it.isNotEmpty() }
            ?.let {
                it.forEach { baseShapeView ->
                    // 遍历得到当前哪块已经标记了电源信息，并且此信息中，是在范围内，则记录起来。
                    baseShapeView.getAvailMultiPower2()?.let { multiPower ->
                        val assistShapeCenterPoints = baseShapeView.getNextShapeCenterPoints(
                            baseShapeView.offsetRotation + baseShapeView.params.powerImgRotationOffset + multiPower.powerEdgeNUmber * 90,
                            baseShapeView.params.nextShapeCenterLineLength + 5 + 3
                        )
//                        if (checkAreaValid(assistShapeCenterPoints)) {
                        // !baseShapeView.params.isMainPower
                        // 标记此电源可用,赋值电源坐标信息
                        baseShapeView.params.isMultiPowerSelected = true
                        multiPower.powerPointF.set(assistShapeCenterPoints.x, assistShapeCenterPoints.y)
                        powerViewPoints.add(baseShapeView)
//                        }
                    }
                }
                powerViewPoints.takeIf { it.isNotEmpty() }?.let { hasPowerShapeView ->
                    showLog(TAG, "updateMultiPowerView() hasPowerShapeView size = ${hasPowerShapeView.size}")
                    if (hasPowerShapeView.size >= 2) {
                        shapeView1 = hasPowerShapeView[0]
                        shapeView2 = hasPowerShapeView[1]
                    } else {
                        shapeView1 = hasPowerShapeView.lastOrNull()
                    }
                }
            }
        showLog(TAG, "updateMultiPowerView() shapeView1 = ${shapeView1.hashCode()}")
        showLog(TAG, "updateMultiPowerView() shapeView2 = ${shapeView2.hashCode()}")

        shapeView1?.let {
            showMultiPowerView(it)
        }
        shapeView2?.let {
            showMultiPowerView(it)
        }
        powerViews.forEach {
            showLog("第一个电源位置地方 ${it.x}  ${it.y}")
        }
        powerViews.clear()
    }

    /**
     *
     * 刷新当前电源选择 UI
     * @param selectNumber 设置可选择电源数量
     */
    private fun resetUI(selectNumber: Int) {
        /*
        1、可以自由选择电源位置，如果数量超过 12 个 ，则第一个是主电源、第二个是辅电源
        2、第一个电源和第二个电源不能相连，所以每选择一次电源就要把电源的周边置灰
        3、重新选择电源则表示取消，若当前已经有电源显示，则选择辅电源，最多是两个
        4、若电源位置是不可编辑的块，则无法编辑。
        */

        powerViewPoints.clear()// 临时存储电源附属的 baseShapeView
        pairMultiPowerList.firstOrNull()?.let {
            // 第一个显示规则
            powerViewPoints.add(it.first)
        }
        val hasNotSupportShape = mutableListOf<ShapePosition>()
        // 符合多电源选择
        powerViewPoints.firstOrNull()?.let {
            // 第一个显示规则
            for (i in 0..5) {
                val nextShapePosition = it.getNextShapePosition(i)
                hasNotSupportShape.add(nextShapePosition)
            }
        }
        shapeViews
            .filter { it.params.editable && (it.shapeState.state != 1) }
            .onEach { it.updateUIState(state = 2) }
            .forEachIndexed { shapeIndex, shapeView ->
                var areaValid = false
                var maxY = 0f
                if (!shapeView.isContainer(hasNotSupportShape)) {/*相邻的块都隐藏掉*/
                    shapeView.addAssistShapeCenterPoints().onEachIndexed { index, pointF ->
                        if (checkAreaValid(pointF)) {// 附近没有电源
                            val directionTag = Constants.getInstallNumberWithDirectionTag2(
                                shapeView.params.shapeType, index
                            )

                            val optionalEdgeNumbers = shapeView.mMultiPower.optionalEdgeNumbers
                            optionalEdgeNumbers.add(directionTag)

                            // 多个辅助点可画，优先 Y 轴最大（存在相同 Y 轴的情况下，与 iOS 保持一致）
                            if (pointF.y > maxY) {
                                maxY = pointF.y
                                // 记录辅助坐标（这个地方得确认下是什么关系,跟方向有关系）
                                val powerNumber = shapeIndex + 1
                                shapeView.mMultiPower.powerEdgeNUmber = directionTag
                                shapeView.mMultiPower.powerNumber = powerNumber
                                shapeView.mMultiPower.powerPointF.set(pointF.x, pointF.y)
                            }
                            areaValid = true
                        }
                    }
                }
                // 处理点击事件
                areaValid.yes {
                    shapeView.onClickListener {
                        /* ---- 在这里刷新 ---- */
                        handleSelectedClickListener(shapeView, selectNumber)
                    }
                    shapeView.updateUIState(state = 0)
                }.no {
                    // 不可用
                    shapeView.updateUIState(state = 2)
                    shapeView.setOnClickListener(null)
                }
            }
        listener?.selectedMultiPower(pairMultiPowerList.size)
    }

    private fun handleSelectedClickListener(baseShapeView: BaseShapeView, selectNumber: Int) {
        showLog(TAG, "handleSelectedClickListener()")

        takeIf {
            baseShapeView.shapeState.state == 1 && pairMultiPowerList.checkContainer(baseShapeView)
        }?.let {
            pairMultiPowerList.iterator().let {
                while (it.hasNext()) {
                    val next = it.next()
                    if (next.first == baseShapeView) {
                        val second = next.second
                        showLog(TAG, "updateSelectedMultiPowerView1() remove power = $second")
                        canvasLayout.removeView(second)
                        it.remove()
                        baseShapeView.params.isMainPower = false
                        baseShapeView.params.isMultiPowerSelected = false
                    }
                }
            }
            baseShapeView.updateUIState(state = 0)
            showLog(TAG, "updateSelectedMultiPowerView1() pairMultiPowerList size = ${pairMultiPowerList.size}")
            // 符合多电源选择
            resetUI(selectNumber)
            return
        }

        if (pairMultiPowerList.size >= selectNumber) {
            pairMultiPowerList.removeLastOrNull()?.let {
                showLog(TAG, "updateSelectedMultiPowerView1: 是否移除成功 it = $it ")
                it.second.let { canvasLayout.removeView(it) }
                it.first.updateUIState(state = 0)
                it.first.params.isMainPower = false
                it.first.params.isMultiPowerSelected = false
            }
            showLog(TAG, "updateSelectedMultiPowerView1() pairMultiPowerList size = ${pairMultiPowerList.size}")
        }

        baseShapeView.updateUIState(state = 1)

        val multiPowerView = config.createPowerView(context).apply {
            setLayoutParams(baseShapeView.mMultiPower.powerPointF)
            showLog(TAG, "updateSelectedMultiPowerView1() 添加的 power 对象 = $this")
            pairMultiPowerList.add(Pair(baseShapeView, this))
        }

        /*最后才添加是为了防止被路径辅助线覆盖*/
        multiPowerView.let { canvasLayout.addView(it) }

        resetUI(selectNumber)

    }

    private fun showMultiPowerView(baseShapeView: BaseShapeView) {
        showLog(TAG, "showMultiPowerView()")
        baseShapeView.getAvailMultiPower()?.let { multiPower ->
            config.createPowerView(context).apply {
                val edge = 20
                resetSize(edge, edge, 5)
                setLayoutParams(multiPower.powerPointF)
                showLog(TAG, "updateSelectedMultiPowerView1() 添加的 power 对象 = ${baseShapeView.mMultiPower.powerPointF}")
                canvasLayout.addView(this)
                powerViews.add(this)
                pairMultiPowerList.add(Pair(baseShapeView, this))
            }
        }
    }

}