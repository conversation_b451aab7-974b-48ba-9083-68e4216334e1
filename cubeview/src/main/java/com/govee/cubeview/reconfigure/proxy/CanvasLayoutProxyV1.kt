package com.govee.cubeview.reconfigure.proxy

import android.animation.Animator
import android.animation.ValueAnimator
import android.graphics.PointF
import android.graphics.Rect
import android.view.animation.DecelerateInterpolator
import androidx.annotation.UiThread
import com.govee.cubeview.CanvasLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.FlipAnimationUtil
import com.govee.cubeview.Utils
import com.govee.cubeview.log
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.PanelLampType
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout
import com.govee.cubeview.reconfigure.shape.BaseShapeView
import com.govee.cubeview.reconfigure.shape.BasicShapeBusiness
import com.govee.cubeview.reconfigure.shape.BusinessShapeView
import com.govee.cubeview.reconfigure.shape.assist.BaseFocusView
import com.govee.cubeview.reconfigure.shape.assist.FocusDragView
import com.govee.cubeview.reconfigure.shape.assist.FocusPointView
import com.govee.cubeview.reconfigure.shape.assist.ImgPathShapeView
import com.govee.cubeview.reconfigure.shape.data.CubeMoveFeastData
import com.govee.cubeview.reconfigure.shape.ui.IBasicBusiness
import com.govee.cubeview.shape.FocusPoint
import com.govee.cubeview.shape.ShapePosition
import com.govee.cubeview.showLog
import com.govee.cubeview.yes
import kotlin.math.ceil
import kotlin.math.roundToInt

/**
 *     author  : sinrow
 *     time    : 2022/9/13
 *     version : 1.0.0
 *     desc    : 旧的业务代理类(6061，6066，6067，6065)
 *              <p>
 *                  CanvasLayoutProxyV1 面板灯业务类
 *                  CanvasLayoutProxyV2 新形态方块灯
 *                  ……
 *              </p>
 */

open class CanvasLayoutProxyV1(protected val canvasLayout: BaseCanvasLayout) :
    BasicShapeBusiness(canvasLayout), IBasicBusiness {

    private var animList: MutableList<Animator> = mutableListOf()/*用于颜色模式-颜色变化动画*/

    companion object {
        /*创建默认Shape*/
        @JvmStatic
        fun generaDefaultShape(type: Int): ShapePosition {
            val canvasLayoutSize = Constants.getCanvasLayoutSize(type)
            val shapeDefaultAngle = Constants.getShapeDefaultAngle(type)
            return ShapePosition(
                type,
                (canvasLayoutSize / 2).toFloat(),
                (canvasLayoutSize / 2).toFloat(),
                shapeDefaultAngle,
                0,
                0
            )
        }
    }

    val curInstallIndex: Int
        get() {
            if (mModeType != CanvasLayoutModeType.Install) return 0
            return shapeViews.let { list ->
                var i = 0
                list.forEach { if (it.shapeState.state != 0) i++ }
                i
            }
        }

    /*针对于单个聚拢扩散的图形*/
    val focusPoint: PointF
        get() {
            return PointF().apply {
                focusViews.asSequence().filterIsInstance<FocusDragView>().firstNotNullOfOrNull {
                    x = it.pos.x
                    y = it.pos.y
                }
            }
        }

    override fun flipShapeViewByX() {
        /*执行动画翻转动画后，重新计算图形坐标等参数*/
        FlipAnimationUtil.startAnimation(canvasLayout, {
            canvasLayout.pivotX = canvasLayout.canvasSize / 2f
            val diff: Float = canvasLayout.resources.displayMetrics.density * 16000
            canvasLayout.cameraDistance = diff
        }, {
            flipByX()
        })
    }

    private fun flipByX() {
        // 核心思路
        // 1、重新计算翻转后中心坐标
        // 2、重新计算翻转角度

        val firstShapeCenterPoint = PointF()

        shapeViews.first().apply {
            firstShapeCenterPoint.x = centerPos.x
            firstShapeCenterPoint.y = centerPos.y
        }

        shapeViews.onEach {
            if (it is BusinessShapeView) {
                it.flipByX(firstShapeCenterPoint)
            }
        }
        config.updateOperatedShapeViews(mModeType, shapeViews)

        move2ShapeCenter(true)

        notifyRefreshShapeUI()
    }

    override fun setShapeRotation(onceRotation: Float) {
        shapeViews.first().let { first ->
            val centerPos = first.centerPos
            shapeViews.onEach {
                if (it is BusinessShapeView) {
                    it.horizontalRotation(onceRotation, centerPos)
                }
            }
        }
        notifyRefreshShapeUI()
    }

    override fun getShapeRotations(): ArrayList<ShapePosition> {
        return canvasLayout.cubeType.config.outputAfterConvertShapeData(shapeViews)
    }

    // 需要把一些特殊参数返回出去
    override fun installNextShape(changeLastShapeState: Boolean): Pair<BaseShapeView, Int>? {
        if (mModeType != CanvasLayoutModeType.Install) return null
        val edibles = shapeViews.filter { it.params.editable }.toMutableList()
        val hasInstallNum = edibles.filter { it.shapeState.state != 0 }.size

        /*最后一个校验完*/
        if (hasInstallNum >= edibles.size) {
            changeLastShapeState.yes {
                edibles.lastOrNull()?.let {
                    it.updateUIState()
                    pathViews[it]?.change2InstalledState()
                }
            }
            return null
        }

        /*更新之前的View*/
        for (i in 0 until hasInstallNum) {

            edibles[i].run {
                updateUIState(2)
                if (params.isMultiPowerSelected) {
                    multiPowerPathView?.change2InstalledState()
                }
                /*只有可编辑时才显示路径*/
                if (params.editable) {
                    pathViews[this]?.change2InstalledState()
                }
            }
        }

        val currentShapeView = edibles[hasInstallNum]

        currentShapeView.updateUIState(1)

        pathViews[currentShapeView]?.change2InstallingState()

        /*相对于画布中心的位置*/
        return Pair(currentShapeView, hasInstallNum)
    }

    override fun installLastShape(): Pair<BaseShapeView, Int>? {
        val edibles = shapeViews.filter { it.params.editable }.toMutableList()
        var curInstallIndex =
            edibles.asSequence().filter { it.shapeState.state == 2 || it.shapeState.state == -1 }
                .toList().size // 6
        /*最后一个校验完*/
        if (curInstallIndex <= 0) {
            return null
        }
        curInstallIndex--
        // 首先得到当前已校准的块数和正在校准的块数
        // 然后根据块数自减，当前正在校准的 index

        edibles.onEachIndexed { index, baseShapeView ->
            var pathView: ImgPathShapeView? = null
            if (baseShapeView.params.editable) {
                pathView = pathViews[baseShapeView]
            }
            if (index > curInstallIndex) {
                // 表示未安装的
                baseShapeView.updateUIState(0)
                pathView?.change2UnInstalledState()
            } else if (index == curInstallIndex) {
                // 正在安装
                baseShapeView.updateUIState(1)
                pathView?.change2InstallingState()
            } else {
                // 已安装的
                baseShapeView.updateUIState(2)
                pathView?.change2InstalledState()
            }

        }
        /*相对于画布中心的位置*/
        return Pair(edibles[curInstallIndex], curInstallIndex)
    }

    override fun installAllShape() {
        val edibles = shapeViews.filter { it.params.editable }.toMutableList()
        edibles.onEachIndexed { index, baseShapeView ->
            baseShapeView.updateUIState(2)
            if (baseShapeView.params.editable) {
                pathViews[baseShapeView]?.change2InstalledState()
            }
        }
    }

    override fun checkNextShape(nextPos: Int): PointF? {
        if (!mModeType.isCheckMode()) return null
        val edibles = shapeViews.filter { it.params.editable }
        if (nextPos == 0) {
            /*若重新开始校准，则恢复状态*/
            edibles.forEach {
                it.updateUIState(0)
            }
        }

        /*更新之前的View*/
        for (i in 0 until nextPos) {
            edibles.getOrNull(i)?.updateUIState(2)
        }

        edibles.getOrNull(nextPos)?.updateUIState(1)

        if (nextPos + 1 > edibles.size) return null

        /*相对于画布中心的位置*/
        return if (nextPos >= edibles.size - 1) null else edibles[nextPos].centerPos
    }

    /*图形是否在画布上完全显示*/
    /* Rect.top 的值不为 0 时,View 要么部分可见,要么完全不可见*/
    fun isShapeAllVisible(): Boolean {
        val rect = Rect()
        shapeViews.forEach {
            if (!it.getGlobalVisibleRect(rect)) return false
        }
        return (shapeViews.size > 0 && !rect.isEmpty)
    }

    override fun checkAllShape(): Boolean {
        val edibles = shapeViews.filter { it.params.editable }
        edibles.onEach {
            it.updateUIState(2)
        }
        return false
    }

    override fun setSelectAll(isSelectedAll: Boolean): Boolean {
        if (!mModeType.isColorMode()) return false
        /*全选&取消全选*/
        shapeViews.forEach {
            it.isSelected = isSelectedAll
            it.invalidate()
        }
        return true
    }

    override fun updateFocusPoint(pointF: PointF, isIn: Boolean) {
        notifyRefreshShapeUI()
        focusViews.forEach {
            it.showFocusState(isIn)
            it.setLayoutParams(pointF)
        }
    }

    override fun setColorParams(
        colorList: ArrayList<Int>, brightnessList: ArrayList<Int>, selectedList: ArrayList<Boolean>
    ) {
        if (mModeType != CanvasLayoutModeType.ColorMode) return
        // 设置选中状态
        selectedList.onEachIndexed { index, b ->
            // 设置当前图形选择状态
            shapeViews.getOrNull(index)?.isSelected = b
        }

        // 渐变动画设置图形的颜色
        val lessColorSize = minOf(colorList.size, shapeViews.size)
        cancelAnimation4ColorMode()
        for (i in 0 until lessColorSize) {
            ValueAnimator.ofArgb(shapeViews[i].params.shapeColor, colorList[i]).apply {
                animList.add(this)
                addUpdateListener { valueAnimator ->
                    shapeViews[i].apply {
                        setShapeColor(valueAnimator.animatedValue as Int)
                        updateUIState()
                    }
                }
                interpolator = DecelerateInterpolator()
                duration = 300
                start()
            }
        }
        // 设置亮度
        val len4Brightness = minOf(brightnessList.size, shapeViews.size)
        for (i in 0 until len4Brightness) {
            shapeViews[i].run {
                setShapeCenterText(brightnessList[i].toString().plus("%"))
                invalidate()
            }
        }
    }

    private fun cancelAnimation4ColorMode() {
        val iterator = animList.iterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            next.removeAllListeners()
            next.pause()
            next.cancel()
            iterator.remove()
        }
    }

    fun updateFocusPoint(pointF: PointF) {
        if (focusViews.isEmpty()) {
            updateFocusView()
        }
        focusViews.forEach {
            it.showFocusState(it.isIn)
            it.setLayoutParams(pointF)
        }
    }

    /*移动到默认位置*/
    fun move2defaultPosition() {
        listener?.scaleAndTransition(mDefaultScale, mDefaultOffsetPoint)
    }

    @UiThread
    fun defColorUi() {
        /*关闭动画*/
        cancelAnimation4ColorMode()
        /*重置颜色*/
        val size = shapeViews.size
        for (i in 0 until size) {
            val shapeView = shapeViews[i]
            shapeView.resetColorAndBrightness()
            shapeView.invalidate()
        }
    }

    fun updateDataWithType(type: CanvasLayoutModeType, refresh: Boolean = false) {
        canvasLayout.takeIf { it.mModeType != type }?.setModeType(type)
            ?.setShapeData(getShapeRotations(), refresh)
    }

    /*图形是否在画布上不可见，有图形可见 或没有图形数据或图形未绘制完返回false*/
    fun isShapeInvisible(): Boolean {
        val rect = Rect()
        shapeViews.forEach {
            if (it.getGlobalVisibleRect(rect)) return false
        }
        return (shapeViews.size > 0 && !rect.isEmpty)
    }

    protected val mFocusPointList: MutableList<FocusPoint>
        get() {
            return checkFocusPointList()
        }

    open fun checkFocusPointList(): MutableList<FocusPoint> {
        return mutableListOf<FocusPoint>().apply {
            if (cubeType == PanelLampType.Y) {
                shapeViews.onEachIndexed { index, baseShapeView ->
                    //1、添加中心点坐标
                    baseShapeView.centerPos.run {
                        add(FocusPoint(x, y, shapeViews.size, index, 0xFF, 0xFF, true))
                    }
                    //2、得到顶点坐标集合
                    val shapePoint = baseShapeView.getShapePoint(
                        baseShapeView.centerPos,
                        baseShapeView.params.outsideLineLength,
                        baseShapeView.offsetRotation,
                        false
                    )
                    shapePoint.forEachIndexed { pointFIndex, floats ->
                        var tempIndex = pointFIndex + 1
                        if (tempIndex > 2) tempIndex = 0
                        add(
                            FocusPoint(
                                floats.x, floats.y, shapeViews.size, index, tempIndex,
                                /*默认值，这个参数是指，当前这点边沿着中心点的方向从0...3，默认参数:0xff*/
                                /*但是由于 5.2.0的 UI 效果是两个块中间，需要发 0xff*/
                                0xFF
                            )
                        )
                    }
                }
            }
        }
    }

    override fun updateFocusView() {
        if (cubeType != PanelLampType.Y) {
            return super.updateFocusView()
        }
        /*处理 Y 形灯的聚拢扩散逻辑*/
        showLog(TAG, "updateFocusView:  =  ")

        mFocusPointList.onEach {
            config.createFocusView(context).apply {
                if (this is FocusPointView) {
                    /*赋值给到 view*/
                    focusPointMsg.apply {
                        shapeTotalQuantity = it.shapeTotalQuantity
                        selectShapeIndex = it.selectShapeIndex
                        selectEdgeIndex = it.selectEdgeIndex
                        selectEdgeIcIndex = it.selectEdgeIcIndex
                        isCenterPoint = it.isCenterPoint
                    }
                }
                doSomething {
                    // Y 形灯
                    focusPointCallback = {
                        val params = byteArrayOf(
                            it.shapeTotalQuantity.toByte(),
                            it.selectShapeIndex.toByte(),
                            it.selectEdgeIndex.toByte(),
                            it.selectEdgeIcIndex.toByte()
                        )
                        listener?.selectedFocusPoint(params)
                        resetFocusPointStatus(this)
                    }
                }
                setLayoutParams(PointF(it.pointX, it.pointY))
            }.let {
                canvasLayout.addView(it)
                focusViews.add(it)
            }
        }
    }

    protected fun resetFocusPointStatus(focusPointView: BaseFocusView) {
        focusViews.forEach {
            it.isSelected = it == focusPointView
        }
    }

    protected fun handleYShapeCenterFocus(): FocusPoint {
        // Y 型灯，中心点计算方式
        // 1、计算当前图形的重心点，遍历当前可选择的点的位置累加得到重心点 x，y
        // 2、然后计算重心点到每一个可选择点的距离（图形端点*112，图形中心点系数*97）从小到大排序，得出最小距离的点的集合
        // 3、根据最小距离的点的集合，按照 Y 轴从小到大排序，取 Y 轴最大的集合
        // 4、若最大 Y 轴的点集合数量为1，则取该点。若数量>1，则按照 x 点从小到大排序，然后取中位数（/2），得到数组下标，取该点

        var heartPointX = 0f
        var heartPointY = 0f

        val list = mFocusPointList.onEach {
            heartPointX += it.pointX
            heartPointY += it.pointY
        }.apply {
            heartPointX /= size
            heartPointY /= size
        }.onEach {
            val distance =
                Utils.distance(PointF(heartPointX, heartPointY), PointF(it.pointX, it.pointY))
                    .roundToInt()
            if (it.isCenterPoint) {
                it.distance = distance * 97
            } else {
                it.distance = distance * 112
            }
        }.apply { sortBy { it.distance } }

        val first = list.first()

        val calculateList = mutableListOf<FocusPoint>()

        calculateList.apply {
            addAll(list.filter { it.distance == first.distance })
        }.sortBy {
            it.pointY
        }
        val last = calculateList.last()
        calculateList.filter { it.pointY == last.pointY }
        if (calculateList.size > 1) {
            calculateList.sortBy { it.pointX }
            val centerPointIndex = calculateList.size / 2
            calculateList[centerPointIndex]
        } else {
            calculateList.first()
        }.apply {
            return this
        }
    }

    open fun getYShapeDefaultFocusPoint(): ByteArray {
        log(CanvasLayout.TAG, "getCurrentFocusPoint4YShape()")
        val yShapeCenterFocusPoint = handleYShapeCenterFocus()

        val byteArrayOf = ByteArray(4)
        byteArrayOf[0] = yShapeCenterFocusPoint.shapeTotalQuantity.toByte()
        byteArrayOf[1] = yShapeCenterFocusPoint.selectShapeIndex.toByte()
        byteArrayOf[2] = yShapeCenterFocusPoint.selectEdgeIndex.toByte()
        byteArrayOf[3] = yShapeCenterFocusPoint.selectEdgeIcIndex.toByte()
        return byteArrayOf
    }

    open fun updateFocusPoint4YShape(params: ByteArray?): PointF {
        val pointF = PointF(shapeCenterPointF.x, shapeCenterPointF.y)
        if (focusViews.isEmpty()) return pointF
        params?.apply {
            log(
                CanvasLayout.TAG,
                "updateFocusPoint4YShape params = ${this[0]} ${this[1]} ${this[2]} ${this[3]}"
            )
            if (this[0].toInt() != shapeViews.size) {
                val yShapeCenterFocus = handleYShapeCenterFocus()
                this[0] = yShapeCenterFocus.shapeTotalQuantity.toByte()
                this[1] = yShapeCenterFocus.selectShapeIndex.toByte()
                this[2] = yShapeCenterFocus.selectEdgeIndex.toByte()
                this[3] = yShapeCenterFocus.selectEdgeIcIndex.toByte()
            }
            focusViews.onEach { it.isSelected = false }.filterIsInstance<FocusPointView>().filter {
                it.focusPointMsg.let { point ->
                    point.selectShapeIndex == Utils.byte2Int(this[1]) && point.selectEdgeIndex == Utils.byte2Int(
                        this[2]
                    ) && point.selectEdgeIcIndex == Utils.byte2Int(this[3])
                }
            }.forEach {
                it.isSelected = true
                pointF.x = it.pos.x
                pointF.y = it.pos.y
            }
        }
        return pointF
    }

    fun resetDefaultFocusPoint(callback: Boolean): PointF {
        log(CanvasLayout.TAG, "resetDefaultFocusPoint(), callback = $callback")
        val handleYShapeCenterFocus = handleYShapeCenterFocus()

        handleYShapeCenterFocus.let {
            val byteArrayOf1 = byteArrayOf(
                it.shapeTotalQuantity.toByte(),
                it.selectShapeIndex.toByte(),
                it.selectEdgeIndex.toByte(),
                it.selectEdgeIcIndex.toByte()
            )
            if (callback) {
                listener?.selectedFocusPoint(byteArrayOf1)
            } else {
                updateFocusPoint4YShape(byteArrayOf1)
            }
            return PointF(it.pointX, it.pointY)
        }
    }

    fun resetCanvas4YShapeAddLamp() {
        val hashMap = HashMap<Int, BaseShapeView>()
        shapeViews.onEachIndexed { index, baseShapeView ->
            baseShapeView.params.editable.yes {
                hashMap[index] = baseShapeView
            }
        }
        hashMap.forEach {
            handleClickDelShapeView(it.value)
        }
        notifyRefreshShapeUI()
    }

    /**
     * @param areaIndexSet 对应区域设置下标
     * @param switchConfigs 对应区域亮度熄灭设置下标
     */
    fun setShapeState4MoveFeast(areaIndexSet: IntArray, switchConfigs: IntArray) {
        // 在这个地方转换数据
        // 同步区域
        // 这里应该返回 extStr 的数据，然后转换成
        shapeViews.asSequence().onEach {
            it.updateUIState()
        }
    }

    private val moveFeastDataMap: HashMap<Int, MutableList<CubeMoveFeastData>>
        get() {
            return HashMap<Int, MutableList<CubeMoveFeastData>>().apply {
                shapeViews
                    .toList()
                    .apply { sortedBy { it.moveFeastData.type } }
                    .onEach {
                        it.moveFeastData.let { cubeMoveFeastData ->
                            if (containsKey(cubeMoveFeastData.type)) {
                                // 已经包含有的
                                this[cubeMoveFeastData.type]?.add(cubeMoveFeastData)
                            } else {
                                // key 不存在
                                val list = mutableListOf<CubeMoveFeastData>()
                                list.add(cubeMoveFeastData)
                                put(cubeMoveFeastData.type, list)
                            }
                        }
                    }
            }
        }

    fun getShapeData4MoveFeast(): ByteArray {
        // 需要在这里把
        val totalSize = shapeViews.size
        val byteLen = ceil(totalSize / 8f).toInt()
        // 区域位置数据长度 = 总块数 * 区域位置字节长度
        val areaByteLen = 1 + 1 + totalSize * byteLen

        val writeValue = ByteArray(areaByteLen)
        writeValue[0] = moveFeastDataMap.size.toByte() // 颜色区域个数
        writeValue[1] = byteLen.toByte() // 区域位置字节单位长度

        var startPos = 2
        moveFeastDataMap.values.onEach {
            it.onEach { cubeMoveFeastData ->
                val serialNumber = cubeMoveFeastData.serialNumber
                val areaBooleans = BooleanArray(totalSize)
                areaBooleans[serialNumber - 1] = true// 取其下标
                val makeSelectedBytes = Utils.makeSelectedBytes(areaBooleans, true)
                /*showLog(
                    TAG,
                    "onClickSureView: it.serialNumber = $serialNumber , makeSelectedBytes = ${
                        JsonUtil.toJson(
                            makeSelectedBytes
                        )
                    } , startPos = $startPos"
                )*/
                System.arraycopy(
                    makeSelectedBytes,
                    0,
                    writeValue,
                    startPos,
                    makeSelectedBytes.size
                )

                /*showLog(TAG, "onClickSureView: writeValue = ${Utils.toJson(writeValue)} ")
                showLog(
                    TAG,
                    "onClickSureView: area keys = ${JsonUtil.toJson(.toIntArray())} "
                )*/
                startPos += makeSelectedBytes.size
            }
        }
        return writeValue
    }

    fun moveFeastAreaOrderMap(): HashMap<Int, MutableList<CubeMoveFeastData>> {
        return moveFeastDataMap
    }

    override fun doSomethingMoveFeast(baseShapeView: BusinessShapeView) {
        listener?.getMoveFeastSelectedAreaMsg()?.let {
            baseShapeView.moveFeastData.set(it)
            baseShapeView.updateUIState()
            listener?.notifyFeastChange(moveFeastDataMap)
        }
    }
}

