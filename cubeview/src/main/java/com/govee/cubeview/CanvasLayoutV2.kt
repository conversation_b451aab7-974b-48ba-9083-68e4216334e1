package com.govee.cubeview

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import com.govee.cubeview.reconfigure.CanvasLayoutModeType
import com.govee.cubeview.reconfigure.canvas.OldCanvasLayoutBusiness
import com.govee.cubeview.reconfigure.proxy.CanvasLayoutProxyV1
import com.govee.cubeview.reconfigure.shape.ui.IGraffiti
import kotlin.math.min
import kotlin.math.sqrt


/**
 *     author  : sinrow
 *     time    : 2022/9/6
 *     version : 1.0.0
 *     desc    :
 *    业务逻辑实现类
 *    1、提供各种视图交互回调
 *    2、与外界交互的逻辑
 */

open class CanvasLayoutV2(context: Context, attrs: AttributeSet? = null) : OldCanvasLayoutBusiness(
    context, attrs
), IGraffiti {

    fun getShapeBusinessProxy(): CanvasLayoutProxyV1 {
        return canvasLayoutProxyV1
    }

    private var isZoom = false
    private var downX1 = 0F
    private var downX2 = 0F
    private var downY1 = 0F
    private var downY2 = 0F
    private val SCALE_MAX = 3F
    private val SCALE_MIN = 0.1F
    private var oldDist = 0f
    private var moveDist = 0f
    private var shapeColors = mutableListOf<Int>()
    private val lastColorList = mutableListOf<Int>()
    private var curColor = 0
    private var defColor = 0

    private val revokeList = mutableListOf<MutableList<Int>>() //回退
    private val restoreList = mutableListOf<MutableList<Int>>() //恢复
    private var keepTime = 10 //设置最大撤回次数

    private var colorModeSingleClickIndex = -1/*用于设置点击事件*/

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val isGraffiti = mModeType == CanvasLayoutModeType.Graffiti
        showLog(TAG, "onTouchEvent() action = ${event?.action}")

        if (isGraffiti) {
            event?.let {
                when (it.action and MotionEvent.ACTION_MASK) {
                    MotionEvent.ACTION_DOWN -> {
                        showLog(TAG, "onTouchEvent() ")
                        shapeColors.forEach {
                            lastColorList.add(it)
                        }
                        return true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val pointerCount = event.pointerCount
                        if (pointerCount == 2) {
                            val x1 = event.getX(0)
                            val x2 = event.getX(1)
                            val y1 = event.getY(0)
                            val y2 = event.getY(1)
                            val changeX1 = (x1 - downX1).toDouble()
                            val changeX2 = (x2 - downX2).toDouble()
                            val changeY1 = (y1 - downY1).toDouble()
                            val changeY2 = (y2 - downY2).toDouble()
                            if (scaleX > 1) { //滑动
                                val lessX = (changeX1 / 2 + changeX2 / 2).toFloat()
                                val lessY = (changeY1 / 2 + changeY2 / 2).toFloat()
                                setSelfPivot(-lessX, -lessY)
                                Log.d(TAG, "此时为滑动")
                            }
                            //缩放处理
                            moveDist = spacing(event)
                            val space = moveDist - oldDist
                            val scale = (scaleX + space / width)
                            if (scale < SCALE_MIN) {
                                setScale(SCALE_MIN)
                            } else if (scale > SCALE_MAX) {
                                setScale(SCALE_MAX)
                            } else {
                                setScale(scale)
                            }
                        } else if (pointerCount == 1) {
                            handleGraffitiMoveEvent(event)
                        }
                        return true
                    }

                    MotionEvent.ACTION_POINTER_DOWN -> {
                        showLog(TAG, "onTouchEvent() 涂鸦模式 双指按下")
                        isZoom = event.pointerCount >= 2
                        downX1 = event.getX(0)
                        downX2 = event.getX(1)
                        downY1 = event.getY(0)
                        downY2 = event.getY(1)
                        oldDist = moveDistance(event)
                        return false
                    }

                    MotionEvent.ACTION_POINTER_UP -> {
                        isZoom = false
                    }

                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        if (colorChange(lastColorList, shapeColors)) {
                            addRevokeList(lastColorList)
                            listener?.notifyGraffitiChangeListener()
                        }
                        lastColorList.clear()
                    }

                    else -> {}
                }
            }
        } else if (cubeType.config.checkSingleClickModeType(modeType = mModeType)) {
            event?.let {
                when (it.action and MotionEvent.ACTION_MASK) {
                    // 这里
                    MotionEvent.ACTION_DOWN -> {
                        colorModeSingleClickIndex = isClickShapeRange(it)
                        if (colorModeSingleClickIndex > -1) {
                            return true
                        }
                    }

                    MotionEvent.ACTION_UP -> {
                        handleSingleClick(colorModeSingleClickIndex, event)
                        return true
                    }

                    else -> {}
                }
            }
        }
        return super.onTouchEvent(event)
    }

    private fun colorChange(beforeColorList: MutableList<Int>, afterColorList: MutableList<Int>): Boolean {
        if (beforeColorList.size != afterColorList.size) return true
        for (i in beforeColorList.indices) {
            if (beforeColorList[i] != afterColorList[i]) {
                return true
            }
        }
        return false
    }

    /**
     * 移动
     *
     * @param lessX
     * @param lessY
     */
    private fun setSelfPivot(lessX: Float, lessY: Float) {
        var setPivotX: Float
        var setPivotY: Float
        setPivotX = pivotX + lessX
        setPivotY = pivotY + lessY
        if (setPivotX < 0 && setPivotY < 0) {
            setPivotX = 0f
            setPivotY = 0f
        } else if (setPivotX > 0 && setPivotY < 0) {
            setPivotY = 0f
            if (setPivotX > width) {
                setPivotX = width.toFloat()
            }
        } else if (setPivotX < 0 && setPivotY > 0) {
            setPivotX = 0f
            if (setPivotY > height) {
                setPivotY = height.toFloat()
            }
        } else {
            if (setPivotX > width) {
                setPivotX = width.toFloat()
            }
            if (setPivotY > height) {
                setPivotY = height.toFloat()
            }
        }
        setPivot(setPivotX, setPivotY)
    }


    /**
     * 平移画面，当画面的宽或高大于屏幕宽高时，调用此方法进行平移
     */
    private fun setPivot(x: Float, y: Float) {
        pivotX = x
        pivotY = y
    }

    /**
     * 计算两个点的距离
     *
     * @param event
     * @return
     */
    private fun spacing(event: MotionEvent): Float {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            sqrt((x * x + y * y))
        } else {
            0f
        }
    }

    /**
     * 设置放大缩小
     */
    private fun setScale(scale: Float) {
        val validScale = if (scale > SCALE_MAX) {
            SCALE_MAX
        } else if (scale < SCALE_MIN) {
            SCALE_MIN
        } else {
            scale
        }
        showLog(TAG, "setScale() scale = $scale , validScale = $validScale")

        scaleX = validScale
        scaleY = validScale
    }

    /**
     * 双指移动的距离
     */
    private fun moveDistance(event: MotionEvent): Float {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            sqrt((x * x + y * y))
        } else 0f
    }

    override fun setDefaultColor(color: Int) {
        val shapeViews = getShapeBusinessProxy().shapeViews
        shapeColors.clear()
        shapeViews.forEach { _ ->
            shapeColors.add(color)
        }
        this.curColor = color
        this.defColor = color
        setShapeViews(shapeColors.toMutableList())
    }

    override fun setGraffitiColor(color: Int) {
        curColor = color
    }

    override fun getGraffitiColors(): LinkedHashMap<Int, MutableList<Int>> {
        val map = LinkedHashMap<Int, MutableList<Int>>()
        for (i in shapeColors.indices) {
            if (shapeColors[i] == 0) continue
            var list = map[shapeColors[i]]
            if (list == null) {
                list = ArrayList()
            }
            if (!list.contains(i)) {
                list.add(i)
            }
            map[shapeColors[i]] = list
        }
        //showLog(TAG, "----------baseColorMap: $map")
        return map
    }

    override fun eraser() {
        curColor = defColor
        setDefaultColor(defColor)
    }

    override fun onRevoke() {
        // 撤销
        if (revokeList.isEmpty()) return
        addRestoreList(shapeColors)
        shapeColors.clear()
        revokeList4Last()?.let {
            shapeColors.addAll(it)
        }
        val index = revokeList.size - 1
        if (index >= 0) {
            revokeList.removeAt(index)
        }
        setShapeViews(colors = shapeColors.toMutableList())
    }

    override fun onRestore() {
        // 恢复
        showLog(TAG, "onRestore() ")
        if (restoreList.isEmpty()) return
        addRevokeList(shapeColors)
        shapeColors.clear()
        restoreList4Last()?.let {
            shapeColors.addAll(it)
        }
        val index = restoreList.size - 1
        if (index >= 0) {
            restoreList.removeAt(index)
        }
        setShapeViews(colors = shapeColors.toMutableList())
    }

    override fun setColors(color: MutableList<Int>) {
        showLog(TAG, "setColors() ")
        setShapeViews(color.toMutableList())
    }

    override fun resetGraffiti() {
        // 清空
        showLog(TAG, "clear() ")
        revokeList.clear()
        restoreList.clear()
        setShapeViews(colors = mutableListOf())
    }

    fun canRevoke() = revokeList.size > 0

    fun canRestore() = restoreList.size > 0
    fun canClear(): Boolean {
        shapeColors.forEach {
            if (it != 0) return true
        }
        return false
    }

    private fun restoreList4Last(): MutableList<Int>? {
        if (restoreList.isEmpty()) return null
        val pos = restoreList.size - 1
        return restoreList[pos]
    }

    private fun addRevokeList(list: MutableList<Int>) {
        if (revokeList.size >= keepTime) {
            revokeList.removeAt(0)
        }
        val resultList = mutableListOf<Int>()
        list.forEach {
            resultList.add(it)
        }
        revokeList.add(resultList)
    }

    private fun revokeList4Last(): MutableList<Int>? {
        if (revokeList.isEmpty()) return null
        val pos = revokeList.size - 1
        return revokeList[pos]
    }


    private fun addRestoreList(list: MutableList<Int>) {
        if (restoreList.size >= keepTime) {
            restoreList.removeAt(0)
        }
        val resultList = mutableListOf<Int>()
        list.forEach {
            resultList.add(it)
        }
        restoreList.add(resultList)
    }


    private fun setShapeViews(colors: MutableList<Int>) {
        val shapeViews = getShapeBusinessProxy().shapeViews
        val minSize = min(shapeViews.size, colors.size)
        showLog(TAG, "setShapeViews() shapeViews =${shapeViews.size} minSize = $minSize , colors = ${colors.size}")

        this.shapeColors.clear()
        shapeViews.forEachIndexed { index, baseShapeView ->
            val realColor = if (index <= minSize) {
                colors[index]
            } else {
                defColor
            }
            shapeColors.add(realColor)
            baseShapeView.setShapeColor(realColor)
            baseShapeView.updateUIState()
        }
    }

    private fun handleGraffitiMoveEvent(event: MotionEvent) {
        showLog(TAG, "handleGraffitiMoveEvent() 涂鸦模式 ${event.x} ${event.y} , ${event.pointerCount}")
        val moveX = event.x
        val moveY = event.y

        val shapeViews = getShapeBusinessProxy().shapeViews
        for (i in 0 until shapeViews.size) {
            val baseShapeView = shapeViews[i]
            val shapeArea4Parent = baseShapeView.shapeArea4Parent()
            val leftX = shapeArea4Parent[0]
            val rightX = shapeArea4Parent[1]
            val leftY = shapeArea4Parent[2]
            val rightY = shapeArea4Parent[3]
            if (moveX in leftX..rightX && moveY in leftY..rightY) {
                showLog(TAG, "handleGraffitiMoveEvent() 命中 $i")
                val shapeColor = baseShapeView.params.shapeColor
                if (shapeColor != curColor) {
                    shapeColors[i] = curColor
                    baseShapeView.setShapeColor(curColor)
                    baseShapeView.updateUIState()
                }
                break
            }
        }
    }

    private fun handleSingleClick(position: Int, event: MotionEvent) {
        if (position < 0) return
        showLog(TAG, "handleSingleClick() 点击事件 ${event.x} ${event.y} , ${event.pointerCount}")
        val moveX = event.x
        val moveY = event.y

        val shapeViews = getShapeBusinessProxy().shapeViews
        val baseShapeView = shapeViews[position]
        val shapeArea4Parent = baseShapeView.shapeArea4Parent()
        val leftX = shapeArea4Parent[0]
        val rightX = shapeArea4Parent[1]
        val leftY = shapeArea4Parent[2]
        val rightY = shapeArea4Parent[3]
        if (moveX in leftX..rightX && moveY in leftY..rightY) {
            showLog(TAG, "handleGraffitiMoveEvent() 命中 $position")
            baseShapeView.singleClickListener?.invoke()
            return
        }
    }

    private fun isClickShapeRange(event: MotionEvent): Int {
        val moveX = event.x
        val moveY = event.y

        val shapeViews = getShapeBusinessProxy().shapeViews
        for (i in 0 until shapeViews.size) {
            val baseShapeView = shapeViews[i]
            val shapeArea4Parent = baseShapeView.shapeArea4Parent()
            val leftX = shapeArea4Parent[0]
            val rightX = shapeArea4Parent[1]
            val leftY = shapeArea4Parent[2]
            val rightY = shapeArea4Parent[3]
            if (moveX in leftX..rightX && moveY in leftY..rightY) {
                return i
            }
        }
        return -1
    }
}