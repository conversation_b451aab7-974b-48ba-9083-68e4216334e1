package com.govee.cubeview

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.view.View
import com.govee.cubeview.reconfigure.canvas.BaseCanvasLayout

/**
 *     author  : sinrow
 *     time    : 2022/4/18
 *     version : 1.0.0
 *     desc    : 用于图形翻转动画
 */
@SuppressLint("ObjectAnimatorBinding")
object FlipAnimationUtil {
    fun startAnimation(canvasLayout: CanvasLayout, block: () -> Unit) {
        canvasLayout.shapeViews.first().pos?.let {
            // 设置沿着哪个点翻转，以及 cameraDistance 属性
            canvasLayout.pivotX = it.x
            val diff: Float = canvasLayout.resources.displayMetrics.density * 16000
            canvasLayout.cameraDistance = diff
        }

        startFlipAnimation(canvasLayout, block)
    }

    fun startAnimation(baseCanvasLayout: BaseCanvasLayout, init: () -> Unit, result: () -> Unit) {
        init()
        startFlipAnimation(baseCanvasLayout, result)
    }

    private fun startFlipAnimation(view: View, block: () -> Unit) {
        val inSet = AnimatorSet().apply {
            duration = 300
            playTogether(ObjectAnimator.ofFloat(null, "rotationY", 0f, 90f))
        }
        val outSet =
            AnimatorSet().apply {
                duration = 300
                playTogether(ObjectAnimator.ofFloat(null, "rotationY", 270f, 360f))
            }
        inSet.setTarget(view)
        inSet.start()
        inSet.removeAllListeners()
        inSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                block()
                outSet.setTarget(view)
                outSet.start()
            }
        })
    }


    private val inSet by lazy {
        AnimatorSet().apply {
            duration = 300
            playTogether(ObjectAnimator.ofFloat(null, "rotationY", 0f, 90f))
        }
    }
    private val outSet by lazy {
        AnimatorSet().apply {
            duration = 300
            playTogether(ObjectAnimator.ofFloat(null, "rotationY", 270f, 360f))
        }
    }
}