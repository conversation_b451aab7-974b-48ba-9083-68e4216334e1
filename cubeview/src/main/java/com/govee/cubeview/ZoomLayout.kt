package com.govee.cubeview

import android.content.Context
import android.graphics.Point
import android.graphics.PointF
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.OnScaleGestureListener
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import com.govee.cubeview.CanvasLayout.OnChangeLayoutParams
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sign

/**
 * Create by lvwenrui on 2021/7/1 19:52
 * 可以拖拽和拉伸的布局，内嵌一个View
 */
class ZoomLayout : FrameLayout, OnScaleGestureListener, GestureDetector.OnDoubleTapListener {
    private enum class Mode {
        NONE, DRAG, ZOOM
    }

    private var mode = Mode.NONE
    var mScale = 1.0f
    private var lastScaleFactor = 0f

    // Where the finger first  touches the screen
    private var startX = 0f
    private var startY = 0f

    // How much to translate the canvas
    private var dx = 0f
    private var dy = 0f
    private var prevDx = 0f
    private var prevDy = 0f
    private var canvasLayoutSize = 3200

    private var scaleDetector: ScaleGestureDetector? = null
    private var doubleDetector: GestureDetector? = null
    var doubleClickListener: DoubleClick? = null
    var moveOrScaleListener: MoveOrScaleListener? = null
    private var supportIntercept4MoveAction: Boolean = false/*是否支持真的move的拦截事件处理-默认不支持*/

    private var isSupportScale = true /*是否支持双指缩放 */
    var supportMode = true/*是否支持移动*/

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(
        context,
        attrs,
        defStyle
    ) {
        init(context)
    }

    fun init(context: Context) {
        setWillNotDraw(false)
        isVerticalScrollBarEnabled = true
        isHorizontalScrollBarEnabled = true
        scaleDetector = ScaleGestureDetector(context, this)
        doubleDetector = GestureDetector(context, GestureDetector.SimpleOnGestureListener())
        doubleDetector?.setOnDoubleTapListener(this)
    }

    /**
     * 新增绑定 CanvasLayout 方法
     */
    fun bindCanvasLayout(canvasLayout: CanvasLayout?) {
        canvasLayout?.apply {
            canvasLayoutSize = this.width
            changeLayoutParams = object : OnChangeLayoutParams {
                override fun layoutParams(with: Int, height: Int) {
                    canvasLayoutSize = with
                }
            }
        }
    }

    fun bindCanvasLayout(canvasLayoutV2: CanvasLayoutV2?) {
        canvasLayoutV2?.apply {
            canvasLayoutSize = this.width
            changeLayoutParams = object : OnChangeLayoutParams {
                override fun layoutParams(with: Int, height: Int) {
                    canvasLayoutSize = with
                }
            }
        }
    }

    private var isCustomRangeMoveZoom = false
    private var rangeMoveZoom: PointF? = null
    private var basePointF: PointF? = null

    /**
     * 在中心点位置的偏移量
     */
    fun setRangeMoveZoom(range: PointF, boolean: Boolean) {
        isCustomRangeMoveZoom = boolean
        rangeMoveZoom = range
    }

    private fun getCanvasLayoutSize(): Int {
        return canvasLayoutSize
    }

    override fun onTouchEvent(motionEvent: MotionEvent): Boolean {
        if (!supportMode) return false
        when (motionEvent.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                Log.i(TAG, "DOWN")
                if (mScale >= MIN_ZOOM) {
                    mode = Mode.DRAG
                    startX = motionEvent.x - prevDx
                    startY = motionEvent.y - prevDy
                }
            }

            MotionEvent.ACTION_MOVE -> if (mode == Mode.DRAG) {
                dx = motionEvent.x - startX
                dy = motionEvent.y - startY
                log(TAG, "motionEvent x = ${motionEvent.x} , y = ${motionEvent.y}")
            }

            MotionEvent.ACTION_POINTER_DOWN -> mode = Mode.ZOOM
            MotionEvent.ACTION_POINTER_UP ->                 /*缩放操作后不给连续滑动*/mode = Mode.NONE
            MotionEvent.ACTION_UP -> {
                Log.i(TAG, "UP")
                mode = Mode.NONE
                prevDx = dx
                prevDy = dy
            }
        }
        if (isSupportScale) {
            scaleDetector!!.onTouchEvent(motionEvent)
        }
        doubleDetector?.onTouchEvent(motionEvent)
        if (mode == Mode.DRAG && mScale >= MIN_ZOOM || mode == Mode.ZOOM) {
            parent.requestDisallowInterceptTouchEvent(true)
            val maxDx = (child().width - (width - paddingLeft - paddingRight) / mScale) / 2 * mScale
            val maxDy =
                (child().height - (height - paddingTop - paddingBottom) / mScale) / 2 * mScale
            dx = dx.coerceAtLeast(-maxDx).coerceAtMost(maxDx)
            dy = dy.coerceAtLeast(-maxDy).coerceAtMost(maxDy)
            Log.i(
                TAG,
                "Width: " + child().width + ", scale " + mScale + ", dx " + dx + ", max " + maxDx
            )
            Log.i(
                TAG,
                "Height: " + child().height + ", scale " + mScale + ", dy " + dy + ", max " + maxDy
            )
            checkScaleRange()
            checkMoveRange()
            applyScaleAndTranslation(false)
        }
        awakenScrollBars()
        return true
    }

    private var moveRangeDiff = 0

    fun setMoveRangeDiff(diff: Int) {
        moveRangeDiff = diff
    }

    private fun checkMoveRange() {
        if (isCustomRangeMoveZoom) {
            rangeMoveZoom?.let {
                val maxDx = ((width - paddingLeft - paddingRight) / mScale - it.x) / 2 * mScale
                val maxDy =
                    ((height - paddingTop - paddingBottom) / mScale - it.y) / 2 * mScale
                basePointF?.apply {
                    dx = dx.coerceAtLeast(-maxDx + x * mScale + moveRangeDiff)
                        .coerceAtMost(maxDx + x * mScale - moveRangeDiff)
                    dy = dy.coerceAtLeast(-maxDy + y * mScale + moveRangeDiff)
                        /* 为了显示图形尺寸框，整体高度已增加大约 60dp，故需要减去该高度 */
                        .coerceAtMost(maxDy + y * mScale - Utils.dp2px(context, 60f))
                }
            }
        }
    }

    private var downX: Int = -1
    private var downY: Int = -1
    private var inMoveStep: Boolean = false

    private fun resetParams4Intercept4MoveAction() {
        downX = -1
        downY = -1
        inMoveStep = false
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            log(TAG, "onInterceptTouchEvent: ${ev.action} inMoveStep = $inMoveStep")
            if (supportIntercept4MoveAction) {
                if (inMoveStep) {
                    return true
                }
            }
        }
        return super.onInterceptTouchEvent(ev)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            log(TAG, "dispatchTouchEvent: ${ev.action}")
            when (ev.action) {
                MotionEvent.ACTION_DOWN -> {
                    downX = ev.x.toInt()
                    downY = ev.y.toInt()
                    Log.i(TAG, "DOWN--》 downX = $downX ; downY = $downY")
                }

                MotionEvent.ACTION_MOVE -> {
                    if (supportIntercept4MoveAction) {
                        val dx = ev.x - downX
                        val dy = ev.y - downY
                        val inMove =
                            abs(dx) > DISTANCE_4_INTERCEPT_MOVE_ACTION || abs(dy) > DISTANCE_4_INTERCEPT_MOVE_ACTION
                        Log.i(
                            TAG,
                            "MOVE--》 dx = $dx ; dy = $dy inMove = $inMove ; inMoveStep = $inMoveStep"
                        )
                        if (inMove && !inMoveStep) {
                            inMoveStep = true
                            if (mScale >= MIN_ZOOM && mode == Mode.NONE) {
                                mode = Mode.DRAG
                                startX = ev.x - prevDx
                                startY = ev.y - prevDy
                            }
                        }
                    }
                    return super.dispatchTouchEvent(ev)
                }

                MotionEvent.ACTION_UP -> {
                    resetParams4Intercept4MoveAction()
                }

                MotionEvent.ACTION_CANCEL -> {
                    resetParams4Intercept4MoveAction()
                }

                else -> {
                    return super.dispatchTouchEvent(ev)
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 是否支持真的move事件进行拦截
     */
    fun supportIntercept4MoveAction(intercept: Boolean) {
        this.supportIntercept4MoveAction = intercept
    }

    fun supportScale(support: Boolean) {
        this.isSupportScale = support
    }

    override fun computeHorizontalScrollExtent(): Int {
        return (width / mScale).toInt()
    }

    override fun computeHorizontalScrollOffset(): Int {
        return ((getCanvasLayoutSize() / 2 - (dx + width / 2) / mScale)).toInt()
    }

    override fun computeHorizontalScrollRange(): Int {
        return getCanvasLayoutSize()
    }

    override fun computeVerticalScrollExtent(): Int {
        return (height / mScale).toInt()
    }

    override fun computeVerticalScrollOffset(): Int {
        return ((getCanvasLayoutSize() / 2 - (dy + height / 2) / mScale)).toInt()
    }

    override fun computeVerticalScrollRange(): Int {
        return getCanvasLayoutSize()
    }

    // ScaleGestureDetector
    override fun onScaleBegin(scaleDetector: ScaleGestureDetector): Boolean {
        Log.i(TAG, "onScaleBegin")
        return true
    }

    override fun onScale(scaleDetector: ScaleGestureDetector): Boolean {
        val scaleFactor = scaleDetector.scaleFactor
        Log.i(TAG, "onScale$scaleFactor")
        if (lastScaleFactor == 0f || sign(scaleFactor) == sign(lastScaleFactor)) {
            mScale *= scaleFactor
            val min = MIN_ZOOM.coerceAtLeast(width * 1f / child().width)
            mScale = min.coerceAtLeast(mScale.coerceAtMost(MAX_ZOOM))
            lastScaleFactor = scaleFactor
        } else {
            lastScaleFactor = 0f
        }
        return true
    }

    override fun onScaleEnd(scaleDetector: ScaleGestureDetector) {
        Log.i(TAG, "onScaleEnd")
    }

    private fun checkScaleRange() {
        mScale = min(max(MIN_ZOOM, mScale), MAX_ZOOM)
    }

    private fun applyScaleAndTranslation(anima: Boolean) {
        if (anima) {
            child().scaleX = mScale
            child().scaleY = mScale
            ViewCompat.animate(child()).translationX(dx).translationY(dy).start()
        } else {
            child().scaleX = mScale
            child().scaleY = mScale
            child().translationX = dx
            child().translationY = dy
        }
        moveOrScaleListener?.onMoveOrScale(mScale)
    }

    fun setScaleAndTranslationByAnimation(scale: Float, point: PointF, duration: Long) {
        if (scale <= 0f) {
            setTransitionXY(point)
            return
        }
        mode = Mode.NONE
        mScale = scale
        checkScaleRange()
        lastScaleFactor = mScale
        startY = 0f
        startY = 0f
        dx = point.x * mScale
        dy = (point.y) * mScale
        checkMoveRange()
        prevDx = dx
        prevDy = dy
        child().scaleX = mScale
        child().scaleY = mScale
        ViewCompat.animate(child()).translationX(dx).translationY(dy).setDuration(duration).start()
        moveOrScaleListener?.onMoveOrScale(mScale)
    }

    private fun child(): View {
        return getChildAt(0)
    }

    fun setScale(scale: Float) {
        mScale = scale
        checkScaleRange()
        applyScaleAndTranslation(false)
    }

    fun getCenterPoint(): Point {
        return Point((dx / mScale).toInt(), (dy / mScale).toInt())
    }

    /*x，y = 形状的偏移中点坐标*/
    fun setScaleAndTransitionXY(scale: Float, point: PointF) {
        if (scale <= 0f) {
            setTransitionXY(point)
            return
        }
        basePointF = point
        mode = Mode.NONE
        mScale = scale
        checkScaleRange()
        lastScaleFactor = mScale
        startY = 0f
        startY = 0f
        dx = point.x * mScale
        dy = point.y * mScale
        checkMoveRange()
        prevDx = dx
        prevDy = dy
        applyScaleAndTranslation(false)
        Log.i(
            TAG,
            "scale=" + mScale + " dx=" + dx + " dy=" + dy + " pointX=" + point.x + " pointY=" + point.y
        )
    }

    fun setTransitionXY(point: PointF) {
        setTransitionXY(point, true)
    }

    fun setTransitionXY(point: PointF, animate: Boolean) {
        basePointF = point
        mode = Mode.NONE
        checkScaleRange()
        lastScaleFactor = mScale
        startY = 0f
        startY = 0f
        dx = point.x * mScale
        dy = point.y * mScale
        checkMoveRange()
        prevDx = dx
        prevDy = dy
        applyScaleAndTranslation(animate)
    }

    private var shapeSizeRange: PointF? = null

    fun setCanvasLayoutMovableRange(shapeSizeRange: PointF) {
        this.shapeSizeRange = shapeSizeRange

    }

    companion object {
        private const val TAG = "ZoomLayout"
        private const val MIN_ZOOM = 0.001f
        private const val MAX_ZOOM = 3.5f
        private const val DISTANCE_4_INTERCEPT_MOVE_ACTION = 50
    }

    interface DoubleClick {
        fun onDoubleClick(v: View)
    }

    interface MoveOrScaleListener {
        fun onMoveOrScale(scale: Float)
    }

    /*双击事件*/
    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        Log.i(TAG, "onSingleTapConfirmed")
        return true
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        Log.i(TAG, "onDoubleTap")
        doubleClickListener?.onDoubleClick(this)
        return true
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        Log.i(TAG, "onDoubleTapEvent")
        return true
    }


}