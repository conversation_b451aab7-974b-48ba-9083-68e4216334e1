apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'


android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':ui')

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"
    api "org.jetbrains.kotlin:kotlin-reflect:1.8.20"
    api "org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.0"
    api 'com.google.code.gson:gson:2.10.1'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'

    // LiveData
    api "androidx.lifecycle:lifecycle-livedata-ktx:2.6.2"
    // For Kotlin use lifecycle-viewModel-ktx 为kotlin引入
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
}