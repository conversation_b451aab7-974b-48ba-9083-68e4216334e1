package com.govee.cubeview

import android.util.Log

/**
 *     author  : sinrow
 *     time    : 2021/12/17
 *     version : 1.0.0
 *     desc    :
 */

inline fun Boolean.yes(block: () -> Unit): <PERSON><PERSON><PERSON> {
    return this.apply { if (this) block() }
}

inline fun Boolean.no(block: () -> Unit): <PERSON><PERSON><PERSON> {
    return this.apply { if (!this) block() }
}

fun log(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.d(tag, content)
    }
}

fun logw(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.w(tag, content)
    }
}

fun loge(tag: String, content: String) {
    BuildConfig.DEBUG.yes {
        Log.e(tag, content)
    }
}