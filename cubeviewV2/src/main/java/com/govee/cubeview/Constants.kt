package com.govee.cubeview

import android.graphics.Color
import com.govee.cubeview.shape.Shape
import kotlin.math.ceil

/**
 *     author  : sinrow
 *     time    : 2021/12/13
 *     version : 1.0.0
 *     desc    :
 */
internal object Constants {

    /* 多边形图形支持最大数量 */
    private const val max_count_default = 23
    private const val max_count_hexagon = 20
    private const val max_count_triangle = 20
    private const val max_count_y = 20
    private const val max_count_solid_hexagon = 25

    /* 正方形画布边长 */
    private const val canvas_size_default = 3200
    private const val canvas_size_hexagon = 3200
    private const val canvas_size_triangle = 3200
    private const val canvas_size_y = 3200
    private const val canvas_size_solid_hexagon = 3200

    /* 多边形初始旋转角度。根据嵌入式规则而定 */
    private const val shape_angle_default = 60
    private const val shape_angle_hexagon = 60
    private const val shape_angle_triangle = 0
    private const val shape_angle_solid_hexagon = 0
    private const val shape_angle_y = 180

    /* 多边形是否支持多电源，边界是多少 */
    private const val shape_power_default = 0
    private const val shape_power_hexagon = 12
    private const val shape_power_triangle = 12
    private const val shape_power_y = 30
    private const val shape_power_solid_hexagon = 25

    /*多边形实际物理尺寸大小(边):mm */
    private const val shape_physical_size_hexagon = 90f
    private const val shape_physical_size_triangle = 198f
    private const val shape_physical_size_solid_hexagon = 90f
    private const val shape_physical_size_y = 220f
    private const val shape_physical_size_default = shape_physical_size_hexagon


    fun getShapeSupportMaxCount(shapeType: Int = Shape.TYPE_DEFAULT): Int {
        return when (shapeType) {
            Shape.TYPE_TRIANGLE -> max_count_triangle
            Shape.TYPE_HEXAGON -> max_count_hexagon
            Shape.TYPE_SOLID_HEXAGON -> max_count_solid_hexagon
            Shape.TYPE_Y -> max_count_y
            else -> max_count_default
        }
    }

    fun getShapeDefaultAngle(shapeType: Int = Shape.TYPE_DEFAULT): Int {
        return when (shapeType) {
            Shape.TYPE_TRIANGLE -> shape_angle_triangle
            Shape.TYPE_HEXAGON -> shape_angle_hexagon
            Shape.TYPE_SOLID_HEXAGON -> shape_angle_solid_hexagon
            Shape.TYPE_Y -> shape_angle_y
            else -> shape_angle_default
        }
    }

    fun getCanvasLayoutSize(shapeType: Int = Shape.TYPE_DEFAULT): Int {
        return when (shapeType) {
            Shape.TYPE_Y -> {
                canvas_size_y
            }
            Shape.TYPE_TRIANGLE -> {
                canvas_size_triangle
            }
            Shape.TYPE_HEXAGON -> {
                canvas_size_hexagon
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                canvas_size_solid_hexagon
            }
            else -> {
                canvas_size_default
            }
        }
    }

    fun getShapePowerBoundary(shapeType: Int = Shape.TYPE_DEFAULT): Int {
        return when (shapeType) {
            Shape.TYPE_Y -> {
                shape_power_y
            }
            Shape.TYPE_TRIANGLE -> {
                shape_power_triangle
            }
            Shape.TYPE_HEXAGON -> {
                shape_power_hexagon
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                shape_power_solid_hexagon
            }
            else -> {
                shape_power_default
            }
        }
    }

    fun isMultiPower(shapeSize: Int, shapeType: Int): Boolean {
        val shapePowerBoundary = getShapePowerBoundary(shapeType)
        return shapeSize >= shapePowerBoundary && shapePowerBoundary != 0
    }

    /**
     *
     */
    fun fetchMultiPowerLimitIndex(shapeSize: Int): Int {
        return ceil((shapeSize / 2f)).toInt()
    }

    /**
     * 根据Tag 获取下一个图形的支持的旋转偏移量
     * Pair<偏移旋转角度（没有=0），对应的tag（默认电源位置）>
     */
    fun getNextShapeSupportOffset(
        outputDirectionTag: Int,//0 .. n
        shapeType: Int = Shape.TYPE_DEFAULT
    ): Pair<Pair<Float, Int>, Pair<Float, Int>?> {
        val defaultTag = getShapeInputTag(outputDirectionTag)
        return when (shapeType) {
            Shape.TYPE_Y -> {
                Pair(Pair(0f, defaultTag), null)
            }
            Shape.TYPE_TRIANGLE -> {
                Pair(Pair(0f, defaultTag), null)
            }
            Shape.TYPE_HEXAGON -> {
                Pair(Pair(0f, defaultTag), null)
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                if ((outputDirectionTag + 1) % 2 == 0) {
                    Pair(Pair(0f, defaultTag), null)
                } else {
                    Pair(Pair(60f, 3), Pair(300f, 5))
                }
            }
            else -> {
                Pair(Pair(0f, defaultTag), null)
            }
        }
    }

    /**
     * 输入电源位置的tag标号
     */
    fun getShapeInputTag(shapeType: Int = Shape.TYPE_DEFAULT): Int {
        return when (shapeType) {
            Shape.TYPE_TRIANGLE -> {
                3
            }
            Shape.TYPE_HEXAGON -> {
                4
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                4
            }
            else -> {
                4
            }
        }
    }

    /**
     * 通过角度计算出对应的输出边的Tag
     * @param angle 垂直向上开始，顺时针旋转的角度，0-360
     * @return DirectionTag（六边形从左上角的边开始0-5，顺时针计算）
     */
    fun getDirectionTagByAngle(angle: Int, shapeType: Int = Shape.TYPE_DEFAULT): Int {
        var result = when (shapeType) {
            Shape.TYPE_TRIANGLE -> {
                0
            }
            Shape.TYPE_HEXAGON -> {
                (angle + 60) / 60
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                (angle + 60) / 60
            }
            else -> {
                0
            }
        }
        while (result < 0) result += 6
        while (result >= 6) result -= 6
        return result
    }

    fun isShowInstallPathNumber(shapeType: Int = Shape.TYPE_DEFAULT): Boolean {
        return when (shapeType) {
            Shape.TYPE_TRIANGLE -> {
                true
            }
            Shape.TYPE_HEXAGON -> {
                true
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                false
            }
            else -> {
                true
            }
        }
    }

    /*获取实际拼接的序号，根据DirectionTag*/
    @JvmStatic
    fun getInstallNumberWithDirectionTag(shapeType: Int = Shape.TYPE_DEFAULT, tag: Int): Int {
        return when (shapeType) {
            Shape.TYPE_TRIANGLE -> {
                tag
            }
            Shape.TYPE_HEXAGON -> {
                when (tag) {
                    0 -> 4
                    1 -> 3
                    2 -> 2
                    3 -> 1
                    4 -> -1
                    5 -> 5
                    else -> -1
                }
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                tag
            }
            else -> {
                tag
            }
        }
    }

    private var randomIndex = -1

    fun fetchRoundColor4PreViewBg(): IntArray? {
        val random = colorsMap.keys.filter { it != randomIndex }.random()
        log("fetchRoundColor4PreViewBg", "random = $random")
        randomIndex = random
        return colorsMap.get(random)
    }

    fun fetchRoundColor4PreViewEdge(): Int {
        val random = colorsMap.keys.filter { it != randomIndex }.random()
        val ints = colorsMap[random] ?: return Color.WHITE
        return ints.random()
    }

    private val colorsMap by lazy {
        HashMap<Int, IntArray>().apply {
            put(1, IntArray(3).apply {
                this[0] = Color.parseColor("#FFCD4D")
                this[1] = Color.parseColor("#FF749E")
                this[2] = Color.parseColor("#00E5E7")
            })
            put(2, IntArray(3).apply {
                this[0] = Color.parseColor("#FFD74D")
                this[1] = Color.parseColor("#74D5FF")
                this[2] = Color.parseColor("#FF356B")
            })
            put(3, IntArray(3).apply {
                this[0] = Color.parseColor("#FFD74D")
                this[1] = Color.parseColor("#74FFF4")
                this[2] = Color.parseColor("#FF9A35")
            })
            put(4, IntArray(3).apply {
                this[0] = Color.parseColor("#FFD74D")
                this[1] = Color.parseColor("#77F259")
                this[2] = Color.parseColor("#FF356B")
            })
            put(5, IntArray(3).apply {
                this[0] = Color.parseColor("#FFD74D")
                this[1] = Color.parseColor("#77F259")
                this[2] = Color.parseColor("#FF9A35")
            })
            put(6, IntArray(3).apply {
                this[0] = Color.parseColor("#F7EE73")
                this[1] = Color.parseColor("#72F7EE")
                this[2] = Color.parseColor("#EE73F7")
            })
            put(7, IntArray(3).apply {
                this[0] = Color.parseColor("#F7EE73")
                this[1] = Color.parseColor("#FF356B")
                this[2] = Color.parseColor("#737CF7")
            })
            put(8, IntArray(3).apply {
                this[0] = Color.parseColor("#F7EE73")
                this[1] = Color.parseColor("#F7737C")
                this[2] = Color.parseColor("#2AC9FF")
            })
            put(9, IntArray(3).apply {
                this[0] = Color.parseColor("#CD2BFF")
                this[1] = Color.parseColor("#FFB3FC")
                this[2] = Color.parseColor("#FA62FE")
            })
            put(10, IntArray(3).apply {
                this[0] = Color.parseColor("#16AAF0")
                this[1] = Color.parseColor("#ADEAFF")
                this[2] = Color.parseColor("#2DD9FF")
            })
            put(11, IntArray(3).apply {
                this[0] = Color.parseColor("#477AFF")
                this[1] = Color.parseColor("#85F8FF")
                this[2] = Color.parseColor("#54A9FF")
            })
            put(12, IntArray(3).apply {
                this[0] = Color.parseColor("#FF356B")
                this[1] = Color.parseColor("#FFBDD0")
                this[2] = Color.parseColor("#FF62A7")
            })
            put(13, IntArray(3).apply {
                this[0] = Color.parseColor("#08D901")
                this[1] = Color.parseColor("#BCFFAD")
                this[2] = Color.parseColor("#2DFF2E")
            })
            put(14, IntArray(3).apply {
                this[0] = Color.parseColor("#FF9A35")
                this[1] = Color.parseColor("#F7EE73")
                this[2] = Color.parseColor("#FFD74D")
            })
        }
    }

    /***
     *<p>
     * 获取多边形实际的物理尺寸（边）
     * 单位：mm（毫米）
     *</p>
     */
    fun fetchShapePhysicalSize(shapeType: Int = Shape.TYPE_DEFAULT): Float {
        return when (shapeType) {
            Shape.TYPE_HEXAGON -> {
                shape_physical_size_hexagon
            }
            Shape.TYPE_TRIANGLE -> {
                shape_physical_size_triangle
            }
            Shape.TYPE_SOLID_HEXAGON -> {
                shape_physical_size_solid_hexagon
            }
            Shape.TYPE_Y -> {
                shape_physical_size_y
            }
            else -> {
                shape_physical_size_default
            }
        }
    }
}
