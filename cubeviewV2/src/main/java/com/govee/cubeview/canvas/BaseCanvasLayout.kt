package com.govee.cubeview.canvas

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.widget.FrameLayout
import com.govee.cubeview.Constants
import com.govee.cubeview.shape.Shape
import com.govee.cubeview.shape.ShapePosition
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/9/14
 *     version : 1.0.0
 *     desc    :
 */
abstract class BaseCanvasLayout : FrameLayout, ICanvasLayout {

    private var cubeShapeViewType = Shape.TYPE_DEFAULT

    companion object {
        val TAG: String = this::class.java.name

        const val CANVAS_PADDING = 40//边界检测的padding
        const val SCALE_DEFAULT = 1.5f//初始化时拉伸的比例

        /*创建默认Shape*/
        @JvmStatic
        fun generaDefaultShape(type: Int): ShapePosition {
            val canvasLayoutSize = Constants.getCanvasLayoutSize(type)
            val shapeDefaultAngle = Constants.getShapeDefaultAngle(type)
            return ShapePosition(
                type,
                (canvasLayoutSize / 2).toFloat(),
                (canvasLayoutSize / 2).toFloat(),
                shapeDefaultAngle, 0, 0
            )
        }

        @JvmStatic
        fun getShapeMaxCount(shapeType: Int): Int {
            return Constants.getShapeSupportMaxCount(shapeType)
        }

        @JvmStatic
        fun canvasLayoutSize(shapeType: Int): Int {
            return Constants.getCanvasLayoutSize(shapeType)
        }

        @JvmStatic
        fun getShapeMultiPowerBoundary(shapeType: Int): Int {
            return Constants.getShapePowerBoundary(shapeType)
        }

        @JvmStatic
        fun getInstallNumberWithDirectionTag(shapeType: Int, directionTag: Int): Int {
            return Constants.getInstallNumberWithDirectionTag(shapeType, directionTag)
        }
    }

    constructor(context: Context?) : super(context!!) {
        initAttrs(context, null)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {
        initAttrs(context, attrs)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context!!,
        attrs,
        defStyleAttr
    ) {
        initAttrs(context, attrs)
    }

    private fun initAttrs(context: Context?, attrs: AttributeSet?) {
        val obtainStyledAttributes =
            context?.obtainStyledAttributes(attrs, R.styleable.CanvasLayout)
        obtainStyledAttributes?.let {
            cubeShapeViewType =
                it.getInt(R.styleable.CanvasLayout_cube_shape_view_type, Shape.TYPE_DEFAULT)
            restCanvasLayoutParams(cubeShapeViewType)
        }
        obtainStyledAttributes?.recycle()
    }

    private fun restCanvasLayoutParams(cubeShapeViewType: Int) {

    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        // 描述过程

    }


}