package com.govee.cubeview.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.widget.ImageView
import androidx.core.view.ViewCompat
import com.govee.ui.R
import com.govee.cubeview.Utils
import com.govee.cubeview.log
import com.govee.cubeview.shape.AbsCubeShapeView
import kotlin.math.abs

/**
 * Create by lvwenrui on 2021/7/5 11:49
 * 六边形安装辅助指引view
 */
open class HexagonInstallView : androidx.appcompat.widget.AppCompatImageView {

    companion object {
        const val Destiny = 375
        const val ImgSizeDp = 115
    }

    var shapeData: AbsCubeShapeView? = null
    private var nextShapeData: AbsCubeShapeView? = null

    var imgSizePx: Int = 0

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context,
            attrs,
            defStyleAttr) {
        init()
    }

    private fun init() {
        imgSizePx = Utils.getPixelByDp(context, ImgSizeDp, Destiny).toInt()
        maxWidth = imgSizePx
        maxHeight = imgSizePx

    }

    val mPaint: Paint
        get() {
            return Paint(Paint.ANTI_ALIAS_FLAG)
        }

    private val resImgIDs =
        listOf(R.mipmap.new_6066_anzhuang_yanshi_fangkuai_back,
                R.mipmap.new_6066_anzhuang_yanshi_fangkuai_back_01,
                R.mipmap.new_6066_anzhuang_yanshi_fangkuai_back_02,
                R.mipmap.new_6066_anzhuang_yanshi_fangkuai_front)

    private var inImgBitmap: Bitmap? = null
    private var outImgBitmap: Bitmap? = null
    private var resImgBitmapList: List<Bitmap?> = mutableListOf()
    var rotationAnimationState = 0
        /*0, 1, 2, 3*/
        set(value) {
            field = value
            invalidate()
        }

    var viewRotation = 0

    /*设置数据 angle = 绝对旋转角度*/

    fun setCurrentShapeData(shape: AbsCubeShapeView, nextShape: AbsCubeShapeView?, angle: Int = 0) {
        viewRotation = if (abs(rotation - angle) >= 180) angle - 360 else angle

        this.nextShapeData = nextShape
        this.shapeData = shape
        inImgBitmap?.recycle()
        outImgBitmap?.recycle()
        resImgBitmapList.forEach { bitmap -> bitmap?.recycle() }

        ViewCompat.animate(this).rotation(viewRotation.toFloat()).start()
    }

    /*
    生成进的图片
    first: 图片资源
    sec: 是否翻转
    */
    fun getInImgInfo(): Pair<Int, Boolean> {
        var needFlip = false
        val angle = shapeData?.offsetTag?.first ?: 0f
        val inImgID = when (angle) {
            0f -> R.drawable.new_6066_anzhuang_yanshi_gif_1ru
            60f -> {
                needFlip = true
                R.drawable.new_6066_anzhuang_yanshi_gif_2ru_1
            }
            300f -> R.drawable.new_6066_anzhuang_yanshi_gif_2ru_1
            else -> R.drawable.new_6066_anzhuang_yanshi_gif_1ru
        }
        log("getInImgData", "offset=${angle} needFlip=${needFlip}")

        return Pair(inImgID, needFlip)
    }

    /*
    生成进线的编号信息
    first: 对应边，不显示反null
    sec: 对应夹角
    */
    fun getInPathNum(): Pair<String, Float>? {
        if (shapeData?.offsetTag?.first == null || shapeData?.offsetTag?.first == 0f) {
            return null
        }

        val tag1 = if (shapeData?.offsetTag?.first == 60f) {
            -1
        }
        else {
            1
        }
        log("getInPathNum()", "tag1=$tag1 tagOffset=${shapeData?.offsetTag?.second} ")
        return Pair((shapeData?.offsetTag?.second!! + tag1).toString(), viewRotation.toFloat())
    }

    /*
    生成输出路径图片，处理图片形状，不处理旋转偏移量
    first : 图片资源
    sec: 水平翻转，垂直翻转
    third: 旋转角度
    */
    fun getOutImgInfo(outPathImg: ImageView): Pair<Int, Matrix> {

        val nextDirectionTag = shapeData?.nextDirectionTag ?: 0
        val matrix = Matrix()

        val scaleWidth = outPathImg.width * 1f / 230
        val scaleHeight = outPathImg.height * 1f / 230

        val size: Float = outPathImg.width.toFloat()

        matrix.postScale(scaleWidth, scaleHeight)
        val outImgID = when (nextDirectionTag) {
            1 -> {
                /*上*/
                /*水平翻转*/
//                matrix.postScale(-1f, 1f, size / 2, size / 2) //改为顺时针优先
                R.drawable.new_6066_anzhuang_yanshi_gif_2chu_1
            }
            3 -> {
                /*右下*/
                R.drawable.new_6066_anzhuang_yanshi_gif_2chu_1
            }
            5 -> {
                /*左下*/
                /*垂直翻转*/
                matrix.postScale(1f, -1f, size / 2, size / 2)
                R.drawable.new_6066_anzhuang_yanshi_gif_2chu_1
            }
            else -> {
                R.drawable.new_6066_anzhuang_yanshi_gif_1chu
            }
        }

        val offsetTag = shapeData?.offsetTag?.first ?: 0f
        val angle = when (nextDirectionTag) {
            1 -> {
//                360f - offsetTag + viewRotation - 120 //改为顺时针优先
                360f - offsetTag + viewRotation + 120
            }
            3 -> {
                360f - offsetTag + viewRotation
            }
            5 -> {
                360f - offsetTag + viewRotation + 180
            }
            else -> {
                360f - (nextDirectionTag * 60 - 60 + offsetTag) + viewRotation
            }
        }

        matrix.postRotate(Utils.rangeRotation360(angle), size / 2, size / 2)

        log("getOutImgInfo()",
                "\noffsetTag=$offsetTag \n" + "angle=${Utils.rangeRotation360(angle)} \n" + "viewRotation=$viewRotation \n" + "rotation=$rotation \n" + "nextDirectionTag=$nextDirectionTag\n" + "size=$size")

        return Pair(outImgID, matrix)
    }

    /*
    生成出线的编号信息
    first: 对应边，不显示反null
    sec: 对应夹角
    */
    fun getOutPathNum(): Pair<String, Float>? {
        if (nextShapeData == null) {
            return null
        }

        var num = when (shapeData?.nextDirectionTag) {
            0 -> 5
            1 -> 4
            2 -> 3
            3 -> 2
            4 -> 1
            5 -> 6
            else -> return null
        }
        val directionRotation: Float =
            shapeData?.getRotationByDirectionTag(shapeData?.nextDirectionTag ?: 0) ?: 0f

        log("getOutPathNum1()",
                "nextDirectionTag=${shapeData?.nextDirectionTag} num=$num  viewRotation=$viewRotation directionRotation=$directionRotation rotation=$rotation")
        log("getOutPathNum2()",
                "directionRotation=$directionRotation rotation=$rotation offsetTag=${shapeData?.offsetTag?.first}")

        var finalAngle =
            360 - (directionRotation + (shapeData?.offsetTag?.first ?: 0f) - viewRotation)
        when (shapeData?.nextDirectionTag) {
            1 -> {
//                num--
//                finalAngle -= 60f //改为顺时针优先
                num++
                finalAngle += 60f
            }
            3 -> {
                num++
                finalAngle += 60f
            }
            5 -> {
                num--
                finalAngle -= 60f
            }
        }

        return Pair("$num", Utils.rangeRotation360(finalAngle))
    }

    /*
    生成出线的转弯处的编号信息，不显示反null
    first: 对应边的编号
    sec: 对应夹角
    */
    fun getOutPathNumSwerve(): Pair<String, Float>? {
        if (nextShapeData == null) {
            return null
        }
        val num = when (shapeData?.nextDirectionTag) {
            1 -> 4
            3 -> 2
            5 -> 6
            else -> return null
        }
        val directionRotation: Float =
            shapeData?.getRotationByDirectionTag(shapeData?.nextDirectionTag ?: 0) ?: 0f

        log("getOutPathNum1()",
                "nextDirectionTag=${shapeData?.nextDirectionTag} num=$num  viewRotation=$viewRotation directionRotation=$directionRotation rotation=$rotation")
        log("getOutPathNum2()",
                "directionRotation=$directionRotation rotation=$rotation offsetTag=${shapeData?.offsetTag?.first}")

        val finalAngle =
            360 - (directionRotation + (shapeData?.offsetTag?.first ?: 0f) - viewRotation)
        return Pair("$num", Utils.rangeRotation360(finalAngle))
    }


    private fun getResImgBitmap(): Bitmap? {
        val index = rotationAnimationState
        var resImgBitmap = resImgBitmapList.getOrNull(index)
        z = if (index > 0) 1f else 0f
        if (resImgBitmap != null && !resImgBitmap.isRecycled) {
            return resImgBitmap
        }
        resImgBitmap = BitmapFactory.decodeResource(resources, resImgIDs[index]).let {
            Utils.scaleBitmap(it, imgSizePx, imgSizePx)
        }
        return resImgBitmap
    }

    private val bitmapMatrix = Matrix()
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let {
            /*图片旋转偏移*/
            getResImgBitmap()?.let {
                if (rotationAnimationState == 0 || rotationAnimationState == 3) {
                    val angle = shapeData?.offsetTag?.first ?: 360f
                    bitmapMatrix.reset()
                    val matrix = bitmapMatrix
                    matrix.postRotate(360 - angle, imgSizePx / 2f, imgSizePx / 2f)
                    canvas.drawBitmap(it, matrix, mPaint)
                }
                else {
                    canvas.drawBitmap(it, 0f, 0f, bitmapPaint)
                }

            }
        }
    }
}