package com.govee.cubeview.view

import android.content.Context
import android.util.AttributeSet
import com.govee.ui.R

/**
 *     author  : sinrow
 *     time    : 2022/4/12
 *     version : 1.0.0
 *     desc    :
 */
class SelectBoxView : androidx.appcompat.widget.AppCompatImageView {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context,
            attrs,
            defStyleAttr) {
        init()
    }

    fun init() {
        scaleType = ScaleType.FIT_CENTER
        setBackgroundResource(R.drawable.component_rect_style_8)
    }

}