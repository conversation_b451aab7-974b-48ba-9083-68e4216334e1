package com.govee.cubeview.view

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.govee.ui.R
import com.govee.cubeview.Utils

/**
 *     author  : sinrow
 *     time    : 2022/4/12
 *     version : 1.0.0
 *     desc    :
 */
class PhysicalSizeView : AppCompatTextView {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context,
            attrs,
            defStyleAttr) {
        init()
    }

    fun init() {
        setPadding(Utils.dp2px(context, 18f),
                Utils.dp2px(context, 6f),
                Utils.dp2px(context, 18f),
                Utils.dp2px(context, 5.5f))
        gravity = TEXT_ALIGNMENT_CENTER
        setTextSize(TypedValue.COMPLEX_UNIT_PX,
                Utils.getDimensionPixelSize(context, com.govee.ui.R.dimen.ui_flag_style_37_textSize)
                    .toFloat())

        setTextColor(ContextCompat.getColor(context, R.color.ui_flag_style_37_textColor))

        setBackgroundResource(com.govee.ui.R.drawable.component_flag_style_37)
    }
}