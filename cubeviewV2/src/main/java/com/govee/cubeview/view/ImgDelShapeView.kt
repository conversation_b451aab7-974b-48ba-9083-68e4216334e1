package com.govee.cubeview.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import com.govee.ui.R
import kotlin.math.min


/**
 * Create by lvwenrui on 2021/7/5 11:49
 * 删除shape按钮
 */
open class ImgDelShapeView : androidx.appcompat.widget.AppCompatImageView {

    companion object {
        //边长
        const val SIZE_DEL = 48
    }

    //圆角半径
    val textBgRadius = 12.5f

    /*图片资源索引*/
    private val imgResId = R.mipmap.new_btn_6061_mini_delete


    //中心点相对于父布局位置
    var pos: PointF? = null

    var bgColor: Int? = null
        set(value) {
            field = value
            /*画背景遮罩*/
            AppCompatResources.getDrawable(context, imgResId)?.let { imgDrawable ->
                val w = min(imgDrawable.intrinsicWidth, SIZE_DEL)
                val h = min(imgDrawable.intrinsicHeight, SIZE_DEL)

                val paint = Paint(Paint.ANTI_ALIAS_FLAG)
                value?.let { paint.color = it }
                paint.style = Paint.Style.FILL_AND_STROKE

                val bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)
                canvas.drawCircle(w / 2f, h / 2f, textBgRadius, paint)

                imgDrawable.setBounds(0, 0, w, h)
                imgDrawable.draw(canvas)
                setImageBitmap(bitmap)
            }
        }

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    private fun init(){
        maxWidth = SIZE_DEL
        maxHeight = SIZE_DEL
        scaleType = ScaleType.FIT_CENTER
        setImageResource(R.mipmap.new_btn_6061_mini_delete)
    }

}