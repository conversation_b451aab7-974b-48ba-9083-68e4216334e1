package com.govee.cubeview.shape

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

/**
 *     author  : sinrow
 *     time    : 2021/12/17
 *     version : 1.0.0
 *     desc    :
 */

/**
 * @param powerNumber 辅助电源所在的序号，从1开始，表示第几块
 *
 * @param powerEdgeNUmber 辅助电源所在哪个方向的序号（边）
 * @param optionalEdgeNumbers
 */

@Keep
data class MultiPower(var powerNumber: Int, var powerEdgeNUmber: Int,
        var optionalEdgeNumbers: MutableList<Int>) : Parcelable {

    constructor(parcel: Parcel) : this(
            parcel.readInt(),
            parcel.readInt(),
            parcel.readMutableList()) {
    }

    override fun toString(): String {
        return (powerNumber + powerEdgeNUmber).toString()
    }

    fun getBytes(): ByteArray {
        val byteArray = ByteArray(2)
        byteArray[0] = powerNumber.toByte()
        byteArray[1] = powerEdgeNUmber.toByte()
        return byteArray
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(powerNumber)
        parcel.writeInt(powerEdgeNUmber)
        parcel.writeList(optionalEdgeNumbers)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object {
        @JvmField val CREATOR = parcelableCreatorOf<MultiPower>()
    }

}

inline fun <reified T> Parcel.readMutableList(): MutableList<T> {
    @Suppress("UNCHECKED_CAST")
    return readArrayList(T::class.java.classLoader) as MutableList<T>
}

inline fun <reified T : Parcelable> parcelableCreatorOf(): Parcelable.Creator<T> =
    object : Parcelable.Creator<T> {
        override fun newArray(size: Int): Array<T?> = arrayOfNulls(size)
        override fun createFromParcel(source: Parcel?): T =
            T::class.java.getDeclaredConstructor(Parcel::class.java).newInstance(source)
    }
