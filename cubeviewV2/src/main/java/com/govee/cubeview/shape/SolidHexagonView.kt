package com.govee.cubeview.shape

import android.content.Context
import android.graphics.*
import android.util.Log
import com.govee.cubeview.Utils
import com.govee.cubeview.log
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import com.govee.ui.R


/**
 * Create by l<PERSON><PERSON><PERSON><PERSON> on 2021/7/2 17:00
 * 立体 六边形 带分区
 */
class SolidHexagonView(context: Context) : AbsCubeShapeView(context) {

    val textBgRadius = 12f//圆角半径

    init {
        LINE_LENGTH = 40f
        SHAPE_TYPE = Shape.TYPE_SOLID_HEXAGON
    }

    override fun init() {
        super.init()
        color4installed = Color.WHITE
        color4installedStroke = getColor(R.color.ui_color_block_style_16_4_stroke)

        color4installing = Color.WHITE
        color4installingStroke = getColor(R.color.ui_color_block_style_16_4_stroke)

        color4checking = Color.parseColor("#4DD248")

    }

    /*根据角度获取下一个shape的中点point*/
    override fun getShapeRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        val matrix = Matrix()
        matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
        val fa =
            floatArrayOf(
                (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
            )
        /*旋转*/
        matrix.mapPoints(fa)
        return PointF(fa[0], fa[1])

    }

    /* directionTag  0-5
     输出边 - 输入边
    *   1 -- none
    *   2 -- 1
    *   3 -- 2, 6
    *   4 -- 1
    *   5 -- 2, 6
    *   6 -- 1
    * */
    override fun getRotationByDirectionTag(directionTag: Int): Float {
        return directionTag * 60 - 60f
    }

    override fun generateConfig(): CubeConfig {
        val config = CubeConfig()
        config.showNumTagInInstallMode = true
        config.showCustomCheckingUI = true
        return config
    }

    override fun getTextColor(): Int {
        if (isInstalledUI) {
            return getColor(R.color.font_style_226_1_textColor)
        } else if (isInstallingUI) {
            return getColor(R.color.font_style_226_2_textColor)
        }

        return super.getTextColor()
    }

    /**/
    override fun drawShapeModifier(canvas: Canvas, pointF: PointF, length: Float, rotation: Float) {
        if (isPreViewUI) {
            drawCustomPreViewUI(canvas, pointF)
        }

        val shapePoint =
            arrayOf(
                PointF(
                    (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
                ),
                PointF((pointF.x), (pointF.y)),
                PointF(
                    (length * cos(0.0) + pointF.x).toFloat(),
                    (length * sin(0.0) + pointF.y).toFloat()
                ),
                PointF((pointF.x), (pointF.y)),
                PointF(
                    (length * cos(2 * PI * 2 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + pointF.y).toFloat()
                ),
//                PointF(
//                        (length * cos(0.0) + pointF.x).toFloat(),
//                        (length * sin(0.0) + pointF.y).toFloat()
//                ),
            )

        path?.let {
            it.reset()
            shapePoint.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }

            /*旋转*/
            val matrix = Matrix()
            matrix.setRotate(canvasRotation + rotation, width / 2f, height / 2f)

            val modifierPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            val resId = if (isNeedDrawColorMode) {
                /*颜色反转*/
                val red = Color.red(mColorModeColor)
                val green = Color.green(mColorModeColor)
                val blue = Color.blue(mColorModeColor)
//        val textColor = UtilColor.toColor(255 - red, 255 - green, 255 - blue)
                if (red * 0.299 + green * 0.578 + blue * 0.114 >= 192) {
                    //浅色
                    R.color.ui_line_style_2_1_stroke_color
                } else {
                    //深色
                    R.color.ui_line_style_2_2_stroke_color
                }
            } else if (isInstalledUI || isInstallingUI) {
                R.color.FFEAEAEA
            } else if (isPreViewUI) {
                R.color.FFFFFFFF_60
            } else {
                R.color.ui_line_style_1_1_stroke_color
            }
            val strokeWidth = if (isPreViewUI) 2f else 1.5f
            modifierPaint.color = getColor(resId)
            modifierPaint.strokeWidth = strokeWidth
            modifierPaint.style = Paint.Style.STROKE

            /*画路径*/
            it.transform(matrix)
            canvas.drawPath(it, modifierPaint)

            it.reset()
        }


        if (isInstalledUI || isInstallingUI || isCheckingUI) {
            drawCustomCheckingUI(canvas)
        }

    }

    private fun drawCustomPreViewUI(canvas: Canvas, pointF: PointF) {

        val allShapePoint =
            getShapePoint(
                PointF((width / 2).toFloat(), (height / 2).toFloat()),
                OUTSIDE_LINE_LENGTH,
                preRotate
            )

        val shapePosition1 =
            arrayOf(
                PointF((pointF.x), (pointF.y)),
                PointF(allShapePoint[3][0], allShapePoint[3][1]),
                PointF(allShapePoint[4][0], allShapePoint[4][1]),
                PointF(allShapePoint[5][0], allShapePoint[5][1])
            )

        val shapePosition2 =
            arrayOf(
                PointF((pointF.x), (pointF.y)),
                PointF(allShapePoint[5][0], allShapePoint[5][1]),
                PointF(allShapePoint[0][0], allShapePoint[0][1]),
                PointF(allShapePoint[1][0], allShapePoint[1][1])
            )

        val shapePoint3 =
            arrayOf(
                PointF((pointF.x), (pointF.y)),
                PointF(allShapePoint[1][0], allShapePoint[1][1]),
                PointF(allShapePoint[2][0], allShapePoint[2][1]),
                PointF(allShapePoint[3][0], allShapePoint[3][1])
            )
        log("TAG -- ", "preViewColors3 = ${preViewColors[1]}")
        drawView(canvas, shapePosition1, preViewColors[0])
        drawView(canvas, shapePosition2, preViewColors[1])
        drawView(canvas, shapePoint3, preViewColors[2])
    }

    private fun drawView(canvas: Canvas, shapePoint3: Array<PointF>, bgColor: Int) {
        path?.let {
            it.reset()
            shapePoint3.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats.x, floats.y)
                } else {
                    it.lineTo(floats.x, floats.y)
                }
            }
            it.close()
            val checkingColorPaint = Paint()
            checkingColorPaint.color = bgColor
            checkingColorPaint.style = Paint.Style.FILL
            canvas.drawPath(it, checkingColorPaint)
            it.reset()
        }
    }

    /*画校准ui*/
    private fun drawCustomCheckingUI(canvas: Canvas) {
        val checkingShapePoint = getCheckStateAreaInfo().second
        path?.let {
            it.reset()
            checkingShapePoint.forEachIndexed { index, floats ->
                if (index == 0) {
                    it.moveTo(floats[0], floats[1])
                } else {
                    it.lineTo(floats[0], floats[1])
                }
            }
            it.close()

            val checkingColorPaint = Paint()
            checkingColorPaint.color = color4checking
            checkingColorPaint.style = Paint.Style.FILL

            /*画路径*/
            canvas.drawPath(it, checkingColorPaint)

            it.reset()
        }
    }


    fun getCheckStateIcIndex(): Int {
        val topPoint = getCheckStateAreaInfo().first
        var icIndex = 0
        when (topPoint) {
            1, 2 -> icIndex = 0
            3, 4 -> icIndex = 2
            0, 5 -> icIndex = 4
        }
        return icIndex
    }

    //获取安装时的显示区域信息
    fun getCheckStateAreaInfo(): Pair<Int, Array<FloatArray>> {
        val pointF = PointF((width / 2).toFloat(), (height / 2).toFloat())

        var tag = offsetTag?.first ?: -60f
        if (tag == 0f) tag = -60f

        val allShapePoint =
            getShapePoint(
                PointF((width / 2).toFloat(), (height / 2).toFloat()),
                OUTSIDE_LINE_LENGTH,
//            offsetRotation + canvasRotation - (tag) - 60f
                offsetRotation + canvasRotation
            )

        var index = 0
        //x最大，y最小
        val minPoint = intArrayOf(Int.MIN_VALUE, Int.MAX_VALUE, 0)

        //转成int，防止float判断误差
        val allShapePoint4Int = arrayOfNulls<IntArray>(6)
        allShapePoint.forEachIndexed { i, floats ->
            allShapePoint4Int[i] = intArrayOf(floats[0].toInt(), floats[1].toInt())
        }

        allShapePoint4Int.forEachIndexed { i, curPoint ->
            if (curPoint!![1] == minPoint[1]) {
                if (curPoint[0] > minPoint[0]) {
                    index = i
                    minPoint[0] = curPoint[0]
                    minPoint[1] = curPoint[1]
                    minPoint[2] = 1
                }
            } else if (curPoint[1] < minPoint[1]) {
                index = i
                minPoint[0] = curPoint[0]
                minPoint[1] = curPoint[1]
            }
        }

        //奇数边+1
        if (index % 2 == 1) {
            if (minPoint[2] == 1) {
                index--
            } else {
                index++
            }
        }
        index = Utils.rangeInt(index, 6)

        Log.i(
            "getCheckStateAreaInfo",
            "offsetR=$offsetRotation, canvasR=$canvasRotation, tagR=${tag}, index=$index"
        )

        return Pair(
            index,
            arrayOf(
                allShapePoint[Utils.rangeInt(index - 1, 6)],
                allShapePoint[index],
                allShapePoint[Utils.rangeInt(index + 1, 6)],
                floatArrayOf((pointF.x), (pointF.y)),
            )
        )
    }

    override fun drawNumTextBackground(canvas: Canvas) {
        val textBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        textBgPaint.color = if (isNeedDrawColorMode) mColorModeColor else mColor
        textBgPaint.style = Paint.Style.FILL_AND_STROKE

        canvas.drawCircle(width / 2f, height / 2f, textBgRadius, textBgPaint)
    }

    override fun getShapePoint(
        pointF: PointF,
        length: Float,
        rotation: Float,
        forSelf: Boolean
    ): Array<FloatArray> {
        val matrix = Matrix()
        matrix.setRotate(rotation, pointF.x, pointF.y)

        val shapePoint =
            arrayOf(
                floatArrayOf(
                    (length * cos(2 * PI * 3 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 3 / 6) + pointF.y).toFloat()
                ),
                floatArrayOf(
                    (length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat()
                ),
                floatArrayOf(
                    (length * cos(2 * PI * 5 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 5 / 6) + pointF.y).toFloat()
                ),
                floatArrayOf(
                    (length * cos(0.0) + pointF.x).toFloat(),
                    (length * sin(0.0) + pointF.y).toFloat()
                ),
                floatArrayOf(
                    (length * cos(2 * PI * 1 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 1 / 6) + pointF.y).toFloat()
                ),
                floatArrayOf(
                    (length * cos(2 * PI * 2 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + pointF.y).toFloat()
                ),
            )


        shapePoint.forEach {
            matrix.mapPoints(it)
        }

        return shapePoint
    }

    /*获取传输协议时的旋转角度*/
    override fun getRotationValue(): Int {
        val angle = ((offsetRotation + canvasRotation).toInt()) % 360
        return Utils.rangeRotation360(angle)
    }

}