package com.govee.cubeview.shape

import android.graphics.PointF

/**
 *     author  : sinrow
 *     time    : 2021/11/11
 *     version : 1.0.0
 *     desc    :
 */
interface ICubeShapeView {

    /**
     * 初始化数据
     */
    fun init()

    /**
     *
     * 根据角度获取下一个shape的中点point，多边形的其实坐标不一样，内部自己实现
     *
     */
    fun getShapeRotation(pointF: PointF, length: Float, rotation: Float): PointF

    /**
     * 获取多边形旋转后的坐标
     * @param forSelf 是否用于画图形
     */
    fun getShapePoint(pointF: PointF,
            length: Float,
            rotation: Float,
            forSelf: Boolean = true): Array<FloatArray>

    /**
     * 根据协议，获取旋转角度
     */
    fun getRotationValue(): Int

    /**
     * 根据方向获取方向角度 Tag 0-5
     */
    fun getRotationByDirectionTag(directionTag: Int): Float
}