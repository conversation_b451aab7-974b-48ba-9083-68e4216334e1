package com.govee.cubeview.shape;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * Create by linshicong on 4/22/21
 */
@Keep
public class ShapePosition implements Parcelable {
    public int type;
    public float x;
    public float y;
    public int angle;
    public MultiPower ext;
    public int lastPosition;
    public int state;

    public ShapePosition(int type, float x, float y, int angle) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.angle = angle;
    }

    public ShapePosition(int type, float x, float y, int angle, int lastPosition, int state) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.angle = angle;
        this.state = state;
        this.lastPosition = lastPosition;
    }


    protected ShapePosition(Parcel in) {
        type = in.readInt();
        x = in.readFloat();
        y = in.readFloat();
        angle = in.readInt();
        lastPosition = in.readInt();
        state = in.readInt();
        ext = in.readParcelable(MultiPower.class.getClassLoader());
    }

    public boolean isExt() {
        return ext != null && ext.getPowerEdgeNUmber() > 0 && ext.getPowerEdgeNUmber() > 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(type);
        dest.writeFloat(x);
        dest.writeFloat(y);
        dest.writeInt(angle);
        dest.writeInt(lastPosition);
        dest.writeInt(state);
        dest.writeParcelable(ext, flags);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ShapePosition> CREATOR = new Creator<ShapePosition>() {
        @Override
        public ShapePosition createFromParcel(Parcel in) {
            return new ShapePosition(in);
        }

        @Override
        public ShapePosition[] newArray(int size) {
            return new ShapePosition[size];
        }
    };

    @NonNull
    @Override
    public String toString() {
        return "ShapePosition{" +
                "type=" + type +
                ", x=" + x +
                ", y=" + y +
                ", angle=" + angle +
                ", lastPosition=" + lastPosition +
                ", state=" + state +
                ", ext=" + ext +
                '}';
    }
}