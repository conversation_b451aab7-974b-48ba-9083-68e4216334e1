package com.govee.cubeview.shape

import android.content.Context
import android.graphics.*
import com.govee.ui.R
import com.govee.cubeview.Utils
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin


/**
 * Create by l<PERSON><PERSON><PERSON><PERSON> on 2021/7/2 17:00
 * 六边形
 */
class SquraViewNew(context: Context) : AbsCubeShapeView(context) {

    init {
        LINE_LENGTH = 40f
        SHAPE_TYPE = Shape.TYPE_HEXAGON
        addImgRotationOffset = 30

        color4canSelect = getColor(R.color.ui_color_block_style_16_1_color)
        color4unselected = getColor(R.color.ui_color_block_style_16_1_color)
        color4selecting = getColor(R.color.ui_color_block_style_16_6_color)
    }

    /*画形状*/
    private fun drawShape(canvas: Canvas, strokePaint: Paint) {
        drawHexagon(canvas, strokePaint)
        drawNumText(canvas)
        drawColorMode(canvas)
    }

    /*画六边形*/
    private fun drawHexagon(canvas: Canvas, strokePaint: Paint) {
        val shapePoint = getShapePoint()
        shapePoint.forEachIndexed { index, floats ->
            if (index == 0) {
                path!!.moveTo(floats[0], floats[1])
            }
            else {
                path!!.lineTo(floats[0], floats[1])
            }
        }
        path!!.close()
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        paint.color = mColorModeColor

        /*旋转*/
        val matrix = Matrix()
        matrix.setRotate(canvasRotation, width / 2f, height / 2f)

        /*画路径*/
        path!!.transform(matrix)
        canvas.drawPath(path!!, paint)
        canvas.drawPath(path!!, strokePaint)

        path!!.reset()
    }

    /*画编号*/
    private fun drawNumText(canvas: Canvas) {
        if (numText.isNotEmpty()) {
            val textPaint = Paint()
            textPaint.color = getTextColor()
            textPaint.textSize = 20.8f
            textPaint.style = Paint.Style.FILL
            //该方法即为设置基线上那个点究竟是left,center,还是right  这里我设置为center
            textPaint.textAlign = Paint.Align.CENTER

            drawTextOnCenter(canvas, textPaint, numText)
        }
    }

    override fun generateConfig(): CubeConfig {
        return CubeConfig()
    }

    /*画颜色模式*/
    private fun drawColorMode(canvas: Canvas) {
        if (!isClickable) return

        /*选中画框*/
        if (mSelected) {
            drawSelectedStroke(canvas)
        }

        val textPaint = Paint()
        /*颜色反转*/
        val red = Color.red(mColorModeColor)
        val green = Color.green(mColorModeColor)
        val blue = Color.blue(mColorModeColor)
        if (red * 0.299 + green * 0.578 + blue * 0.114 >= 192) {
            //浅色
            textPaint.color = Color.BLACK
        }
        else {
            //深色
            textPaint.color = Color.WHITE
        }
        /*大小*/
        textPaint.textSize = 16f
        textPaint.style = Paint.Style.FILL
        //该方法即为设置基线上那个点究竟是left,center,还是right  这里我设置为center
        textPaint.textAlign = Paint.Align.CENTER
        drawTextOnCenter(canvas, textPaint, "${mBrightness}%")

    }

    /*画选中线*/
    private fun drawSelectedStroke(canvas: Canvas) {
        val shapePoint =
            getShapePoint(PointF((width / 2).toFloat(), (height / 2).toFloat()),
                    LINE_LENGTH - STROKE_WIDTH / 2 - COLOR_MODE_STROKE_WIDTH / 2,
                    0f)

        path!!.reset()

        path!!.moveTo(shapePoint[0][0], shapePoint[0][1])
        path!!.lineTo(shapePoint[1][0], shapePoint[1][1])
        path!!.lineTo(shapePoint[2][0], shapePoint[2][1])
        path!!.lineTo(shapePoint[3][0], shapePoint[3][1])
        path!!.lineTo(shapePoint[4][0], shapePoint[4][1])
        path!!.lineTo(shapePoint[5][0], shapePoint[5][1])
        path!!.close()

        /*旋转*/
        val matrix = Matrix()
        matrix.setRotate(canvasRotation, width / 2f, height / 2f)

        /*画路径*/
        path!!.transform(matrix)
        canvas.drawPath(path!!, colorModeStrokePaint)

        path!!.reset()
    }

    private fun drawTextOnCenter(canvas: Canvas, textPaint: Paint, text: String) {
        val fontMetrics = textPaint.fontMetrics
        val top = fontMetrics.top//为基线到字体上边框的距离,即上图中的top
        val bottom = fontMetrics.bottom//为基线到字体下边框的距离,即上图中的bottom

        val baseLineY = (height / 2).toFloat() - top / 2 - bottom / 2//基线中间点的y轴计算公式

        canvas.drawText(text, (width / 2).toFloat(), baseLineY, textPaint)
    }

    /*形状的点*/
    private var mShapePoint: Array<FloatArray>? = null

    /*获取六边形的点*/
    private fun getShapePoint(): Array<FloatArray> {
        if (mShapePoint.isNullOrEmpty()) {
            mShapePoint =
                getShapePoint(PointF((width / 2).toFloat(), (height / 2).toFloat()),
                        LINE_LENGTH,
                        0f)
        }
        return mShapePoint!!
    }

    /*根据角度获取下一个shape的中点point*/
    override fun getShapeRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        val matrix = Matrix()
        matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
        val fa =
            floatArrayOf((length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat())
        /*旋转*/
        matrix.mapPoints(fa)
        return PointF(fa[0], fa[1])

    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        return directionTag * 60 - 60f
    }

    override fun getShapePoint(pointF: PointF,
            length: Float,
            rotation: Float,
            forSelf: Boolean): Array<FloatArray> {
        val matrix = Matrix()//旋转
        matrix.setRotate(rotation, pointF.x, pointF.y)

        val shapePoint = Array(6) { FloatArray(2) }
        shapePoint[0] =
            floatArrayOf((length * cos(2 * PI * 3 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 3 / 6) + pointF.y).toFloat())
        shapePoint[1] =
            floatArrayOf((length * cos(2 * PI * 4 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 4 / 6) + pointF.y).toFloat())
        shapePoint[2] =
            floatArrayOf((length * cos(2 * PI * 5 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 5 / 6) + pointF.y).toFloat())
        shapePoint[3] =
            floatArrayOf((length * cos(0.0) + pointF.x).toFloat(),
                    (length * sin(0.0) + pointF.y).toFloat())
        shapePoint[4] =
            floatArrayOf((length * cos(2 * PI * 1 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 1 / 6) + pointF.y).toFloat())
        shapePoint[5] =
            floatArrayOf((length * cos(2 * PI * 2 / 6) + pointF.x).toFloat(),
                    (length * sin(2 * PI * 2 / 6) + pointF.y).toFloat())

        matrix.mapPoints(shapePoint[0])
        matrix.mapPoints(shapePoint[1])
        matrix.mapPoints(shapePoint[2])
        matrix.mapPoints(shapePoint[3])
        matrix.mapPoints(shapePoint[4])
        matrix.mapPoints(shapePoint[5])

        return shapePoint
    }

    /*获取传输协议时的旋转角度*/
    override fun getRotationValue(): Int {
        var angle = ((offsetRotation + canvasRotation).toInt() + 60) % 360
        angle = Utils.rangeRotation360(angle)
        return angle
    }


}