package com.govee.cubeview.shape

import android.text.TextUtils
import com.govee.cubeview.Utils
import kotlin.math.*

/**
 *     author  : sinrow
 *     time    : 2021/11/16
 *     version : 1.0.0
 *     desc    :
 */
object Shape {

    /**
     *  正六边形
     * <p>
     *     蓝牙协议中定义类型为 0
     * </p>
     */
    const val TYPE_HEXAGON = 0

    /**
     * 三角形
     * <p>
     *     蓝牙协议中定义类型为 3
     * </p>
     */
    const val TYPE_TRIANGLE = 3

    /**
     * 立体 六边形
     * <p>
     *     蓝牙协议中定义类型为 4
     * </p>
     */
    const val TYPE_SOLID_HEXAGON = 4

    /**
     * Y 形灯
     */
    const val TYPE_Y = 0x0F

    const val TYPE_DEFAULT = TYPE_HEXAGON

    /**
     * 六边形的边长
     */
    const val LINE_LENGTH_4_SQURA = 40

    const val op_type_splice = 0
    const val op_type_calibration = 1
    const val op_type_adjust = 2

    private const val diffValue = 3

    private var supportShape = arrayOf(TYPE_HEXAGON)

    /**
     * 实际设备的 ic 数
     */
    fun getTypeIc(type: Int): Int {
        return when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 6
            TYPE_SOLID_HEXAGON -> 6
            TYPE_Y -> 6
            else -> 6
        }
    }

    /**
     * 获取正多边形内切圆半径
     * @param length 边长
     * @param type 多边形类型
     * <p>
     * 内切圆半径计算公式,
     * x = 半径
     * n = 边数
     * 公式 ：x*cos(π/n)
     *</p>
     */
    fun getShapeInnerRadius(length: Float, type: Int, stroke: Int = 0): Float {
        val n = when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 3
            TYPE_SOLID_HEXAGON -> 6
            TYPE_Y -> 3
            else -> 6
        }
        if (type == TYPE_HEXAGON || type == TYPE_SOLID_HEXAGON) {
            return ((length - stroke / 2f) * sqrt(3f) + stroke / 2f) / 2f
        }
        val r = (length / 2) * cos(PI / n) / sin(PI / n)
        return r.toFloat()
    }

    /**
     *  获取正多边形外接圆半径
     *  <p>
     *      公式： r = a/(2*sin(π/n))
     *      r：半径
     *      a：边长
     *      n：边数
     *  </p>
     */
    fun getShapeOutsideRadius(length: Float, type: Int): Float {
        val n = when (type) {
            TYPE_HEXAGON -> 6
            TYPE_TRIANGLE -> 3
            TYPE_SOLID_HEXAGON -> 6
            TYPE_Y -> 3
            else -> 6
        }
        val result = length * 1 / (2 * sin(PI / n))
        return result.toFloat()
    }

    fun filterSupportType(list: MutableList<Int>): List<Int> {
        val supportList = listOf(*supportShape)
        val iterator = list.iterator()
        if (iterator.hasNext()) {
            val next = iterator.next()
            if (!supportList.contains(next)) {
                iterator.remove()
            }
        }
        return list
    }

    fun getShapes(shapes: String?): ArrayList<ShapePosition> {
        val shapePositions = ArrayList<ShapePosition>()
        if (shapes == null || TextUtils.isEmpty(shapes)) return shapePositions
        val bytes: ByteArray = Utils.decryByBase64(shapes) ?: return shapePositions
        val size = bytes[0].toInt()
        if (bytes.size != 1 + 7 * size && bytes.size != 1 + 7 * size + 2 && bytes.size != 1 + 7 * size + 2 + size) {
            return shapePositions
        }
        var pos = 1
        for (i in 0 until size) {
            val type = bytes[pos++].toInt()
            val x: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])
            val y: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])
            val angle: Int = Utils.convertTwoBytesToShort(bytes[pos++], bytes[pos++])

            shapePositions.add(ShapePosition(type, x.toFloat(), y.toFloat(), angle, 0, 0))
        }

        if (bytes.size == (1 + 7 * size + 2)) {
            // 扩展内容
            val powerNumber = bytes[pos++].toInt()
            val powerEdgeNumber = bytes[pos].toInt()
            shapePositions[powerNumber - 1].ext =
                MultiPower(powerNumber, powerEdgeNumber, mutableListOf())
        }
        if (bytes.size == 1 + 7 * size + 2 + size) {
            pos += 2
            shapePositions.forEach {
                it.state = bytes[pos++].toInt()
            }
        }
        return shapePositions
    }

    /**
     * 校验是否编辑过
     * */
    fun checkHadEdit(shapes1: ArrayList<ShapePosition>,
            shapes2: ArrayList<ShapePosition>): Boolean {
        if (shapes1.size != shapes2.size) return true
        for (i in shapes1.indices) {
            val curShape = shapes1[i]
            val checkShape = shapes2[i]
            if (curShape.type != checkShape.type || curShape.angle != checkShape.angle) {
                return true
            }
            val diffX = abs(curShape.x - checkShape.x)
            val diffY = abs(curShape.y - checkShape.y)

            if (diffX > diffValue || diffY > diffValue) {
                return true
            }
        }
        return false
    }

    fun isOpSplice(opType: Int): Boolean {
        return opType == op_type_splice
    }

    fun isOpCalibration(opType: Int): Boolean {
        return opType == op_type_calibration
    }

    fun isOpAdjust(opType: Int): Boolean {
        return opType == op_type_adjust
    }

}