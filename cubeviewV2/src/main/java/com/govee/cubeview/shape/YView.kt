package com.govee.cubeview.shape

import android.content.Context
import android.graphics.*
import com.govee.cubeview.Constants
import com.govee.cubeview.Utils
import com.govee.ui.R
import kotlin.math.*

/**
 *     author  : sinrow
 *     time    : 2022/5/5
 *     version : 1.0.0
 *     desc    :
 */
class YView(context: Context) : AbsCubeShapeView(context) {
    private val TAG = "YView"
    private var diffX = 8f// 宽度
    val textBgRadius = 15f//数字文本背景圆角半径
    val curLightMode by lazy { Utils.curLightMode(context) }

    init {
        LINE_LENGTH = 56 * sqrt(3f)
        SHAPE_TYPE = Shape.TYPE_Y
        addImgRotationOffset = 0
        STROKE_WIDTH = 2

        color4canSelect = getColor(R.color.ui_color_block_style_16_1_color)
        color4unselected = getColor(R.color.ui_color_block_style_16_1_color)
        color4selecting = getColor(R.color.ui_color_block_style_16_6_color)

        mColorProjection = getColor(R.color.ui_line_style_4_1_stroke_color)
        mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
        mColorSheetMetal = getColor(R.color.ui_line_style_4_3_stroke_color)
        mColorGuideLight = getColor(R.color.ui_line_style_4_4_stroke_color)
    }

    private val mBlurMaskFilter by lazy { BlurMaskFilter(18f, BlurMaskFilter.Blur.NORMAL) }


    override fun generateConfig(): CubeConfig {
        val config = CubeConfig()
        config.showNumTagInInstallMode = false
        config.showNumTagCheckedMode = true
        config.showCustomCheckingUI = true
        config.handleNumColorMode = false
        config.transparentBgCheckedMode = true
        return config
    }

    override fun getShapePoint(
        centerPointF: PointF, length: Float, rotation: Float,
        forSelf: Boolean
    ): Array<FloatArray> {
        val matrix = Matrix()//旋转
        matrix.setRotate(rotation - 150f, centerPointF.x, centerPointF.y)

        val shapePoint = Array(9) { FloatArray(2) }

        for (i in 0..2) {
            shapePoint[i] =
                floatArrayOf(
                    centerPointF.x + (length * cos(2 * PI * i / 3)).toFloat(),
                    centerPointF.y + (length * sin(2 * PI * i / 3)).toFloat()
                )
            if (!forSelf) {
                matrix.mapPoints(shapePoint[i])
            }
        }
        if (!forSelf) {
            return shapePoint.filterIndexed { index, floats -> index < 3 }.toTypedArray()
        }

        val point0 = shapePoint[0]
        val point1 = shapePoint[2]
        val point2 = shapePoint[1]

        val a1 = PointF(point0[0], point0[1] + diffX)/*左上角顶点*/
        val a2 = PointF(point0[0], point0[1] - diffX)/*左下角底点*/


        val a3 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y - diffX)/*中心左边点*/

        val a4 = PointF(point1[0] + diffX * sqrt(3f) / 2, point1[1] - diffX / 2)/*底部左边点*/
        val a5 = PointF(point1[0] - diffX * sqrt(3f) / 2, point1[1] + diffX / 2)/*底部右边点*/

        val a6 = PointF(centerPointF.x - diffX * 2 / sqrt(3f), centerPointF.y)/*中心右边点*/

        val a7 = PointF(point2[0] - diffX * sqrt(3f) / 2, point2[1] - diffX / 2)/*右上角底点*/

        val a8 = PointF(point2[0] + diffX * sqrt(3f) / 2, point2[1] + diffX / 2)/*右上角顶点*/

        val a9 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y + diffX)/*中心顶点*/

        shapePoint[0] = floatArrayOf(a1.x, a1.y)
        shapePoint[1] = floatArrayOf(a2.x, a2.y)
        shapePoint[2] = floatArrayOf(a3.x, a3.y)
        shapePoint[3] = floatArrayOf(a4.x, a4.y)
        shapePoint[4] = floatArrayOf(a5.x, a5.y)
        shapePoint[5] = floatArrayOf(a6.x, a6.y)
        shapePoint[6] = floatArrayOf(a7.x, a7.y)
        shapePoint[7] = floatArrayOf(a8.x, a8.y)
        shapePoint[8] = floatArrayOf(a9.x, a9.y)

        shapePoint.forEach {
            matrix.mapPoints(it)
        }

        return shapePoint
    }

    override fun getShapeRotation(pointF: PointF, length: Float, rotation: Float): PointF {
        val matrix = Matrix()
        matrix.setRotate(rotation + 30f, pointF.x, pointF.y)
        val fa =
            floatArrayOf(
                (length * cos(2 * PI * 2 / 3) + pointF.x).toFloat(),
                (length * sin(2 * PI * 2 / 3) + pointF.y).toFloat()
            )
        /*旋转*/
        matrix.mapPoints(fa)
        return PointF(fa[0], fa[1])
    }

    override fun getRotationByDirectionTag(directionTag: Int): Float {
        // 奇数 60 ，偶数 = 300
        if (directionTag < 0) return 0f
        return if (directionTag % 2 == 0) {
            300f
        } else {
            60f
        }
    }

    override fun drawShape(canvas: Canvas) {
        resetPaintColor()
        drawProjection(canvas)
        drawBottomEdge(canvas)
        drawSheetMetal(canvas)
        drawGuideLight(canvas)
    }

    override fun drawSelectedStroke(canvas: Canvas, paint: Paint) {
        if (!isNeedDrawColorMode) return

        val min = min(min(width, height), 80)

        val minX = (width - min) / 2f
        val minY = (height - min) / 2f
        val maxX = (width + min) / 2f
        val maxY = (height + min) / 2f

        val rect = Array(4) { FloatArray(2) }

        rect[0] = floatArrayOf(minX, minY)
        rect[1] = floatArrayOf(minX, maxY)
        rect[2] = floatArrayOf(maxX, maxY)
        rect[3] = floatArrayOf(maxX, minY)


        path?.let {
            it.reset()
            rect.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF[0], pointF[1])
                } else {
                    it.lineTo(pointF[0], pointF[1])
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = getColor(R.color.ui_rect_style_2_stroke)
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 2f

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    private fun resetPaintColor() {
        if (curLightMode) {
            setColor4LightMode()
        } else {
            setColor4DarkMode()
        }
    }

    private fun setColor4DarkMode() {
        if (isInstalledUI) {
            /*已安装的*/
            mColorProjection = getColor(R.color.FF204653)
            mColorBottomEdge = getColor(R.color.FF204653)
        } else if (isInstallingUI) {
            /*正在安装的*/
            mColorProjection = getColor(R.color.transparent)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_4_stroke_color)
        } else if (isCheckedUI) {
            /*已检查的*/
            mColorProjection = getColor(R.color.white)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.white)
        } else if (isCheckingUI) {
            /*正在检查*/
            mColorProjection = getColor(R.color.ui_line_style_4_1_stroke_color)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_1_stroke_color)
        } else if (isNeedDrawColorMode) {
            /*颜色模式，采用默认配置*/
            // setColorMode4Brightness
        } else if (isPreViewUI) {
            mColorBottomEdge = preViewColors[3]
            mColorSheetMetal = preViewColors[4]
        } else {
            /*普通场景*/
            mColorProjection = getColor(R.color.ui_line_style_4_1_stroke_color)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_2_stroke_color)
        }
    }

    private fun setColor4LightMode() {
        if (isInstalledUI) {
            /*已安装的*/
            mColorProjection = getColor(R.color.FFB2E2F3)
            mColorBottomEdge = getColor(R.color.FFB2E2F3)
        } else if (isInstallingUI) {
            /*正在安装的*/
            mColorProjection = getColor(R.color.transparent)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
        } else if (isCheckedUI) {
            /*已检查的*/
            mColorProjection = getColor(R.color.FFB2E2F3)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_2_stroke_color)
        } else if (isCheckingUI) {
            /*正在检查*/
            mColorProjection = getColor(R.color.ui_line_style_4_1_stroke_color)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_1_stroke_color)
        } else if (isNeedDrawColorMode) {
            /*颜色模式，采用默认配置*/
            // setColorMode4Brightness
        } else if (isPreViewUI) {
            // 预览模式
            mColorBottomEdge = preViewColors[3]
            mColorSheetMetal = preViewColors[4]
        } else {
            /*普通场景*/
            mColorProjection = getColor(R.color.ui_line_style_4_1_stroke_color)
            mColorBottomEdge = getColor(R.color.ui_line_style_4_2_stroke_color)
            mColorGuideLight = getColor(R.color.ui_line_style_4_2_stroke_color)
        }
    }

    override fun drawNumTextBackground(canvas: Canvas) {
        if (isNeedDrawColorMode) return
        var paintColor = mColor
        if (!curLightMode) {
            paintColor = getColor(R.color.ui_line_style_4_4_stroke_color)
        }
        val textBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        textBgPaint.color = paintColor
        textBgPaint.style = Paint.Style.FILL_AND_STROKE
        textBgPaint.textSize =
            Utils.getDimensionPixelSize(context, R.dimen.font_style_185_1_textSize).toFloat()

        canvas.drawCircle(width / 2f, height / 2f, textBgRadius, textBgPaint)
    }

    /* 投影 */
    private fun drawProjection(canvas: Canvas) {
        if (!isCheckingUI && !isCheckedUI && !isInstalledUI && !isInstallingUI && !isNeedDrawColorMode && !isPreViewUI) {
            return
        }
        diffX = 9f
        val shapePoint = getShapePoint(OUTSIDE_LINE_LENGTH - 8)
        // 重写图形

        val rect = Array(4) { FloatArray(2) }
        rect[0] = shapePoint[0]
        rect[1] = shapePoint[1]
        rect[2] = shapePoint[2]
        rect[3] = shapePoint[8]

        var fetchRoundColor4PreViewEdge = 0
        if (isPreViewUI) {
            fetchRoundColor4PreViewEdge = Constants.fetchRoundColor4PreViewEdge()
            preViewColors[0] = fetchRoundColor4PreViewEdge
            mColorProjection = fetchRoundColor4PreViewEdge
        }
        drawRectView(rect, canvas)

        rect[0] = shapePoint[2]
        rect[1] = shapePoint[3]
        rect[2] = shapePoint[4]
        rect[3] = shapePoint[5]
        if (isPreViewUI) {
            fetchRoundColor4PreViewEdge = Constants.fetchRoundColor4PreViewEdge()
            preViewColors[1] = fetchRoundColor4PreViewEdge
            mColorProjection = fetchRoundColor4PreViewEdge
        }
        drawRectView(rect, canvas)

        rect[0] = shapePoint[5]
        rect[1] = shapePoint[6]
        rect[2] = shapePoint[7]
        rect[3] = shapePoint[8]
        if (isPreViewUI) {
            fetchRoundColor4PreViewEdge = Constants.fetchRoundColor4PreViewEdge()
            preViewColors[2] = fetchRoundColor4PreViewEdge
            mColorProjection = fetchRoundColor4PreViewEdge
        }
        drawRectView(rect, canvas)

    }

    private fun drawRectView(shapePoint: Array<FloatArray>, canvas: Canvas) {
        path?.let {
            it.reset()
            shapePoint.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF[0], pointF[1])
                } else {
                    it.lineTo(pointF[0], pointF[1])
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = mColorProjection
            paint.style = Paint.Style.FILL
            paint.maskFilter = mBlurMaskFilter

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    /* 底边 -常驻,颜色随时变化 */
    private fun drawBottomEdge(canvas: Canvas) {
        diffX = 9f
        val shapePoint = getShapePoint()
        // 重写图形
        path?.let {
            it.reset()
            shapePoint.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF[0], pointF[1])
                } else {
                    it.lineTo(pointF[0], pointF[1])
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = mColorBottomEdge
            paint.style = Paint.Style.FILL

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    /*金属片 - 常驻，颜色固定不变 */
    private fun drawSheetMetal(canvas: Canvas) {
        diffX = 5f
        val shapePoint = getShapePoint()
        // 重写图形
        path?.let {
            it.reset()
            shapePoint.forEachIndexed { index, pointF ->
                if (index == 0) {
                    it.moveTo(pointF[0], pointF[1])
                } else {
                    it.lineTo(pointF[0], pointF[1])
                }
            }
            it.close()

            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            paint.color = mColorSheetMetal
            paint.style = Paint.Style.FILL

            /*画路径*/
            canvas.drawPath(it, paint)
            it.reset()
        }
    }

    /*导光线 - 常驻，颜色随时变化  */
    private fun drawGuideLight(canvas: Canvas) {
        diffX = 9f
        val length = OUTSIDE_LINE_LENGTH - 4
        val centerPointF = PointF((width / 2).toFloat(), (height / 2).toFloat())// 中心点

        val matrix = Matrix()//旋转
        matrix.setRotate(offsetRotation + canvasRotation - 150f, centerPointF.x, centerPointF.y)

        val shapePoint = Array(6) { FloatArray(2) }

        for (i in 0..2) {
            shapePoint[i] =
                floatArrayOf(
                    centerPointF.x + (length * cos(2 * PI * i / 3)).toFloat(),
                    centerPointF.y + (length * sin(2 * PI * i / 3)).toFloat()
                )
        }


        val point0 = shapePoint[0]
        val point1 = shapePoint[2]
        val point2 = shapePoint[1]


        val a1 = PointF(centerPointF.x + diffX / sqrt(3f), centerPointF.y)
        val a2 = PointF(centerPointF.x - diffX / (2 * sqrt(3f)), centerPointF.y - diffX / 2)
        val a3 = PointF(centerPointF.x - diffX / (2 * sqrt(3f)), centerPointF.y + diffX / 2)

        shapePoint[0] = floatArrayOf(point0[0], point0[1])

        shapePoint[1] = floatArrayOf(a1.x, a1.y)

        shapePoint[2] = floatArrayOf(point1[0], point1[1])

        shapePoint[3] = floatArrayOf(a2.x, a2.y)

        shapePoint[4] = floatArrayOf(point2[0], point2[1])

        shapePoint[5] = floatArrayOf(a3.x, a3.y)


        shapePoint.forEach {
            matrix.mapPoints(it)
        }

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = mColorGuideLight
        paint.style = Paint.Style.FILL
        paint.strokeWidth = 2f

        if (isPreViewUI) {
            paint.color = preViewColors[0]
        }
        canvas.drawLine(
            shapePoint[0][0],
            shapePoint[0][1],
            shapePoint[1][0],
            shapePoint[1][1],
            paint
        )

        if (isPreViewUI) {
            paint.color = preViewColors[1]
        }
        canvas.drawLine(
            shapePoint[2][0],
            shapePoint[2][1],
            shapePoint[3][0],
            shapePoint[3][1],
            paint
        )

        if (isPreViewUI) {
            paint.color = preViewColors[2]
        }
        canvas.drawLine(
            shapePoint[4][0],
            shapePoint[4][1],
            shapePoint[5][0],
            shapePoint[5][1],
            paint
        )
    }
}