package com.govee.cubeview.shape

import com.govee.cubeview.log
import java.util.*

/**
 *     author  : sinrow
 *     time    : 2022/5/29
 *     version : 1.0.0
 *     desc    : 根据列表中 state 状态，反向构建树形图
 */
class ShapeTree3 {

    var root: Node? = null


    class Node(var id: Int, val shapePosition: ShapePosition) {
        var lastPosition: Int = 0
        var leftNode: Node? = null
        var rightNode: Node? = null
    }

    fun reset() {
        root = null
    }

    val visible = mutableListOf<Node>()

    fun insert(id: Int, shapePosition: ShapePosition) {
        showLog("开始插入--- id = $id")
        val newNode = Node(id, shapePosition)
        if (root == null) {
            root = newNode
            showLog("做为跟节点 newNode id = ${newNode.id} 上一个的 id = ${newNode.lastPosition}")
            return
        }
        val stack = Stack<Node>()
        stack.push(root)
        visible.clear()

        while (stack.isNotEmpty()) {

            val pop = stack.pop()

            when (pop.shapePosition.state) {
                0 -> {
                    // 走到尽头了
                    showLog("当前块没有 ${pop.id} ")
                }
                1 -> {
                    // 表示左边
                    if (pop.leftNode != null) {
                        stack.push(pop.leftNode)
                        showLog("只有左边 - 压栈 --- 左边")
                    } else {
                        showLog("只有左边 - newNode id = ${newNode.id}  插在 ${pop.id} 左边")
                        pop.leftNode = newNode
                        newNode.lastPosition = pop.id
                        stack.clear()
                    }
                }
                2 -> {
                    // 表示只有右边
                    if (pop.rightNode != null) {
                        showLog("只有右边 - 压栈 --- 右边")
                        stack.push(pop.rightNode)
                    } else {
                        showLog("只有右边 - newNode id = ${newNode.id} 插在 ${pop.id} 右边")
                        pop.rightNode = newNode
                        newNode.lastPosition = pop.id
                        stack.clear()
                    }
                }
                3 -> {
                    // 表示两条边都有
                    // 这里出问题了，左边插满，如何回右边呢

                    if (visible.contains(pop)) {
                        // 左边走完了，开始右边
                        if (pop.rightNode != null) {
                            stack.push(pop.rightNode)
                            showLog("两边都有 - 左边走完，走右边")
                        } else {
                            newNode.lastPosition = pop.id
                            pop.rightNode = newNode
                            showLog("两边都有 - 左边走完，右边没有插右边 id = ${newNode.id} 插在 ${pop.id} 右边")
                            stack.clear()
                        }
                    } else
                        if (pop.leftNode != null) {
                            stack.push(pop)
                            stack.push(pop.leftNode)
                            showLog("两边都有 - 先压当前 id = ${pop.id}，在压左边")
                            visible.add(pop)
                        } else {
                            newNode.lastPosition = pop.id
                            pop.leftNode = newNode
                            showLog("两边都有 - newNode id = ${newNode.id} 插在 ${pop.id} 左边")
                            stack.clear()
                        }
                }
            }
        }

    }

    fun findLastShape(id: Int): Int {
        if (root == null) {
            showLog("findLastShape --  空树")
            return id
        }
        val stack = Stack<Node>()
        stack.push(root)
        while (stack.isNotEmpty()) {
            val pop = stack.pop()
            if (pop.id == id) {
                showLog("findLastShape --  id = $id , 上一个的 id = ${pop.lastPosition}")
                return pop.lastPosition
            } else {
                if (pop.rightNode != null) {
                    stack.push(pop.rightNode)
                }
                if (pop.leftNode != null) {
                    stack.push(pop.leftNode)
                }
            }
        }
        showLog("findLastShape --  没找到")
        return id
    }

    fun ergodicMid(node: Node?) {
        if (node == null) {
            return
        }
        showLog("ergodicMid ${node.id}")
        ergodicMid(node.leftNode)
        ergodicMid(node.rightNode)
    }

    companion object {
        const val showLog = false
        private fun showLog(content: String) {
            val TAG = "shapeTree3"
            if (showLog) {
                log(TAG, content)
            }
        }
    }

}